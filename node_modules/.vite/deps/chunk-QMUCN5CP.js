import {
  useThemeProps
} from "./chunk-L4AXDUFP.js";
import {
  defaultTheme_default,
  identifier_default,
  init_defaultTheme,
  init_identifier
} from "./chunk-5BYOH2ZS.js";

// node_modules/@mui/material/styles/useThemeProps.js
init_defaultTheme();
init_identifier();
function useThemeProps2({
  props,
  name
}) {
  return useThemeProps({
    props,
    name,
    defaultTheme: defaultTheme_default,
    themeId: identifier_default
  });
}

export {
  useThemeProps2 as useThemeProps
};
//# sourceMappingURL=chunk-QMUCN5CP.js.map
