import {
  init_useForkRef,
  useForkRef
} from "./chunk-X4NELWXS.js";
import {
  __esm
} from "./chunk-V4OQ3NZ2.js";

// node_modules/@mui/material/utils/useForkRef.js
var useForkRef_default;
var init_useForkRef2 = __esm({
  "node_modules/@mui/material/utils/useForkRef.js"() {
    "use client";
    init_useForkRef();
    useForkRef_default = useForkRef;
  }
});

export {
  useForkRef_default,
  init_useForkRef2 as init_useForkRef
};
//# sourceMappingURL=chunk-E4DQEAQY.js.map
