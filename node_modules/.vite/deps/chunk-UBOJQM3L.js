import {
  require_react
} from "./chunk-QJTFJ6OV.js";
import {
  __esm,
  __toESM
} from "./chunk-V4OQ3NZ2.js";

// node_modules/@mui/utils/esm/getValidReactChildren/getValidReactChildren.js
function getValidReactChildren(children) {
  return React.Children.toArray(children).filter((child) => React.isValidElement(child));
}
var React;
var init_getValidReactChildren = __esm({
  "node_modules/@mui/utils/esm/getValidReactChildren/getValidReactChildren.js"() {
    React = __toESM(require_react());
  }
});

// node_modules/@mui/utils/esm/getValidReactChildren/index.js
var init_getValidReactChildren2 = __esm({
  "node_modules/@mui/utils/esm/getValidReactChildren/index.js"() {
    init_getValidReactChildren();
  }
});

export {
  getValidReactChildren,
  init_getValidReactChildren2 as init_getValidReactChildren
};
//# sourceMappingURL=chunk-UBOJQM3L.js.map
