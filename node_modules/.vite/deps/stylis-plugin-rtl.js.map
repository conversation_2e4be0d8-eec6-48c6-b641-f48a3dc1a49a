{"version": 3, "sources": ["../../cssjanus/src/cssjanus.js", "../../stylis-plugin-rtl/src/stylis-rtl.ts"], "sourcesContent": ["/*!\n * CSSJanus. https://www.mediawiki.org/wiki/CSSJanus\n *\n * Copyright 2014 <PERSON>\n * Copyright 2010 <PERSON><PERSON>\n * Copyright 2008 Google Inc.\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n * http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nvar cssjanus;\n\n/**\n * Create a tokenizer object.\n *\n * This utility class is used by CSSJanus to protect strings by replacing them temporarily with\n * tokens and later transforming them back.\n *\n * @class\n * @constructor\n * @param {RegExp} regex Regular expression whose matches to replace by a token\n * @param {string} token Placeholder text\n */\nfunction Tokenizer( regex, token ) {\n\n\tvar matches = [],\n\t\tindex = 0;\n\n\t/**\n\t * Add a match.\n\t *\n\t * @private\n\t * @param {string} match Matched string\n\t * @return {string} Token to leave in the matched string's place\n\t */\n\tfunction tokenizeCallback( match ) {\n\t\tmatches.push( match );\n\t\treturn token;\n\t}\n\n\t/**\n\t * Get a match.\n\t *\n\t * @private\n\t * @return {string} Original matched string to restore\n\t */\n\tfunction detokenizeCallback() {\n\t\treturn matches[ index++ ];\n\t}\n\n\treturn {\n\t\t/**\n\t\t * Replace matching strings with tokens.\n\t\t *\n\t\t * @param {string} str String to tokenize\n\t\t * @return {string} Tokenized string\n\t\t */\n\t\ttokenize: function ( str ) {\n\t\t\treturn str.replace( regex, tokenizeCallback );\n\t\t},\n\n\t\t/**\n\t\t * Restores tokens to their original values.\n\t\t *\n\t\t * @param {string} str String previously run through tokenize()\n\t\t * @return {string} Original string\n\t\t */\n\t\tdetokenize: function ( str ) {\n\t\t\treturn str.replace( new RegExp( '(' + token + ')', 'g' ), detokenizeCallback );\n\t\t}\n\t};\n}\n\n/**\n * Create a CSSJanus object.\n *\n * CSSJanus transforms CSS rules with horizontal relevance so that a left-to-right stylesheet can\n * become a right-to-left stylesheet automatically. Processing can be bypassed for an entire rule\n * or a single property by adding a / * @noflip * / comment above the rule or property.\n *\n * @class\n * @constructor\n */\nfunction CSSJanus() {\n\n\tvar\n\t\t// Tokens\n\t\ttemporaryToken = '`TMP`',\n\t\ttemporaryLtrToken = '`TMPLTR`',\n\t\ttemporaryRtlToken = '`TMPRTL`',\n\t\tnoFlipSingleToken = '`NOFLIP_SINGLE`',\n\t\tnoFlipClassToken = '`NOFLIP_CLASS`',\n\t\tcommentToken = '`COMMENT`',\n\t\t// Patterns\n\t\tnonAsciiPattern = '[^\\\\u0020-\\\\u007e]',\n\t\tunicodePattern = '(?:(?:\\\\\\\\[0-9a-f]{1,6})(?:\\\\r\\\\n|\\\\s)?)',\n\t\tnumPattern = '(?:[0-9]*\\\\.[0-9]+|[0-9]+)',\n\t\tunitPattern = '(?:em|ex|px|cm|mm|in|pt|pc|deg|rad|grad|ms|s|hz|khz|%)',\n\t\tdirectionPattern = 'direction\\\\s*:\\\\s*',\n\t\turlSpecialCharsPattern = '[!#$%&*-~]',\n\t\tvalidAfterUriCharsPattern = '[\\'\"]?\\\\s*',\n\t\tnonLetterPattern = '(^|[^a-zA-Z])',\n\t\tcharsWithinSelectorPattern = '[^\\\\}]*?',\n\t\tnoFlipPattern = '\\\\/\\\\*\\\\!?\\\\s*@noflip\\\\s*\\\\*\\\\/',\n\t\tcommentPattern = '\\\\/\\\\*[^*]*\\\\*+([^\\\\/*][^*]*\\\\*+)*\\\\/',\n\t\tescapePattern = '(?:' + unicodePattern + '|\\\\\\\\[^\\\\r\\\\n\\\\f0-9a-f])',\n\t\tnmstartPattern = '(?:[_a-z]|' + nonAsciiPattern + '|' + escapePattern + ')',\n\t\tnmcharPattern = '(?:[_a-z0-9-]|' + nonAsciiPattern + '|' + escapePattern + ')',\n\t\tidentPattern = '-?' + nmstartPattern + nmcharPattern + '*',\n\t\tquantPattern = numPattern + '(?:\\\\s*' + unitPattern + '|' + identPattern + ')?',\n\t\tsignedQuantPattern = '((?:-?' + quantPattern + ')|(?:inherit|auto))',\n\t\tsignedQuantSimplePattern = '(?:-?' + numPattern + '(?:\\\\s*' + unitPattern + ')?)',\n\t\tmathOperatorsPattern = '(?:\\\\+|\\\\-|\\\\*|\\\\/)',\n\t\tallowedCharsPattern = '(?:\\\\(|\\\\)|\\\\t| )',\n\t\tcalcEquationPattern = '(?:' + allowedCharsPattern + '|' + signedQuantSimplePattern + '|' + mathOperatorsPattern + '){3,}',\n\t\tcalcPattern = '(?:calc\\\\((?:' + calcEquationPattern + ')\\\\))',\n\t\tsignedQuantCalcPattern = '((?:-?' + quantPattern + ')|(?:inherit|auto)|' + calcPattern + ')',\n\t\tfourNotationQuantPropsPattern = '((?:margin|padding|border-width)\\\\s*:\\\\s*)',\n\t\tfourNotationColorPropsPattern = '((?:-color|border-style)\\\\s*:\\\\s*)',\n\t\tcolorPattern = '(#?' + nmcharPattern + '+|(?:rgba?|hsla?)\\\\([ \\\\d.,%-]+\\\\))',\n\t\t// The use of a lazy match (\"*?\") may cause a backtrack limit to be exceeded before finding\n\t\t// the intended match. This affects 'urlCharsPattern' and 'lookAheadNotOpenBracePattern'.\n\t\t// We have not yet found this problem on Node.js, but we have on PHP 7, where it was\n\t\t// mitigated by using a possessive quantifier (\"*+\"), which are not supported in JS.\n\t\t// See <https://phabricator.wikimedia.org/T215746#4944830>.\n\t\turlCharsPattern = '(?:' + urlSpecialCharsPattern + '|' + nonAsciiPattern + '|' + escapePattern + ')*?',\n\t\tlookAheadNotLetterPattern = '(?![a-zA-Z])',\n\t\tlookAheadNotOpenBracePattern = '(?!(' + nmcharPattern + '|\\\\r?\\\\n|\\\\s|#|\\\\:|\\\\.|\\\\,|\\\\+|>|~|\\\\(|\\\\)|\\\\[|\\\\]|=|\\\\*=|~=|\\\\^=|\\'[^\\']*\\'|\"[^\"]*\"|' + commentToken + ')*?{)',\n\t\tlookAheadNotClosingParenPattern = '(?!' + urlCharsPattern + validAfterUriCharsPattern + '\\\\))',\n\t\tlookAheadForClosingParenPattern = '(?=' + urlCharsPattern + validAfterUriCharsPattern + '\\\\))',\n\t\tsuffixPattern = '(\\\\s*(?:!important\\\\s*)?[;}])',\n\t\t// Regular expressions\n\t\ttemporaryTokenRegExp = /`TMP`/g,\n\t\ttemporaryLtrTokenRegExp = /`TMPLTR`/g,\n\t\ttemporaryRtlTokenRegExp = /`TMPRTL`/g,\n\t\tcommentRegExp = new RegExp( commentPattern, 'gi' ),\n\t\tnoFlipSingleRegExp = new RegExp( '(' + noFlipPattern + lookAheadNotOpenBracePattern + '[^;}]+;?)', 'gi' ),\n\t\tnoFlipClassRegExp = new RegExp( '(' + noFlipPattern + charsWithinSelectorPattern + '})', 'gi' ),\n\t\tdirectionLtrRegExp = new RegExp( '(' + directionPattern + ')ltr', 'gi' ),\n\t\tdirectionRtlRegExp = new RegExp( '(' + directionPattern + ')rtl', 'gi' ),\n\t\tleftRegExp = new RegExp( nonLetterPattern + '(left)' + lookAheadNotLetterPattern + lookAheadNotClosingParenPattern + lookAheadNotOpenBracePattern, 'gi' ),\n\t\trightRegExp = new RegExp( nonLetterPattern + '(right)' + lookAheadNotLetterPattern + lookAheadNotClosingParenPattern + lookAheadNotOpenBracePattern, 'gi' ),\n\t\tleftInUrlRegExp = new RegExp( nonLetterPattern + '(left)' + lookAheadForClosingParenPattern, 'gi' ),\n\t\trightInUrlRegExp = new RegExp( nonLetterPattern + '(right)' + lookAheadForClosingParenPattern, 'gi' ),\n\t\tltrDirSelector = /(:dir\\( *)ltr( *\\))/g,\n\t\trtlDirSelector = /(:dir\\( *)rtl( *\\))/g,\n\t\tltrInUrlRegExp = new RegExp( nonLetterPattern + '(ltr)' + lookAheadForClosingParenPattern, 'gi' ),\n\t\trtlInUrlRegExp = new RegExp( nonLetterPattern + '(rtl)' + lookAheadForClosingParenPattern, 'gi' ),\n\t\tcursorEastRegExp = new RegExp( nonLetterPattern + '([ns]?)e-resize', 'gi' ),\n\t\tcursorWestRegExp = new RegExp( nonLetterPattern + '([ns]?)w-resize', 'gi' ),\n\t\tfourNotationQuantRegExp = new RegExp( fourNotationQuantPropsPattern + signedQuantCalcPattern + '(\\\\s+)' + signedQuantCalcPattern + '(\\\\s+)' + signedQuantCalcPattern + '(\\\\s+)' + signedQuantCalcPattern + suffixPattern, 'gi' ),\n\t\tfourNotationColorRegExp = new RegExp( fourNotationColorPropsPattern + colorPattern + '(\\\\s+)' + colorPattern + '(\\\\s+)' + colorPattern + '(\\\\s+)' + colorPattern + suffixPattern, 'gi' ),\n\t\tbgHorizontalPercentageRegExp = new RegExp( '(background(?:-position)?\\\\s*:\\\\s*(?:[^:;}\\\\s]+\\\\s+)*?)(' + quantPattern + ')', 'gi' ),\n\t\tbgHorizontalPercentageXRegExp = new RegExp( '(background-position-x\\\\s*:\\\\s*)(-?' + numPattern + '%)', 'gi' ),\n\t\t// border-radius: <length or percentage>{1,4} [optional: / <length or percentage>{1,4} ]\n\t\tborderRadiusRegExp = new RegExp( '(border-radius\\\\s*:\\\\s*)' + signedQuantPattern + '(?:(?:\\\\s+' + signedQuantPattern + ')(?:\\\\s+' + signedQuantPattern + ')?(?:\\\\s+' + signedQuantPattern + ')?)?' +\n\t\t\t'(?:(?:(?:\\\\s*\\\\/\\\\s*)' + signedQuantPattern + ')(?:\\\\s+' + signedQuantPattern + ')?(?:\\\\s+' + signedQuantPattern + ')?(?:\\\\s+' + signedQuantPattern + ')?)?' + suffixPattern, 'gi' ),\n\t\tboxShadowRegExp = new RegExp( '(box-shadow\\\\s*:\\\\s*(?:inset\\\\s*)?)' + signedQuantPattern, 'gi' ),\n\t\ttextShadow1RegExp = new RegExp( '(text-shadow\\\\s*:\\\\s*)' + signedQuantPattern + '(\\\\s*)' + colorPattern, 'gi' ),\n\t\ttextShadow2RegExp = new RegExp( '(text-shadow\\\\s*:\\\\s*)' + colorPattern + '(\\\\s*)' + signedQuantPattern, 'gi' ),\n\t\ttextShadow3RegExp = new RegExp( '(text-shadow\\\\s*:\\\\s*)' + signedQuantPattern, 'gi' ),\n\t\ttranslateXRegExp = new RegExp( '(transform\\\\s*:[^;}]*)(translateX\\\\s*\\\\(\\\\s*)' + signedQuantPattern + '(\\\\s*\\\\))', 'gi' ),\n\t\ttranslateRegExp = new RegExp( '(transform\\\\s*:[^;}]*)(translate\\\\s*\\\\(\\\\s*)' + signedQuantPattern + '((?:\\\\s*,\\\\s*' + signedQuantPattern + '){0,2}\\\\s*\\\\))', 'gi' );\n\n\t/**\n\t * Invert the horizontal value of a background position property.\n\t *\n\t * @private\n\t * @param {string} match Matched property\n\t * @param {string} pre Text before value\n\t * @param {string} value Horizontal value\n\t * @return {string} Inverted property\n\t */\n\tfunction calculateNewBackgroundPosition( match, pre, value ) {\n\t\tvar idx, len;\n\t\tif ( value.slice( -1 ) === '%' ) {\n\t\t\tidx = value.indexOf( '.' );\n\t\t\tif ( idx !== -1 ) {\n\t\t\t\t// Two off, one for the \"%\" at the end, one for the dot itself\n\t\t\t\tlen = value.length - idx - 2;\n\t\t\t\tvalue = 100 - parseFloat( value );\n\t\t\t\tvalue = value.toFixed( len ) + '%';\n\t\t\t} else {\n\t\t\t\tvalue = 100 - parseFloat( value ) + '%';\n\t\t\t}\n\t\t}\n\t\treturn pre + value;\n\t}\n\n\t/**\n\t * Invert a set of border radius values.\n\t *\n\t * @private\n\t * @param {Array} values Matched values\n\t * @return {string} Inverted values\n\t */\n\tfunction flipBorderRadiusValues( values ) {\n\t\tswitch ( values.length ) {\n\t\t\tcase 4:\n\t\t\t\tvalues = [ values[ 1 ], values[ 0 ], values[ 3 ], values[ 2 ] ];\n\t\t\t\tbreak;\n\t\t\tcase 3:\n\t\t\t\tvalues = [ values[ 1 ], values[ 0 ], values[ 1 ], values[ 2 ] ];\n\t\t\t\tbreak;\n\t\t\tcase 2:\n\t\t\t\tvalues = [ values[ 1 ], values[ 0 ] ];\n\t\t\t\tbreak;\n\t\t\tcase 1:\n\t\t\t\tvalues = [ values[ 0 ] ];\n\t\t\t\tbreak;\n\t\t}\n\n\t\treturn values.join( ' ' );\n\t}\n\n\t/**\n\t * Invert a set of border radius values.\n\t *\n\t * @private\n\t * @param {string} match Matched property\n\t * @param {string} pre Text before value\n\t * @param {string} [firstGroup1]\n\t * @param {string} [firstGroup2]\n\t * @param {string} [firstGroup3]\n\t * @param {string} [firstGroup4]\n\t * @param {string} [secondGroup1]\n\t * @param {string} [secondGroup2]\n\t * @param {string} [secondGroup3]\n\t * @param {string} [secondGroup4]\n\t * @param {string} [post] Text after value\n\t * @return {string} Inverted property\n\t */\n\tfunction calculateNewBorderRadius( match, pre ) {\n\t\tvar values,\n\t\t\targs = [].slice.call( arguments ),\n\t\t\tfirstGroup = args.slice( 2, 6 ).filter( function ( val ) {\n\t\t\t\treturn val;\n\t\t\t} ),\n\t\t\tsecondGroup = args.slice( 6, 10 ).filter( function ( val ) {\n\t\t\t\treturn val;\n\t\t\t} ),\n\t\t\tpost = args[ 10 ] || '';\n\n\t\tif ( secondGroup.length ) {\n\t\t\tvalues = flipBorderRadiusValues( firstGroup ) + ' / ' + flipBorderRadiusValues( secondGroup );\n\t\t} else {\n\t\t\tvalues = flipBorderRadiusValues( firstGroup );\n\t\t}\n\n\t\treturn pre + values + post;\n\t}\n\n\t/**\n\t * Flip the sign of a CSS value, possibly with a unit.\n\t *\n\t * We can't just negate the value with unary minus due to the units.\n\t *\n\t * @private\n\t * @param {string} value\n\t * @return {string}\n\t */\n\tfunction flipSign( value ) {\n\t\tif ( parseFloat( value ) === 0 ) {\n\t\t\t// Don't mangle zeroes\n\t\t\treturn value;\n\t\t}\n\n\t\tif ( value[ 0 ] === '-' ) {\n\t\t\treturn value.slice( 1 );\n\t\t}\n\n\t\treturn '-' + value;\n\t}\n\n\t/**\n\t * @private\n\t * @param {string} match\n\t * @param {string} property\n\t * @param {string} offset\n\t * @return {string}\n\t */\n\tfunction calculateNewShadow( match, property, offset ) {\n\t\treturn property + flipSign( offset );\n\t}\n\n\t/**\n\t * @private\n\t * @param {string} match\n\t * @param {string} property\n\t * @param {string} prefix\n\t * @param {string} offset\n\t * @param {string} suffix\n\t * @return {string}\n\t */\n\tfunction calculateNewTranslate( match, property, prefix, offset, suffix ) {\n\t\treturn property + prefix + flipSign( offset ) + suffix;\n\t}\n\n\t/**\n\t * @private\n\t * @param {string} match\n\t * @param {string} property\n\t * @param {string} color\n\t * @param {string} space\n\t * @param {string} offset\n\t * @return {string}\n\t */\n\tfunction calculateNewFourTextShadow( match, property, color, space, offset ) {\n\t\treturn property + color + space + flipSign( offset );\n\t}\n\n\treturn {\n\t\t/**\n\t\t * Transform a left-to-right stylesheet to right-to-left.\n\t\t *\n\t\t * @param {string} css Stylesheet to transform\n\t\t * @param {Object} options Options\n\t\t * @param {boolean} [options.transformDirInUrl=false] Transform directions in URLs\n\t\t * (e.g. 'ltr', 'rtl')\n\t\t * @param {boolean} [options.transformEdgeInUrl=false] Transform edges in URLs\n\t\t * (e.g. 'left', 'right')\n\t\t * @return {string} Transformed stylesheet\n\t\t */\n\t\t'transform': function ( css, options ) { // eslint-disable-line quote-props\n\t\t\t// Use single quotes in this object literal key for closure compiler.\n\t\t\t// Tokenizers\n\t\t\tvar noFlipSingleTokenizer = new Tokenizer( noFlipSingleRegExp, noFlipSingleToken ),\n\t\t\t\tnoFlipClassTokenizer = new Tokenizer( noFlipClassRegExp, noFlipClassToken ),\n\t\t\t\tcommentTokenizer = new Tokenizer( commentRegExp, commentToken );\n\n\t\t\t// Tokenize\n\t\t\tcss = commentTokenizer.tokenize(\n\t\t\t\tnoFlipClassTokenizer.tokenize(\n\t\t\t\t\tnoFlipSingleTokenizer.tokenize(\n\t\t\t\t\t\t// We wrap tokens in ` , not ~ like the original implementation does.\n\t\t\t\t\t\t// This was done because ` is not a legal character in CSS and can only\n\t\t\t\t\t\t// occur in URLs, where we escape it to %60 before inserting our tokens.\n\t\t\t\t\t\tcss.replace( '`', '%60' )\n\t\t\t\t\t)\n\t\t\t\t)\n\t\t\t);\n\n\t\t\t// Transform URLs\n\t\t\tif ( options.transformDirInUrl ) {\n\t\t\t\t// Replace 'ltr' with 'rtl' and vice versa in background URLs\n\t\t\t\tcss = css\n\t\t\t\t\t.replace( ltrDirSelector, '$1' + temporaryLtrToken + '$2' )\n\t\t\t\t\t.replace( rtlDirSelector, '$1' + temporaryRtlToken + '$2' )\n\t\t\t\t\t.replace( ltrInUrlRegExp, '$1' + temporaryToken )\n\t\t\t\t\t.replace( rtlInUrlRegExp, '$1ltr' )\n\t\t\t\t\t.replace( temporaryTokenRegExp, 'rtl' )\n\t\t\t\t\t.replace( temporaryLtrTokenRegExp, 'ltr' )\n\t\t\t\t\t.replace( temporaryRtlTokenRegExp, 'rtl' );\n\t\t\t}\n\t\t\tif ( options.transformEdgeInUrl ) {\n\t\t\t\t// Replace 'left' with 'right' and vice versa in background URLs\n\t\t\t\tcss = css\n\t\t\t\t\t.replace( leftInUrlRegExp, '$1' + temporaryToken )\n\t\t\t\t\t.replace( rightInUrlRegExp, '$1left' )\n\t\t\t\t\t.replace( temporaryTokenRegExp, 'right' );\n\t\t\t}\n\n\t\t\t// Transform rules\n\t\t\tcss = css\n\t\t\t\t// Replace direction: ltr; with direction: rtl; and vice versa.\n\t\t\t\t.replace( directionLtrRegExp, '$1' + temporaryToken )\n\t\t\t\t.replace( directionRtlRegExp, '$1ltr' )\n\t\t\t\t.replace( temporaryTokenRegExp, 'rtl' )\n\t\t\t\t// Flip rules like left: , padding-right: , etc.\n\t\t\t\t.replace( leftRegExp, '$1' + temporaryToken )\n\t\t\t\t.replace( rightRegExp, '$1left' )\n\t\t\t\t.replace( temporaryTokenRegExp, 'right' )\n\t\t\t\t// Flip East and West in rules like cursor: nw-resize;\n\t\t\t\t.replace( cursorEastRegExp, '$1$2' + temporaryToken )\n\t\t\t\t.replace( cursorWestRegExp, '$1$2e-resize' )\n\t\t\t\t.replace( temporaryTokenRegExp, 'w-resize' )\n\t\t\t\t// Border radius\n\t\t\t\t.replace( borderRadiusRegExp, calculateNewBorderRadius )\n\t\t\t\t// Shadows\n\t\t\t\t.replace( boxShadowRegExp, calculateNewShadow )\n\t\t\t\t.replace( textShadow1RegExp, calculateNewFourTextShadow )\n\t\t\t\t.replace( textShadow2RegExp, calculateNewFourTextShadow )\n\t\t\t\t.replace( textShadow3RegExp, calculateNewShadow )\n\t\t\t\t// Translate\n\t\t\t\t.replace( translateXRegExp, calculateNewTranslate )\n\t\t\t\t.replace( translateRegExp, calculateNewTranslate )\n\t\t\t\t// Swap the second and fourth parts in four-part notation rules\n\t\t\t\t// like padding: 1px 2px 3px 4px;\n\t\t\t\t.replace( fourNotationQuantRegExp, '$1$2$3$8$5$6$7$4$9' )\n\t\t\t\t.replace( fourNotationColorRegExp, '$1$2$3$8$5$6$7$4$9' )\n\t\t\t\t// Flip horizontal background percentages\n\t\t\t\t.replace( bgHorizontalPercentageRegExp, calculateNewBackgroundPosition )\n\t\t\t\t.replace( bgHorizontalPercentageXRegExp, calculateNewBackgroundPosition );\n\n\t\t\t// Detokenize\n\t\t\tcss = noFlipSingleTokenizer.detokenize(\n\t\t\t\tnoFlipClassTokenizer.detokenize(\n\t\t\t\t\tcommentTokenizer.detokenize( css )\n\t\t\t\t)\n\t\t\t);\n\n\t\t\treturn css;\n\t\t}\n\t};\n}\n\n/* Initialization */\n\ncssjanus = new CSSJanus();\n\n/* Exports */\n\nif ( typeof module !== 'undefined' && module.exports ) {\n\t/**\n\t * Transform a left-to-right stylesheet to right-to-left.\n\t *\n\t * This function is a static wrapper around the transform method of an instance of CSSJanus.\n\t *\n\t * @param {string} css Stylesheet to transform\n\t * @param {Object|boolean} [options] Options object, or transformDirInUrl option (back-compat)\n\t * @param {boolean} [options.transformDirInUrl=false] Transform directions in URLs\n\t * (e.g. 'ltr', 'rtl')\n\t * @param {boolean} [options.transformEdgeInUrl=false] Transform edges in URLs\n\t * (e.g. 'left', 'right')\n\t * @param {boolean} [transformEdgeInUrl] Back-compat parameter\n\t * @return {string} Transformed stylesheet\n\t */\n\texports.transform = function ( css, options, transformEdgeInUrl ) {\n\t\tvar norm;\n\t\tif ( typeof options === 'object' ) {\n\t\t\tnorm = options;\n\t\t} else {\n\t\t\tnorm = {};\n\t\t\tif ( typeof options === 'boolean' ) {\n\t\t\t\tnorm.transformDirInUrl = options;\n\t\t\t}\n\t\t\tif ( typeof transformEdgeInUrl === 'boolean' ) {\n\t\t\t\tnorm.transformEdgeInUrl = transformEdgeInUrl;\n\t\t\t}\n\t\t}\n\t\treturn cssjanus.transform( css, norm );\n\t};\n} else if ( typeof window !== 'undefined' ) {\n\t/* global window */\n\t// Allow cssjanus to be used in a browser.\n\t// eslint-disable-next-line dot-notation\n\twindow[ 'cssjanus' ] = cssjanus;\n}\n", null], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAoBA,QAAIA;AAaJ,aAAS,UAAW,OAAO,OAAQ;AAElC,UAAI,UAAU,CAAC,GACd,QAAQ;AAST,eAAS,iBAAkB,OAAQ;AAClC,gBAAQ,KAAM,KAAM;AACpB,eAAO;AAAA,MACR;AAQA,eAAS,qBAAqB;AAC7B,eAAO,QAAS,OAAQ;AAAA,MACzB;AAEA,aAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAON,UAAU,SAAW,KAAM;AAC1B,iBAAO,IAAI,QAAS,OAAO,gBAAiB;AAAA,QAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQA,YAAY,SAAW,KAAM;AAC5B,iBAAO,IAAI,QAAS,IAAI,OAAQ,MAAM,QAAQ,KAAK,GAAI,GAAG,kBAAmB;AAAA,QAC9E;AAAA,MACD;AAAA,IACD;AAYA,aAAS,WAAW;AAEnB,UAEC,iBAAiB,SACjB,oBAAoB,YACpB,oBAAoB,YACpB,oBAAoB,mBACpB,mBAAmB,kBACnB,eAAe,aAEf,kBAAkB,sBAClB,iBAAiB,4CACjB,aAAa,8BACb,cAAc,0DACd,mBAAmB,sBACnB,yBAAyB,cACzB,4BAA4B,aAC5B,mBAAmB,iBACnB,6BAA6B,YAC7B,gBAAgB,mCAChB,iBAAiB,yCACjB,gBAAgB,QAAQ,iBAAiB,4BACzC,iBAAiB,eAAe,kBAAkB,MAAM,gBAAgB,KACxE,gBAAgB,mBAAmB,kBAAkB,MAAM,gBAAgB,KAC3E,eAAe,OAAO,iBAAiB,gBAAgB,KACvD,eAAe,aAAa,YAAY,cAAc,MAAM,eAAe,MAC3E,qBAAqB,WAAW,eAAe,uBAC/C,2BAA2B,UAAU,aAAa,YAAY,cAAc,OAC5E,uBAAuB,uBACvB,sBAAsB,qBACtB,sBAAsB,QAAQ,sBAAsB,MAAM,2BAA2B,MAAM,uBAAuB,SAClH,cAAc,kBAAkB,sBAAsB,SACtD,yBAAyB,WAAW,eAAe,wBAAwB,cAAc,KACzF,gCAAgC,8CAChC,gCAAgC,sCAChC,eAAe,QAAQ,gBAAgB,uCAMvC,kBAAkB,QAAQ,yBAAyB,MAAM,kBAAkB,MAAM,gBAAgB,OACjG,4BAA4B,gBAC5B,+BAA+B,SAAS,gBAAgB,uFAA0F,eAAe,SACjK,kCAAkC,QAAQ,kBAAkB,4BAA4B,QACxF,kCAAkC,QAAQ,kBAAkB,4BAA4B,QACxF,gBAAgB,iCAEhB,uBAAuB,UACvB,0BAA0B,aAC1B,0BAA0B,aAC1B,gBAAgB,IAAI,OAAQ,gBAAgB,IAAK,GACjD,qBAAqB,IAAI,OAAQ,MAAM,gBAAgB,+BAA+B,aAAa,IAAK,GACxG,oBAAoB,IAAI,OAAQ,MAAM,gBAAgB,6BAA6B,MAAM,IAAK,GAC9F,qBAAqB,IAAI,OAAQ,MAAM,mBAAmB,QAAQ,IAAK,GACvE,qBAAqB,IAAI,OAAQ,MAAM,mBAAmB,QAAQ,IAAK,GACvE,aAAa,IAAI,OAAQ,mBAAmB,WAAW,4BAA4B,kCAAkC,8BAA8B,IAAK,GACxJ,cAAc,IAAI,OAAQ,mBAAmB,YAAY,4BAA4B,kCAAkC,8BAA8B,IAAK,GAC1J,kBAAkB,IAAI,OAAQ,mBAAmB,WAAW,iCAAiC,IAAK,GAClG,mBAAmB,IAAI,OAAQ,mBAAmB,YAAY,iCAAiC,IAAK,GACpG,iBAAiB,wBACjB,iBAAiB,wBACjB,iBAAiB,IAAI,OAAQ,mBAAmB,UAAU,iCAAiC,IAAK,GAChG,iBAAiB,IAAI,OAAQ,mBAAmB,UAAU,iCAAiC,IAAK,GAChG,mBAAmB,IAAI,OAAQ,mBAAmB,mBAAmB,IAAK,GAC1E,mBAAmB,IAAI,OAAQ,mBAAmB,mBAAmB,IAAK,GAC1E,0BAA0B,IAAI,OAAQ,gCAAgC,yBAAyB,WAAW,yBAAyB,WAAW,yBAAyB,WAAW,yBAAyB,eAAe,IAAK,GAC/N,0BAA0B,IAAI,OAAQ,gCAAgC,eAAe,WAAW,eAAe,WAAW,eAAe,WAAW,eAAe,eAAe,IAAK,GACvL,+BAA+B,IAAI,OAAQ,6DAA6D,eAAe,KAAK,IAAK,GACjI,gCAAgC,IAAI,OAAQ,wCAAwC,aAAa,MAAM,IAAK,GAE5G,qBAAqB,IAAI,OAAQ,6BAA6B,qBAAqB,eAAe,qBAAqB,aAAa,qBAAqB,cAAc,qBAAqB,8BACjK,qBAAqB,aAAa,qBAAqB,cAAc,qBAAqB,cAAc,qBAAqB,SAAS,eAAe,IAAK,GACrL,kBAAkB,IAAI,OAAQ,wCAAwC,oBAAoB,IAAK,GAC/F,oBAAoB,IAAI,OAAQ,2BAA2B,qBAAqB,WAAW,cAAc,IAAK,GAC9G,oBAAoB,IAAI,OAAQ,2BAA2B,eAAe,WAAW,oBAAoB,IAAK,GAC9G,oBAAoB,IAAI,OAAQ,2BAA2B,oBAAoB,IAAK,GACpF,mBAAmB,IAAI,OAAQ,kDAAkD,qBAAqB,aAAa,IAAK,GACxH,kBAAkB,IAAI,OAAQ,iDAAiD,qBAAqB,kBAAkB,qBAAqB,kBAAkB,IAAK;AAWnK,eAAS,+BAAgC,OAAO,KAAK,OAAQ;AAC5D,YAAI,KAAK;AACT,YAAK,MAAM,MAAO,EAAG,MAAM,KAAM;AAChC,gBAAM,MAAM,QAAS,GAAI;AACzB,cAAK,QAAQ,IAAK;AAEjB,kBAAM,MAAM,SAAS,MAAM;AAC3B,oBAAQ,MAAM,WAAY,KAAM;AAChC,oBAAQ,MAAM,QAAS,GAAI,IAAI;AAAA,UAChC,OAAO;AACN,oBAAQ,MAAM,WAAY,KAAM,IAAI;AAAA,UACrC;AAAA,QACD;AACA,eAAO,MAAM;AAAA,MACd;AASA,eAAS,uBAAwB,QAAS;AACzC,gBAAS,OAAO,QAAS;AAAA,UACxB,KAAK;AACJ,qBAAS,CAAE,OAAQ,CAAE,GAAG,OAAQ,CAAE,GAAG,OAAQ,CAAE,GAAG,OAAQ,CAAE,CAAE;AAC9D;AAAA,UACD,KAAK;AACJ,qBAAS,CAAE,OAAQ,CAAE,GAAG,OAAQ,CAAE,GAAG,OAAQ,CAAE,GAAG,OAAQ,CAAE,CAAE;AAC9D;AAAA,UACD,KAAK;AACJ,qBAAS,CAAE,OAAQ,CAAE,GAAG,OAAQ,CAAE,CAAE;AACpC;AAAA,UACD,KAAK;AACJ,qBAAS,CAAE,OAAQ,CAAE,CAAE;AACvB;AAAA,QACF;AAEA,eAAO,OAAO,KAAM,GAAI;AAAA,MACzB;AAmBA,eAAS,yBAA0B,OAAO,KAAM;AAC/C,YAAI,QACH,OAAO,CAAC,EAAE,MAAM,KAAM,SAAU,GAChC,aAAa,KAAK,MAAO,GAAG,CAAE,EAAE,OAAQ,SAAW,KAAM;AACxD,iBAAO;AAAA,QACR,CAAE,GACF,cAAc,KAAK,MAAO,GAAG,EAAG,EAAE,OAAQ,SAAW,KAAM;AAC1D,iBAAO;AAAA,QACR,CAAE,GACF,OAAO,KAAM,EAAG,KAAK;AAEtB,YAAK,YAAY,QAAS;AACzB,mBAAS,uBAAwB,UAAW,IAAI,QAAQ,uBAAwB,WAAY;AAAA,QAC7F,OAAO;AACN,mBAAS,uBAAwB,UAAW;AAAA,QAC7C;AAEA,eAAO,MAAM,SAAS;AAAA,MACvB;AAWA,eAAS,SAAU,OAAQ;AAC1B,YAAK,WAAY,KAAM,MAAM,GAAI;AAEhC,iBAAO;AAAA,QACR;AAEA,YAAK,MAAO,CAAE,MAAM,KAAM;AACzB,iBAAO,MAAM,MAAO,CAAE;AAAA,QACvB;AAEA,eAAO,MAAM;AAAA,MACd;AASA,eAAS,mBAAoB,OAAO,UAAU,QAAS;AACtD,eAAO,WAAW,SAAU,MAAO;AAAA,MACpC;AAWA,eAAS,sBAAuB,OAAO,UAAU,QAAQ,QAAQ,QAAS;AACzE,eAAO,WAAW,SAAS,SAAU,MAAO,IAAI;AAAA,MACjD;AAWA,eAAS,2BAA4B,OAAO,UAAU,OAAO,OAAO,QAAS;AAC5E,eAAO,WAAW,QAAQ,QAAQ,SAAU,MAAO;AAAA,MACpD;AAEA,aAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAYN,aAAa,SAAW,KAAK,SAAU;AAGtC,cAAI,wBAAwB,IAAI,UAAW,oBAAoB,iBAAkB,GAChF,uBAAuB,IAAI,UAAW,mBAAmB,gBAAiB,GAC1E,mBAAmB,IAAI,UAAW,eAAe,YAAa;AAG/D,gBAAM,iBAAiB;AAAA,YACtB,qBAAqB;AAAA,cACpB,sBAAsB;AAAA;AAAA;AAAA;AAAA,gBAIrB,IAAI,QAAS,KAAK,KAAM;AAAA,cACzB;AAAA,YACD;AAAA,UACD;AAGA,cAAK,QAAQ,mBAAoB;AAEhC,kBAAM,IACJ,QAAS,gBAAgB,OAAO,oBAAoB,IAAK,EACzD,QAAS,gBAAgB,OAAO,oBAAoB,IAAK,EACzD,QAAS,gBAAgB,OAAO,cAAe,EAC/C,QAAS,gBAAgB,OAAQ,EACjC,QAAS,sBAAsB,KAAM,EACrC,QAAS,yBAAyB,KAAM,EACxC,QAAS,yBAAyB,KAAM;AAAA,UAC3C;AACA,cAAK,QAAQ,oBAAqB;AAEjC,kBAAM,IACJ,QAAS,iBAAiB,OAAO,cAAe,EAChD,QAAS,kBAAkB,QAAS,EACpC,QAAS,sBAAsB,OAAQ;AAAA,UAC1C;AAGA,gBAAM,IAEJ,QAAS,oBAAoB,OAAO,cAAe,EACnD,QAAS,oBAAoB,OAAQ,EACrC,QAAS,sBAAsB,KAAM,EAErC,QAAS,YAAY,OAAO,cAAe,EAC3C,QAAS,aAAa,QAAS,EAC/B,QAAS,sBAAsB,OAAQ,EAEvC,QAAS,kBAAkB,SAAS,cAAe,EACnD,QAAS,kBAAkB,cAAe,EAC1C,QAAS,sBAAsB,UAAW,EAE1C,QAAS,oBAAoB,wBAAyB,EAEtD,QAAS,iBAAiB,kBAAmB,EAC7C,QAAS,mBAAmB,0BAA2B,EACvD,QAAS,mBAAmB,0BAA2B,EACvD,QAAS,mBAAmB,kBAAmB,EAE/C,QAAS,kBAAkB,qBAAsB,EACjD,QAAS,iBAAiB,qBAAsB,EAGhD,QAAS,yBAAyB,oBAAqB,EACvD,QAAS,yBAAyB,oBAAqB,EAEvD,QAAS,8BAA8B,8BAA+B,EACtE,QAAS,+BAA+B,8BAA+B;AAGzE,gBAAM,sBAAsB;AAAA,YAC3B,qBAAqB;AAAA,cACpB,iBAAiB,WAAY,GAAI;AAAA,YAClC;AAAA,UACD;AAEA,iBAAO;AAAA,QACR;AAAA,MACD;AAAA,IACD;AAIA,IAAAA,YAAW,IAAI,SAAS;AAIxB,QAAK,OAAO,WAAW,eAAe,OAAO,SAAU;AAetD,cAAQ,YAAY,SAAW,KAAK,SAAS,oBAAqB;AACjE,YAAI;AACJ,YAAK,OAAO,YAAY,UAAW;AAClC,iBAAO;AAAA,QACR,OAAO;AACN,iBAAO,CAAC;AACR,cAAK,OAAO,YAAY,WAAY;AACnC,iBAAK,oBAAoB;AAAA,UAC1B;AACA,cAAK,OAAO,uBAAuB,WAAY;AAC9C,iBAAK,qBAAqB;AAAA,UAC3B;AAAA,QACD;AACA,eAAOA,UAAS,UAAW,KAAK,IAAK;AAAA,MACtC;AAAA,IACD,WAAY,OAAO,WAAW,aAAc;AAI3C,aAAQ,UAAW,IAAIA;AAAA,IACxB;AAAA;AAAA;;;ACzcA,sBAAqB;AACrB;AAgBA,SAAS,0BACP,SACA,OACA,UAA6B;AAE7B,UAAQ,QAAQ,MAAM;IACpB,KAAK;IACL,KAAK;IACL,KAAK;AACH,aAAQ,QAAQ,SAAS,QAAQ,UAAU,QAAQ;IACrD,KAAK,SAAS;AACZ,cAAQ,QAAQ,MAAM,QAAQ,QAAQ,KAAK,IAAI,QAAQ,MAAM,KAAK,GAAG,IAAI,QAAQ;AAEjF,UAAI,MAAM,QAAQ,QAAQ,QAAQ,GAAG;AACnC,gBAAQ,SAAS,QAAQ,SAAC,GAAC;AACzB,cAAI,EAAE,SAAS;AAAS,cAAE,WAAW,EAAE;QACzC,CAAC;;;;AAKP,MAAM,qBAAqB,UAAU,MAAM,UAAU,OAAO,QAAQ,QAAQ,GAAG,yBAAyB;AAExG,SAAO,OAAO,kBAAkB,IAAK,QAAQ,SAAS,QAAQ,QAAQ,MAAM,qBAAqB,MAAO;AAC1G;AAEA,SAAS,gBACP,SACA,OACA,UACA,UAA6B;AAE7B,MACE,QAAQ,SAAS,aACjB,QAAQ,SAAS,YAChB,QAAQ,SAAS,YAAY,CAAC,QAAQ,UAAU,QAAQ,OAAO,SAAS,SAAS,QAAQ,OAAO,SAAS,UAC1G;AACA,QAAM,cAAc,gBAAAC,QAAS,UAAU,0BAA0B,SAAS,OAAO,QAAQ,CAAC;AAC1F,YAAQ,WAAW,cAAc,QAAQ,WAAW,EAAE,CAAC,EAAE,WAAW,CAAA;AAEpE,YAAQ,SAAS;;AAErB;AAIA,OAAO,eAAe,iBAAiB,QAAQ,EAAE,OAAO,kBAAiB,CAAE;AAE3E,IAAA,qBAAe;", "names": ["c<PERSON><PERSON><PERSON>", "c<PERSON><PERSON><PERSON>"]}