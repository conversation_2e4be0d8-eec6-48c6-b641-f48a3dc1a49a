import {
  init_useId,
  useId
} from "./chunk-RH6Y4V25.js";
import {
  __esm
} from "./chunk-V4OQ3NZ2.js";

// node_modules/@mui/material/utils/useId.js
var useId_default;
var init_useId2 = __esm({
  "node_modules/@mui/material/utils/useId.js"() {
    "use client";
    init_useId();
    useId_default = useId;
  }
});

export {
  useId_default,
  init_useId2 as init_useId
};
//# sourceMappingURL=chunk-QYCGFL4C.js.map
