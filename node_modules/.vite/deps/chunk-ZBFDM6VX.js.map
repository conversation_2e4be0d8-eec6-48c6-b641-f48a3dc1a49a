{"version": 3, "sources": ["../../@mui/material/Fade/Fade.js", "../../@mui/material/Backdrop/backdropClasses.js", "../../@mui/material/Backdrop/Backdrop.js", "../../@mui/material/Modal/ModalManager.js", "../../@mui/material/Unstable_TrapFocus/FocusTrap.js", "../../@mui/material/Modal/modalClasses.js", "../../@mui/material/Modal/Modal.js", "../../@mui/material/Modal/useModal.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"addEndListener\", \"appear\", \"children\", \"easing\", \"in\", \"onEnter\", \"onEntered\", \"onEntering\", \"onExit\", \"onExited\", \"onExiting\", \"style\", \"timeout\", \"TransitionComponent\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { Transition } from 'react-transition-group';\nimport elementAcceptingRef from '@mui/utils/elementAcceptingRef';\nimport getReactElementRef from '@mui/utils/getReactElementRef';\nimport useTheme from '../styles/useTheme';\nimport { reflow, getTransitionProps } from '../transitions/utils';\nimport useForkRef from '../utils/useForkRef';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst styles = {\n  entering: {\n    opacity: 1\n  },\n  entered: {\n    opacity: 1\n  }\n};\n\n/**\n * The Fade transition is used by the [Modal](/material-ui/react-modal/) component.\n * It uses [react-transition-group](https://github.com/reactjs/react-transition-group) internally.\n */\nconst Fade = /*#__PURE__*/React.forwardRef(function Fade(props, ref) {\n  const theme = useTheme();\n  const defaultTimeout = {\n    enter: theme.transitions.duration.enteringScreen,\n    exit: theme.transitions.duration.leavingScreen\n  };\n  const {\n      addEndListener,\n      appear = true,\n      children,\n      easing,\n      in: inProp,\n      onEnter,\n      onEntered,\n      onEntering,\n      onExit,\n      onExited,\n      onExiting,\n      style,\n      timeout = defaultTimeout,\n      // eslint-disable-next-line react/prop-types\n      TransitionComponent = Transition\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const enableStrictModeCompat = true;\n  const nodeRef = React.useRef(null);\n  const handleRef = useForkRef(nodeRef, getReactElementRef(children), ref);\n  const normalizedTransitionCallback = callback => maybeIsAppearing => {\n    if (callback) {\n      const node = nodeRef.current;\n\n      // onEnterXxx and onExitXxx callbacks have a different arguments.length value.\n      if (maybeIsAppearing === undefined) {\n        callback(node);\n      } else {\n        callback(node, maybeIsAppearing);\n      }\n    }\n  };\n  const handleEntering = normalizedTransitionCallback(onEntering);\n  const handleEnter = normalizedTransitionCallback((node, isAppearing) => {\n    reflow(node); // So the animation always start from the start.\n\n    const transitionProps = getTransitionProps({\n      style,\n      timeout,\n      easing\n    }, {\n      mode: 'enter'\n    });\n    node.style.webkitTransition = theme.transitions.create('opacity', transitionProps);\n    node.style.transition = theme.transitions.create('opacity', transitionProps);\n    if (onEnter) {\n      onEnter(node, isAppearing);\n    }\n  });\n  const handleEntered = normalizedTransitionCallback(onEntered);\n  const handleExiting = normalizedTransitionCallback(onExiting);\n  const handleExit = normalizedTransitionCallback(node => {\n    const transitionProps = getTransitionProps({\n      style,\n      timeout,\n      easing\n    }, {\n      mode: 'exit'\n    });\n    node.style.webkitTransition = theme.transitions.create('opacity', transitionProps);\n    node.style.transition = theme.transitions.create('opacity', transitionProps);\n    if (onExit) {\n      onExit(node);\n    }\n  });\n  const handleExited = normalizedTransitionCallback(onExited);\n  const handleAddEndListener = next => {\n    if (addEndListener) {\n      // Old call signature before `react-transition-group` implemented `nodeRef`\n      addEndListener(nodeRef.current, next);\n    }\n  };\n  return /*#__PURE__*/_jsx(TransitionComponent, _extends({\n    appear: appear,\n    in: inProp,\n    nodeRef: enableStrictModeCompat ? nodeRef : undefined,\n    onEnter: handleEnter,\n    onEntered: handleEntered,\n    onEntering: handleEntering,\n    onExit: handleExit,\n    onExited: handleExited,\n    onExiting: handleExiting,\n    addEndListener: handleAddEndListener,\n    timeout: timeout\n  }, other, {\n    children: (state, childProps) => {\n      return /*#__PURE__*/React.cloneElement(children, _extends({\n        style: _extends({\n          opacity: 0,\n          visibility: state === 'exited' && !inProp ? 'hidden' : undefined\n        }, styles[state], style, children.props.style),\n        ref: handleRef\n      }, childProps));\n    }\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Fade.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Add a custom transition end trigger. Called with the transitioning DOM\n   * node and a done callback. Allows for more fine grained transition end\n   * logic. Note: Timeouts are still used as a fallback if provided.\n   */\n  addEndListener: PropTypes.func,\n  /**\n   * Perform the enter transition when it first mounts if `in` is also `true`.\n   * Set this to `false` to disable this behavior.\n   * @default true\n   */\n  appear: PropTypes.bool,\n  /**\n   * A single child content element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * The transition timing function.\n   * You may specify a single easing or a object containing enter and exit values.\n   */\n  easing: PropTypes.oneOfType([PropTypes.shape({\n    enter: PropTypes.string,\n    exit: PropTypes.string\n  }), PropTypes.string]),\n  /**\n   * If `true`, the component will transition in.\n   */\n  in: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  onEnter: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onEntered: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onEntering: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onExit: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onExited: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onExiting: PropTypes.func,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   * @default {\n   *   enter: theme.transitions.duration.enteringScreen,\n   *   exit: theme.transitions.duration.leavingScreen,\n   * }\n   */\n  timeout: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })])\n} : void 0;\nexport default Fade;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getBackdropUtilityClass(slot) {\n  return generateUtilityClass('MuiBackdrop', slot);\n}\nconst backdropClasses = generateUtilityClasses('MuiBackdrop', ['root', 'invisible']);\nexport default backdropClasses;", "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"component\", \"components\", \"componentsProps\", \"invisible\", \"open\", \"slotProps\", \"slots\", \"TransitionComponent\", \"transitionDuration\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Fade from '../Fade';\nimport { getBackdropUtilityClass } from './backdropClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    invisible\n  } = ownerState;\n  const slots = {\n    root: ['root', invisible && 'invisible']\n  };\n  return composeClasses(slots, getBackdropUtilityClass, classes);\n};\nconst BackdropRoot = styled('div', {\n  name: 'Mu<PERSON><PERSON>ackdrop',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.invisible && styles.invisible];\n  }\n})(({\n  ownerState\n}) => _extends({\n  position: 'fixed',\n  display: 'flex',\n  alignItems: 'center',\n  justifyContent: 'center',\n  right: 0,\n  bottom: 0,\n  top: 0,\n  left: 0,\n  backgroundColor: 'rgba(0, 0, 0, 0.5)',\n  WebkitTapHighlightColor: 'transparent'\n}, ownerState.invisible && {\n  backgroundColor: 'transparent'\n}));\nconst Backdrop = /*#__PURE__*/React.forwardRef(function Backdrop(inProps, ref) {\n  var _slotProps$root, _ref, _slots$root;\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiBackdrop'\n  });\n  const {\n      children,\n      className,\n      component = 'div',\n      components = {},\n      componentsProps = {},\n      invisible = false,\n      open,\n      slotProps = {},\n      slots = {},\n      TransitionComponent = Fade,\n      transitionDuration\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component,\n    invisible\n  });\n  const classes = useUtilityClasses(ownerState);\n  const rootSlotProps = (_slotProps$root = slotProps.root) != null ? _slotProps$root : componentsProps.root;\n  return /*#__PURE__*/_jsx(TransitionComponent, _extends({\n    in: open,\n    timeout: transitionDuration\n  }, other, {\n    children: /*#__PURE__*/_jsx(BackdropRoot, _extends({\n      \"aria-hidden\": true\n    }, rootSlotProps, {\n      as: (_ref = (_slots$root = slots.root) != null ? _slots$root : components.Root) != null ? _ref : component,\n      className: clsx(classes.root, className, rootSlotProps == null ? void 0 : rootSlotProps.className),\n      ownerState: _extends({}, ownerState, rootSlotProps == null ? void 0 : rootSlotProps.ownerState),\n      classes: classes,\n      ref: ref,\n      children: children\n    }))\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Backdrop.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `slots` prop.\n   * It's recommended to use the `slots` prop instead.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * If `true`, the backdrop is invisible.\n   * It can be used when rendering a popover or a custom select component.\n   * @default false\n   */\n  invisible: PropTypes.bool,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `componentsProps` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.object\n  }),\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `components` prop, which will be deprecated in the future.\n   *\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The component used for the transition.\n   * [Follow this guide](/material-ui/transitions/#transitioncomponent-prop) to learn more about the requirements for this component.\n   * @default Fade\n   */\n  TransitionComponent: PropTypes.elementType,\n  /**\n   * The duration for the transition, in milliseconds.\n   * You may specify a single timeout for all transitions, or individually with an object.\n   */\n  transitionDuration: PropTypes.oneOfType([PropTypes.number, PropTypes.shape({\n    appear: PropTypes.number,\n    enter: PropTypes.number,\n    exit: PropTypes.number\n  })])\n} : void 0;\nexport default Backdrop;", "import { unstable_ownerWindow as ownerWindow, unstable_ownerDocument as ownerDocument, unstable_getScrollbarSize as getScrollbarSize } from '@mui/utils';\n// Is a vertical scrollbar displayed?\nfunction isOverflowing(container) {\n  const doc = ownerDocument(container);\n  if (doc.body === container) {\n    return ownerWindow(container).innerWidth > doc.documentElement.clientWidth;\n  }\n  return container.scrollHeight > container.clientHeight;\n}\nexport function ariaHidden(element, show) {\n  if (show) {\n    element.setAttribute('aria-hidden', 'true');\n  } else {\n    element.removeAttribute('aria-hidden');\n  }\n}\nfunction getPaddingRight(element) {\n  return parseInt(ownerWindow(element).getComputedStyle(element).paddingRight, 10) || 0;\n}\nfunction isAriaHiddenForbiddenOnElement(element) {\n  // The forbidden HTML tags are the ones from ARIA specification that\n  // can be children of body and can't have aria-hidden attribute.\n  // cf. https://www.w3.org/TR/html-aria/#docconformance\n  const forbiddenTagNames = ['TEMPLATE', 'SCRIPT', 'STYLE', 'LINK', 'MAP', 'META', 'NOSCRIPT', 'PICTURE', 'COL', 'COLGROUP', 'PARAM', 'SLOT', 'SOURCE', 'TRACK'];\n  const isForbiddenTagName = forbiddenTagNames.indexOf(element.tagName) !== -1;\n  const isInputHidden = element.tagName === 'INPUT' && element.getAttribute('type') === 'hidden';\n  return isForbiddenTagName || isInputHidden;\n}\nfunction ariaHiddenSiblings(container, mountElement, currentElement, elementsToExclude, show) {\n  const blacklist = [mountElement, currentElement, ...elementsToExclude];\n  [].forEach.call(container.children, element => {\n    const isNotExcludedElement = blacklist.indexOf(element) === -1;\n    const isNotForbiddenElement = !isAriaHiddenForbiddenOnElement(element);\n    if (isNotExcludedElement && isNotForbiddenElement) {\n      ariaHidden(element, show);\n    }\n  });\n}\nfunction findIndexOf(items, callback) {\n  let idx = -1;\n  items.some((item, index) => {\n    if (callback(item)) {\n      idx = index;\n      return true;\n    }\n    return false;\n  });\n  return idx;\n}\nfunction handleContainer(containerInfo, props) {\n  const restoreStyle = [];\n  const container = containerInfo.container;\n  if (!props.disableScrollLock) {\n    if (isOverflowing(container)) {\n      // Compute the size before applying overflow hidden to avoid any scroll jumps.\n      const scrollbarSize = getScrollbarSize(ownerDocument(container));\n      restoreStyle.push({\n        value: container.style.paddingRight,\n        property: 'padding-right',\n        el: container\n      });\n      // Use computed style, here to get the real padding to add our scrollbar width.\n      container.style.paddingRight = `${getPaddingRight(container) + scrollbarSize}px`;\n\n      // .mui-fixed is a global helper.\n      const fixedElements = ownerDocument(container).querySelectorAll('.mui-fixed');\n      [].forEach.call(fixedElements, element => {\n        restoreStyle.push({\n          value: element.style.paddingRight,\n          property: 'padding-right',\n          el: element\n        });\n        element.style.paddingRight = `${getPaddingRight(element) + scrollbarSize}px`;\n      });\n    }\n    let scrollContainer;\n    if (container.parentNode instanceof DocumentFragment) {\n      scrollContainer = ownerDocument(container).body;\n    } else {\n      // Support html overflow-y: auto for scroll stability between pages\n      // https://css-tricks.com/snippets/css/force-vertical-scrollbar/\n      const parent = container.parentElement;\n      const containerWindow = ownerWindow(container);\n      scrollContainer = (parent == null ? void 0 : parent.nodeName) === 'HTML' && containerWindow.getComputedStyle(parent).overflowY === 'scroll' ? parent : container;\n    }\n\n    // Block the scroll even if no scrollbar is visible to account for mobile keyboard\n    // screensize shrink.\n    restoreStyle.push({\n      value: scrollContainer.style.overflow,\n      property: 'overflow',\n      el: scrollContainer\n    }, {\n      value: scrollContainer.style.overflowX,\n      property: 'overflow-x',\n      el: scrollContainer\n    }, {\n      value: scrollContainer.style.overflowY,\n      property: 'overflow-y',\n      el: scrollContainer\n    });\n    scrollContainer.style.overflow = 'hidden';\n  }\n  const restore = () => {\n    restoreStyle.forEach(({\n      value,\n      el,\n      property\n    }) => {\n      if (value) {\n        el.style.setProperty(property, value);\n      } else {\n        el.style.removeProperty(property);\n      }\n    });\n  };\n  return restore;\n}\nfunction getHiddenSiblings(container) {\n  const hiddenSiblings = [];\n  [].forEach.call(container.children, element => {\n    if (element.getAttribute('aria-hidden') === 'true') {\n      hiddenSiblings.push(element);\n    }\n  });\n  return hiddenSiblings;\n}\n/**\n * @ignore - do not document.\n *\n * Proper state management for containers and the modals in those containers.\n * Simplified, but inspired by react-overlay's ModalManager class.\n * Used by the Modal to ensure proper styling of containers.\n */\nexport class ModalManager {\n  constructor() {\n    this.containers = void 0;\n    this.modals = void 0;\n    this.modals = [];\n    this.containers = [];\n  }\n  add(modal, container) {\n    let modalIndex = this.modals.indexOf(modal);\n    if (modalIndex !== -1) {\n      return modalIndex;\n    }\n    modalIndex = this.modals.length;\n    this.modals.push(modal);\n\n    // If the modal we are adding is already in the DOM.\n    if (modal.modalRef) {\n      ariaHidden(modal.modalRef, false);\n    }\n    const hiddenSiblings = getHiddenSiblings(container);\n    ariaHiddenSiblings(container, modal.mount, modal.modalRef, hiddenSiblings, true);\n    const containerIndex = findIndexOf(this.containers, item => item.container === container);\n    if (containerIndex !== -1) {\n      this.containers[containerIndex].modals.push(modal);\n      return modalIndex;\n    }\n    this.containers.push({\n      modals: [modal],\n      container,\n      restore: null,\n      hiddenSiblings\n    });\n    return modalIndex;\n  }\n  mount(modal, props) {\n    const containerIndex = findIndexOf(this.containers, item => item.modals.indexOf(modal) !== -1);\n    const containerInfo = this.containers[containerIndex];\n    if (!containerInfo.restore) {\n      containerInfo.restore = handleContainer(containerInfo, props);\n    }\n  }\n  remove(modal, ariaHiddenState = true) {\n    const modalIndex = this.modals.indexOf(modal);\n    if (modalIndex === -1) {\n      return modalIndex;\n    }\n    const containerIndex = findIndexOf(this.containers, item => item.modals.indexOf(modal) !== -1);\n    const containerInfo = this.containers[containerIndex];\n    containerInfo.modals.splice(containerInfo.modals.indexOf(modal), 1);\n    this.modals.splice(modalIndex, 1);\n\n    // If that was the last modal in a container, clean up the container.\n    if (containerInfo.modals.length === 0) {\n      // The modal might be closed before it had the chance to be mounted in the DOM.\n      if (containerInfo.restore) {\n        containerInfo.restore();\n      }\n      if (modal.modalRef) {\n        // In case the modal wasn't in the DOM yet.\n        ariaHidden(modal.modalRef, ariaHiddenState);\n      }\n      ariaHiddenSiblings(containerInfo.container, modal.mount, modal.modalRef, containerInfo.hiddenSiblings, false);\n      this.containers.splice(containerIndex, 1);\n    } else {\n      // Otherwise make sure the next top modal is visible to a screen reader.\n      const nextTop = containerInfo.modals[containerInfo.modals.length - 1];\n      // as soon as a modal is adding its modalRef is undefined. it can't set\n      // aria-hidden because the dom element doesn't exist either\n      // when modal was unmounted before modalRef gets null\n      if (nextTop.modalRef) {\n        ariaHidden(nextTop.modalRef, false);\n      }\n    }\n    return modalIndex;\n  }\n  isTopModal(modal) {\n    return this.modals.length > 0 && this.modals[this.modals.length - 1] === modal;\n  }\n}", "'use client';\n\n/* eslint-disable consistent-return, jsx-a11y/no-noninteractive-tabindex */\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { exactProp, elementAcceptingRef, unstable_useForkRef as useForkRef, unstable_ownerDocument as ownerDocument, unstable_getReactElementRef as getReactElementRef } from '@mui/utils';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\n// Inspired by https://github.com/focus-trap/tabbable\nconst candidatesSelector = ['input', 'select', 'textarea', 'a[href]', 'button', '[tabindex]', 'audio[controls]', 'video[controls]', '[contenteditable]:not([contenteditable=\"false\"])'].join(',');\nfunction getTabIndex(node) {\n  const tabindexAttr = parseInt(node.getAttribute('tabindex') || '', 10);\n  if (!Number.isNaN(tabindexAttr)) {\n    return tabindexAttr;\n  }\n\n  // Browsers do not return `tabIndex` correctly for contentEditable nodes;\n  // https://bugs.chromium.org/p/chromium/issues/detail?id=661108&q=contenteditable%20tabindex&can=2\n  // so if they don't have a tabindex attribute specifically set, assume it's 0.\n  // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n  //  `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n  //  yet they are still part of the regular tab order; in FF, they get a default\n  //  `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n  //  order, consider their tab index to be 0.\n  if (node.contentEditable === 'true' || (node.nodeName === 'AUDIO' || node.nodeName === 'VIDEO' || node.nodeName === 'DETAILS') && node.getAttribute('tabindex') === null) {\n    return 0;\n  }\n  return node.tabIndex;\n}\nfunction isNonTabbableRadio(node) {\n  if (node.tagName !== 'INPUT' || node.type !== 'radio') {\n    return false;\n  }\n  if (!node.name) {\n    return false;\n  }\n  const getRadio = selector => node.ownerDocument.querySelector(`input[type=\"radio\"]${selector}`);\n  let roving = getRadio(`[name=\"${node.name}\"]:checked`);\n  if (!roving) {\n    roving = getRadio(`[name=\"${node.name}\"]`);\n  }\n  return roving !== node;\n}\nfunction isNodeMatchingSelectorFocusable(node) {\n  if (node.disabled || node.tagName === 'INPUT' && node.type === 'hidden' || isNonTabbableRadio(node)) {\n    return false;\n  }\n  return true;\n}\nfunction defaultGetTabbable(root) {\n  const regularTabNodes = [];\n  const orderedTabNodes = [];\n  Array.from(root.querySelectorAll(candidatesSelector)).forEach((node, i) => {\n    const nodeTabIndex = getTabIndex(node);\n    if (nodeTabIndex === -1 || !isNodeMatchingSelectorFocusable(node)) {\n      return;\n    }\n    if (nodeTabIndex === 0) {\n      regularTabNodes.push(node);\n    } else {\n      orderedTabNodes.push({\n        documentOrder: i,\n        tabIndex: nodeTabIndex,\n        node: node\n      });\n    }\n  });\n  return orderedTabNodes.sort((a, b) => a.tabIndex === b.tabIndex ? a.documentOrder - b.documentOrder : a.tabIndex - b.tabIndex).map(a => a.node).concat(regularTabNodes);\n}\nfunction defaultIsEnabled() {\n  return true;\n}\n\n/**\n * @ignore - internal component.\n */\nfunction FocusTrap(props) {\n  const {\n    children,\n    disableAutoFocus = false,\n    disableEnforceFocus = false,\n    disableRestoreFocus = false,\n    getTabbable = defaultGetTabbable,\n    isEnabled = defaultIsEnabled,\n    open\n  } = props;\n  const ignoreNextEnforceFocus = React.useRef(false);\n  const sentinelStart = React.useRef(null);\n  const sentinelEnd = React.useRef(null);\n  const nodeToRestore = React.useRef(null);\n  const reactFocusEventTarget = React.useRef(null);\n  // This variable is useful when disableAutoFocus is true.\n  // It waits for the active element to move into the component to activate.\n  const activated = React.useRef(false);\n  const rootRef = React.useRef(null);\n  const handleRef = useForkRef(getReactElementRef(children), rootRef);\n  const lastKeydown = React.useRef(null);\n  React.useEffect(() => {\n    // We might render an empty child.\n    if (!open || !rootRef.current) {\n      return;\n    }\n    activated.current = !disableAutoFocus;\n  }, [disableAutoFocus, open]);\n  React.useEffect(() => {\n    // We might render an empty child.\n    if (!open || !rootRef.current) {\n      return;\n    }\n    const doc = ownerDocument(rootRef.current);\n    if (!rootRef.current.contains(doc.activeElement)) {\n      if (!rootRef.current.hasAttribute('tabIndex')) {\n        if (process.env.NODE_ENV !== 'production') {\n          console.error(['MUI: The modal content node does not accept focus.', 'For the benefit of assistive technologies, ' + 'the tabIndex of the node is being set to \"-1\".'].join('\\n'));\n        }\n        rootRef.current.setAttribute('tabIndex', '-1');\n      }\n      if (activated.current) {\n        rootRef.current.focus();\n      }\n    }\n    return () => {\n      // restoreLastFocus()\n      if (!disableRestoreFocus) {\n        // In IE11 it is possible for document.activeElement to be null resulting\n        // in nodeToRestore.current being null.\n        // Not all elements in IE11 have a focus method.\n        // Once IE11 support is dropped the focus() call can be unconditional.\n        if (nodeToRestore.current && nodeToRestore.current.focus) {\n          ignoreNextEnforceFocus.current = true;\n          nodeToRestore.current.focus();\n        }\n        nodeToRestore.current = null;\n      }\n    };\n    // Missing `disableRestoreFocus` which is fine.\n    // We don't support changing that prop on an open FocusTrap\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [open]);\n  React.useEffect(() => {\n    // We might render an empty child.\n    if (!open || !rootRef.current) {\n      return;\n    }\n    const doc = ownerDocument(rootRef.current);\n    const loopFocus = nativeEvent => {\n      lastKeydown.current = nativeEvent;\n      if (disableEnforceFocus || !isEnabled() || nativeEvent.key !== 'Tab') {\n        return;\n      }\n\n      // Make sure the next tab starts from the right place.\n      // doc.activeElement refers to the origin.\n      if (doc.activeElement === rootRef.current && nativeEvent.shiftKey) {\n        // We need to ignore the next contain as\n        // it will try to move the focus back to the rootRef element.\n        ignoreNextEnforceFocus.current = true;\n        if (sentinelEnd.current) {\n          sentinelEnd.current.focus();\n        }\n      }\n    };\n    const contain = () => {\n      const rootElement = rootRef.current;\n\n      // Cleanup functions are executed lazily in React 17.\n      // Contain can be called between the component being unmounted and its cleanup function being run.\n      if (rootElement === null) {\n        return;\n      }\n      if (!doc.hasFocus() || !isEnabled() || ignoreNextEnforceFocus.current) {\n        ignoreNextEnforceFocus.current = false;\n        return;\n      }\n\n      // The focus is already inside\n      if (rootElement.contains(doc.activeElement)) {\n        return;\n      }\n\n      // The disableEnforceFocus is set and the focus is outside of the focus trap (and sentinel nodes)\n      if (disableEnforceFocus && doc.activeElement !== sentinelStart.current && doc.activeElement !== sentinelEnd.current) {\n        return;\n      }\n\n      // if the focus event is not coming from inside the children's react tree, reset the refs\n      if (doc.activeElement !== reactFocusEventTarget.current) {\n        reactFocusEventTarget.current = null;\n      } else if (reactFocusEventTarget.current !== null) {\n        return;\n      }\n      if (!activated.current) {\n        return;\n      }\n      let tabbable = [];\n      if (doc.activeElement === sentinelStart.current || doc.activeElement === sentinelEnd.current) {\n        tabbable = getTabbable(rootRef.current);\n      }\n\n      // one of the sentinel nodes was focused, so move the focus\n      // to the first/last tabbable element inside the focus trap\n      if (tabbable.length > 0) {\n        var _lastKeydown$current, _lastKeydown$current2;\n        const isShiftTab = Boolean(((_lastKeydown$current = lastKeydown.current) == null ? void 0 : _lastKeydown$current.shiftKey) && ((_lastKeydown$current2 = lastKeydown.current) == null ? void 0 : _lastKeydown$current2.key) === 'Tab');\n        const focusNext = tabbable[0];\n        const focusPrevious = tabbable[tabbable.length - 1];\n        if (typeof focusNext !== 'string' && typeof focusPrevious !== 'string') {\n          if (isShiftTab) {\n            focusPrevious.focus();\n          } else {\n            focusNext.focus();\n          }\n        }\n        // no tabbable elements in the trap focus or the focus was outside of the focus trap\n      } else {\n        rootElement.focus();\n      }\n    };\n    doc.addEventListener('focusin', contain);\n    doc.addEventListener('keydown', loopFocus, true);\n\n    // With Edge, Safari and Firefox, no focus related events are fired when the focused area stops being a focused area.\n    // for example https://bugzilla.mozilla.org/show_bug.cgi?id=559561.\n    // Instead, we can look if the active element was restored on the BODY element.\n    //\n    // The whatwg spec defines how the browser should behave but does not explicitly mention any events:\n    // https://html.spec.whatwg.org/multipage/interaction.html#focus-fixup-rule.\n    const interval = setInterval(() => {\n      if (doc.activeElement && doc.activeElement.tagName === 'BODY') {\n        contain();\n      }\n    }, 50);\n    return () => {\n      clearInterval(interval);\n      doc.removeEventListener('focusin', contain);\n      doc.removeEventListener('keydown', loopFocus, true);\n    };\n  }, [disableAutoFocus, disableEnforceFocus, disableRestoreFocus, isEnabled, open, getTabbable]);\n  const onFocus = event => {\n    if (nodeToRestore.current === null) {\n      nodeToRestore.current = event.relatedTarget;\n    }\n    activated.current = true;\n    reactFocusEventTarget.current = event.target;\n    const childrenPropsHandler = children.props.onFocus;\n    if (childrenPropsHandler) {\n      childrenPropsHandler(event);\n    }\n  };\n  const handleFocusSentinel = event => {\n    if (nodeToRestore.current === null) {\n      nodeToRestore.current = event.relatedTarget;\n    }\n    activated.current = true;\n  };\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(\"div\", {\n      tabIndex: open ? 0 : -1,\n      onFocus: handleFocusSentinel,\n      ref: sentinelStart,\n      \"data-testid\": \"sentinelStart\"\n    }), /*#__PURE__*/React.cloneElement(children, {\n      ref: handleRef,\n      onFocus\n    }), /*#__PURE__*/_jsx(\"div\", {\n      tabIndex: open ? 0 : -1,\n      onFocus: handleFocusSentinel,\n      ref: sentinelEnd,\n      \"data-testid\": \"sentinelEnd\"\n    })]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? FocusTrap.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A single child content element.\n   */\n  children: elementAcceptingRef,\n  /**\n   * If `true`, the focus trap will not automatically shift focus to itself when it opens, and\n   * replace it to the last focused element when it closes.\n   * This also works correctly with any focus trap children that have the `disableAutoFocus` prop.\n   *\n   * Generally this should never be set to `true` as it makes the focus trap less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableAutoFocus: PropTypes.bool,\n  /**\n   * If `true`, the focus trap will not prevent focus from leaving the focus trap while open.\n   *\n   * Generally this should never be set to `true` as it makes the focus trap less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableEnforceFocus: PropTypes.bool,\n  /**\n   * If `true`, the focus trap will not restore focus to previously focused element once\n   * focus trap is hidden or unmounted.\n   * @default false\n   */\n  disableRestoreFocus: PropTypes.bool,\n  /**\n   * Returns an array of ordered tabbable nodes (i.e. in tab order) within the root.\n   * For instance, you can provide the \"tabbable\" npm dependency.\n   * @param {HTMLElement} root\n   */\n  getTabbable: PropTypes.func,\n  /**\n   * This prop extends the `open` prop.\n   * It allows to toggle the open state without having to wait for a rerender when changing the `open` prop.\n   * This prop should be memoized.\n   * It can be used to support multiple focus trap mounted at the same time.\n   * @default function defaultIsEnabled(): boolean {\n   *   return true;\n   * }\n   */\n  isEnabled: PropTypes.func,\n  /**\n   * If `true`, focus is locked.\n   */\n  open: PropTypes.bool.isRequired\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  FocusTrap['propTypes' + ''] = exactProp(FocusTrap.propTypes);\n}\nexport default FocusTrap;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getModalUtilityClass(slot) {\n  return generateUtilityClass('MuiModal', slot);\n}\nconst modalClasses = generateUtilityClasses('MuiModal', ['root', 'hidden', 'backdrop']);\nexport default modalClasses;", "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"BackdropComponent\", \"BackdropProps\", \"classes\", \"className\", \"closeAfterTransition\", \"children\", \"container\", \"component\", \"components\", \"componentsProps\", \"disableAutoFocus\", \"disableEnforceFocus\", \"disableEscapeKeyDown\", \"disablePortal\", \"disableRestoreFocus\", \"disableScrollLock\", \"hideBackdrop\", \"keepMounted\", \"onBackdropClick\", \"onClose\", \"onTransitionEnter\", \"onTransitionExited\", \"open\", \"slotProps\", \"slots\", \"theme\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport HTMLElementType from '@mui/utils/HTMLElementType';\nimport elementAcceptingRef from '@mui/utils/elementAcceptingRef';\nimport composeClasses from '@mui/utils/composeClasses';\nimport useSlotProps from '@mui/utils/useSlotProps';\nimport FocusTrap from '../Unstable_TrapFocus';\nimport Portal from '../Portal';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport Backdrop from '../Backdrop';\nimport useModal from './useModal';\nimport { getModalUtilityClass } from './modalClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    open,\n    exited,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', !open && exited && 'hidden'],\n    backdrop: ['backdrop']\n  };\n  return composeClasses(slots, getModalUtilityClass, classes);\n};\nconst ModalRoot = styled('div', {\n  name: 'MuiModal',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, !ownerState.open && ownerState.exited && styles.hidden];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  position: 'fixed',\n  zIndex: (theme.vars || theme).zIndex.modal,\n  right: 0,\n  bottom: 0,\n  top: 0,\n  left: 0\n}, !ownerState.open && ownerState.exited && {\n  visibility: 'hidden'\n}));\nconst ModalBackdrop = styled(Backdrop, {\n  name: 'MuiModal',\n  slot: 'Backdrop',\n  overridesResolver: (props, styles) => {\n    return styles.backdrop;\n  }\n})({\n  zIndex: -1\n});\n\n/**\n * Modal is a lower-level construct that is leveraged by the following components:\n *\n * - [Dialog](/material-ui/api/dialog/)\n * - [Drawer](/material-ui/api/drawer/)\n * - [Menu](/material-ui/api/menu/)\n * - [Popover](/material-ui/api/popover/)\n *\n * If you are creating a modal dialog, you probably want to use the [Dialog](/material-ui/api/dialog/) component\n * rather than directly using Modal.\n *\n * This component shares many concepts with [react-overlays](https://react-bootstrap.github.io/react-overlays/#modals).\n */\nconst Modal = /*#__PURE__*/React.forwardRef(function Modal(inProps, ref) {\n  var _ref, _slots$root, _ref2, _slots$backdrop, _slotProps$root, _slotProps$backdrop;\n  const props = useDefaultProps({\n    name: 'MuiModal',\n    props: inProps\n  });\n  const {\n      BackdropComponent = ModalBackdrop,\n      BackdropProps,\n      className,\n      closeAfterTransition = false,\n      children,\n      container,\n      component,\n      components = {},\n      componentsProps = {},\n      disableAutoFocus = false,\n      disableEnforceFocus = false,\n      disableEscapeKeyDown = false,\n      disablePortal = false,\n      disableRestoreFocus = false,\n      disableScrollLock = false,\n      hideBackdrop = false,\n      keepMounted = false,\n      onBackdropClick,\n      open,\n      slotProps,\n      slots\n      // eslint-disable-next-line react/prop-types\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const propsWithDefaults = _extends({}, props, {\n    closeAfterTransition,\n    disableAutoFocus,\n    disableEnforceFocus,\n    disableEscapeKeyDown,\n    disablePortal,\n    disableRestoreFocus,\n    disableScrollLock,\n    hideBackdrop,\n    keepMounted\n  });\n  const {\n    getRootProps,\n    getBackdropProps,\n    getTransitionProps,\n    portalRef,\n    isTopModal,\n    exited,\n    hasTransition\n  } = useModal(_extends({}, propsWithDefaults, {\n    rootRef: ref\n  }));\n  const ownerState = _extends({}, propsWithDefaults, {\n    exited\n  });\n  const classes = useUtilityClasses(ownerState);\n  const childProps = {};\n  if (children.props.tabIndex === undefined) {\n    childProps.tabIndex = '-1';\n  }\n\n  // It's a Transition like component\n  if (hasTransition) {\n    const {\n      onEnter,\n      onExited\n    } = getTransitionProps();\n    childProps.onEnter = onEnter;\n    childProps.onExited = onExited;\n  }\n  const RootSlot = (_ref = (_slots$root = slots == null ? void 0 : slots.root) != null ? _slots$root : components.Root) != null ? _ref : ModalRoot;\n  const BackdropSlot = (_ref2 = (_slots$backdrop = slots == null ? void 0 : slots.backdrop) != null ? _slots$backdrop : components.Backdrop) != null ? _ref2 : BackdropComponent;\n  const rootSlotProps = (_slotProps$root = slotProps == null ? void 0 : slotProps.root) != null ? _slotProps$root : componentsProps.root;\n  const backdropSlotProps = (_slotProps$backdrop = slotProps == null ? void 0 : slotProps.backdrop) != null ? _slotProps$backdrop : componentsProps.backdrop;\n  const rootProps = useSlotProps({\n    elementType: RootSlot,\n    externalSlotProps: rootSlotProps,\n    externalForwardedProps: other,\n    getSlotProps: getRootProps,\n    additionalProps: {\n      ref,\n      as: component\n    },\n    ownerState,\n    className: clsx(className, rootSlotProps == null ? void 0 : rootSlotProps.className, classes == null ? void 0 : classes.root, !ownerState.open && ownerState.exited && (classes == null ? void 0 : classes.hidden))\n  });\n  const backdropProps = useSlotProps({\n    elementType: BackdropSlot,\n    externalSlotProps: backdropSlotProps,\n    additionalProps: BackdropProps,\n    getSlotProps: otherHandlers => {\n      return getBackdropProps(_extends({}, otherHandlers, {\n        onClick: e => {\n          if (onBackdropClick) {\n            onBackdropClick(e);\n          }\n          if (otherHandlers != null && otherHandlers.onClick) {\n            otherHandlers.onClick(e);\n          }\n        }\n      }));\n    },\n    className: clsx(backdropSlotProps == null ? void 0 : backdropSlotProps.className, BackdropProps == null ? void 0 : BackdropProps.className, classes == null ? void 0 : classes.backdrop),\n    ownerState\n  });\n  if (!keepMounted && !open && (!hasTransition || exited)) {\n    return null;\n  }\n  return /*#__PURE__*/_jsx(Portal, {\n    ref: portalRef,\n    container: container,\n    disablePortal: disablePortal,\n    children: /*#__PURE__*/_jsxs(RootSlot, _extends({}, rootProps, {\n      children: [!hideBackdrop && BackdropComponent ? /*#__PURE__*/_jsx(BackdropSlot, _extends({}, backdropProps)) : null, /*#__PURE__*/_jsx(FocusTrap, {\n        disableEnforceFocus: disableEnforceFocus,\n        disableAutoFocus: disableAutoFocus,\n        disableRestoreFocus: disableRestoreFocus,\n        isEnabled: isTopModal,\n        open: open,\n        children: /*#__PURE__*/React.cloneElement(children, childProps)\n      })]\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Modal.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * A backdrop component. This prop enables custom backdrop rendering.\n   * @deprecated Use `slots.backdrop` instead. While this prop currently works, it will be removed in the next major version.\n   * Use the `slots.backdrop` prop to make your application ready for the next version of Material UI.\n   * @default styled(Backdrop, {\n   *   name: 'MuiModal',\n   *   slot: 'Backdrop',\n   *   overridesResolver: (props, styles) => {\n   *     return styles.backdrop;\n   *   },\n   * })({\n   *   zIndex: -1,\n   * })\n   */\n  BackdropComponent: PropTypes.elementType,\n  /**\n   * Props applied to the [`Backdrop`](/material-ui/api/backdrop/) element.\n   * @deprecated Use `slotProps.backdrop` instead.\n   */\n  BackdropProps: PropTypes.object,\n  /**\n   * A single child content element.\n   */\n  children: elementAcceptingRef.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * When set to true the Modal waits until a nested Transition is completed before closing.\n   * @default false\n   */\n  closeAfterTransition: PropTypes.bool,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The components used for each slot inside.\n   *\n   * This prop is an alias for the `slots` prop.\n   * It's recommended to use the `slots` prop instead.\n   *\n   * @default {}\n   */\n  components: PropTypes.shape({\n    Backdrop: PropTypes.elementType,\n    Root: PropTypes.elementType\n  }),\n  /**\n   * The extra props for the slot components.\n   * You can override the existing props or add new ones.\n   *\n   * This prop is an alias for the `slotProps` prop.\n   * It's recommended to use the `slotProps` prop instead, as `componentsProps` will be deprecated in the future.\n   *\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * An HTML element or function that returns one.\n   * The `container` will have the portal children appended to it.\n   *\n   * You can also provide a callback, which is called in a React layout effect.\n   * This lets you set the container from a ref, and also makes server-side rendering possible.\n   *\n   * By default, it uses the body of the top-level document object,\n   * so it's simply `document.body` most of the time.\n   */\n  container: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([HTMLElementType, PropTypes.func]),\n  /**\n   * If `true`, the modal will not automatically shift focus to itself when it opens, and\n   * replace it to the last focused element when it closes.\n   * This also works correctly with any modal children that have the `disableAutoFocus` prop.\n   *\n   * Generally this should never be set to `true` as it makes the modal less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableAutoFocus: PropTypes.bool,\n  /**\n   * If `true`, the modal will not prevent focus from leaving the modal while open.\n   *\n   * Generally this should never be set to `true` as it makes the modal less\n   * accessible to assistive technologies, like screen readers.\n   * @default false\n   */\n  disableEnforceFocus: PropTypes.bool,\n  /**\n   * If `true`, hitting escape will not fire the `onClose` callback.\n   * @default false\n   */\n  disableEscapeKeyDown: PropTypes.bool,\n  /**\n   * The `children` will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool,\n  /**\n   * If `true`, the modal will not restore focus to previously focused element once\n   * modal is hidden or unmounted.\n   * @default false\n   */\n  disableRestoreFocus: PropTypes.bool,\n  /**\n   * Disable the scroll lock behavior.\n   * @default false\n   */\n  disableScrollLock: PropTypes.bool,\n  /**\n   * If `true`, the backdrop is not rendered.\n   * @default false\n   */\n  hideBackdrop: PropTypes.bool,\n  /**\n   * Always keep the children in the DOM.\n   * This prop can be useful in SEO situation or\n   * when you want to maximize the responsiveness of the Modal.\n   * @default false\n   */\n  keepMounted: PropTypes.bool,\n  /**\n   * Callback fired when the backdrop is clicked.\n   * @deprecated Use the `onClose` prop with the `reason` argument to handle the `backdropClick` events.\n   */\n  onBackdropClick: PropTypes.func,\n  /**\n   * Callback fired when the component requests to be closed.\n   * The `reason` parameter can optionally be used to control the response to `onClose`.\n   *\n   * @param {object} event The event source of the callback.\n   * @param {string} reason Can be: `\"escapeKeyDown\"`, `\"backdropClick\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * A function called when a transition enters.\n   */\n  onTransitionEnter: PropTypes.func,\n  /**\n   * A function called when a transition has exited.\n   */\n  onTransitionExited: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool.isRequired,\n  /**\n   * The props used for each slot inside the Modal.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    backdrop: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the Modal.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    backdrop: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Modal;", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_ownerDocument as ownerDocument, unstable_useForkRef as useForkRef, unstable_useEventCallback as useEventCallback, unstable_createChainedFunction as createChainedFunction } from '@mui/utils';\nimport extractEventHandlers from '@mui/utils/extractEventHandlers';\nimport { ModalManager, ariaHidden } from './ModalManager';\nfunction getContainer(container) {\n  return typeof container === 'function' ? container() : container;\n}\nfunction getHasTransition(children) {\n  return children ? children.props.hasOwnProperty('in') : false;\n}\n\n// A modal manager used to track and manage the state of open Modals.\n// Modals don't open on the server so this won't conflict with concurrent requests.\nconst defaultManager = new ModalManager();\n/**\n *\n * Demos:\n *\n * - [Modal](https://mui.com/base-ui/react-modal/#hook)\n *\n * API:\n *\n * - [useModal API](https://mui.com/base-ui/react-modal/hooks-api/#use-modal)\n */\nfunction useModal(parameters) {\n  const {\n    container,\n    disableEscapeKeyDown = false,\n    disableScrollLock = false,\n    // @ts-ignore internal logic - Base UI supports the manager as a prop too\n    manager = defaultManager,\n    closeAfterTransition = false,\n    onTransitionEnter,\n    onTransitionExited,\n    children,\n    onClose,\n    open,\n    rootRef\n  } = parameters;\n\n  // @ts-ignore internal logic\n  const modal = React.useRef({});\n  const mountNodeRef = React.useRef(null);\n  const modalRef = React.useRef(null);\n  const handleRef = useForkRef(modalRef, rootRef);\n  const [exited, setExited] = React.useState(!open);\n  const hasTransition = getHasTransition(children);\n  let ariaHiddenProp = true;\n  if (parameters['aria-hidden'] === 'false' || parameters['aria-hidden'] === false) {\n    ariaHiddenProp = false;\n  }\n  const getDoc = () => ownerDocument(mountNodeRef.current);\n  const getModal = () => {\n    modal.current.modalRef = modalRef.current;\n    modal.current.mount = mountNodeRef.current;\n    return modal.current;\n  };\n  const handleMounted = () => {\n    manager.mount(getModal(), {\n      disableScrollLock\n    });\n\n    // Fix a bug on Chrome where the scroll isn't initially 0.\n    if (modalRef.current) {\n      modalRef.current.scrollTop = 0;\n    }\n  };\n  const handleOpen = useEventCallback(() => {\n    const resolvedContainer = getContainer(container) || getDoc().body;\n    manager.add(getModal(), resolvedContainer);\n\n    // The element was already mounted.\n    if (modalRef.current) {\n      handleMounted();\n    }\n  });\n  const isTopModal = React.useCallback(() => manager.isTopModal(getModal()), [manager]);\n  const handlePortalRef = useEventCallback(node => {\n    mountNodeRef.current = node;\n    if (!node) {\n      return;\n    }\n    if (open && isTopModal()) {\n      handleMounted();\n    } else if (modalRef.current) {\n      ariaHidden(modalRef.current, ariaHiddenProp);\n    }\n  });\n  const handleClose = React.useCallback(() => {\n    manager.remove(getModal(), ariaHiddenProp);\n  }, [ariaHiddenProp, manager]);\n  React.useEffect(() => {\n    return () => {\n      handleClose();\n    };\n  }, [handleClose]);\n  React.useEffect(() => {\n    if (open) {\n      handleOpen();\n    } else if (!hasTransition || !closeAfterTransition) {\n      handleClose();\n    }\n  }, [open, handleClose, hasTransition, closeAfterTransition, handleOpen]);\n  const createHandleKeyDown = otherHandlers => event => {\n    var _otherHandlers$onKeyD;\n    (_otherHandlers$onKeyD = otherHandlers.onKeyDown) == null || _otherHandlers$onKeyD.call(otherHandlers, event);\n\n    // The handler doesn't take event.defaultPrevented into account:\n    //\n    // event.preventDefault() is meant to stop default behaviors like\n    // clicking a checkbox to check it, hitting a button to submit a form,\n    // and hitting left arrow to move the cursor in a text input etc.\n    // Only special HTML elements have these default behaviors.\n    if (event.key !== 'Escape' || event.which === 229 ||\n    // Wait until IME is settled.\n    !isTopModal()) {\n      return;\n    }\n    if (!disableEscapeKeyDown) {\n      // Swallow the event, in case someone is listening for the escape key on the body.\n      event.stopPropagation();\n      if (onClose) {\n        onClose(event, 'escapeKeyDown');\n      }\n    }\n  };\n  const createHandleBackdropClick = otherHandlers => event => {\n    var _otherHandlers$onClic;\n    (_otherHandlers$onClic = otherHandlers.onClick) == null || _otherHandlers$onClic.call(otherHandlers, event);\n    if (event.target !== event.currentTarget) {\n      return;\n    }\n    if (onClose) {\n      onClose(event, 'backdropClick');\n    }\n  };\n  const getRootProps = (otherHandlers = {}) => {\n    const propsEventHandlers = extractEventHandlers(parameters);\n\n    // The custom event handlers shouldn't be spread on the root element\n    delete propsEventHandlers.onTransitionEnter;\n    delete propsEventHandlers.onTransitionExited;\n    const externalEventHandlers = _extends({}, propsEventHandlers, otherHandlers);\n    return _extends({\n      role: 'presentation'\n    }, externalEventHandlers, {\n      onKeyDown: createHandleKeyDown(externalEventHandlers),\n      ref: handleRef\n    });\n  };\n  const getBackdropProps = (otherHandlers = {}) => {\n    const externalEventHandlers = otherHandlers;\n    return _extends({\n      'aria-hidden': true\n    }, externalEventHandlers, {\n      onClick: createHandleBackdropClick(externalEventHandlers),\n      open\n    });\n  };\n  const getTransitionProps = () => {\n    const handleEnter = () => {\n      setExited(false);\n      if (onTransitionEnter) {\n        onTransitionEnter();\n      }\n    };\n    const handleExited = () => {\n      setExited(true);\n      if (onTransitionExited) {\n        onTransitionExited();\n      }\n      if (closeAfterTransition) {\n        handleClose();\n      }\n    };\n    return {\n      onEnter: createChainedFunction(handleEnter, children == null ? void 0 : children.props.onEnter),\n      onExited: createChainedFunction(handleExited, children == null ? void 0 : children.props.onExited)\n    };\n  };\n  return {\n    getRootProps,\n    getBackdropProps,\n    getTransitionProps,\n    rootRef: handleRef,\n    portalRef: handlePortalRef,\n    isTopModal,\n    exited,\n    hasTransition\n  };\n}\nexport default useModal;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AAEA,YAAuB;AACvB,wBAAsB;AAEtB;AACA;AAGA;AACA,yBAA4B;AAT5B,IAAM,YAAY,CAAC,kBAAkB,UAAU,YAAY,UAAU,MAAM,WAAW,aAAa,cAAc,UAAU,YAAY,aAAa,SAAS,WAAW,qBAAqB;AAU7L,IAAM,SAAS;AAAA,EACb,UAAU;AAAA,IACR,SAAS;AAAA,EACX;AAAA,EACA,SAAS;AAAA,IACP,SAAS;AAAA,EACX;AACF;AAMA,IAAM,OAA0B,iBAAW,SAASA,MAAK,OAAO,KAAK;AACnE,QAAM,QAAQ,SAAS;AACvB,QAAM,iBAAiB;AAAA,IACrB,OAAO,MAAM,YAAY,SAAS;AAAA,IAClC,MAAM,MAAM,YAAY,SAAS;AAAA,EACnC;AACA,QAAM;AAAA,IACF;AAAA,IACA,SAAS;AAAA,IACT;AAAA,IACA;AAAA,IACA,IAAI;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU;AAAA;AAAA,IAEV,sBAAsB;AAAA,EACxB,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM,yBAAyB;AAC/B,QAAM,UAAgB,aAAO,IAAI;AACjC,QAAM,YAAY,mBAAW,SAAS,mBAAmB,QAAQ,GAAG,GAAG;AACvE,QAAM,+BAA+B,cAAY,sBAAoB;AACnE,QAAI,UAAU;AACZ,YAAM,OAAO,QAAQ;AAGrB,UAAI,qBAAqB,QAAW;AAClC,iBAAS,IAAI;AAAA,MACf,OAAO;AACL,iBAAS,MAAM,gBAAgB;AAAA,MACjC;AAAA,IACF;AAAA,EACF;AACA,QAAM,iBAAiB,6BAA6B,UAAU;AAC9D,QAAM,cAAc,6BAA6B,CAAC,MAAM,gBAAgB;AACtE,WAAO,IAAI;AAEX,UAAM,kBAAkB,mBAAmB;AAAA,MACzC;AAAA,MACA;AAAA,MACA;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AACD,SAAK,MAAM,mBAAmB,MAAM,YAAY,OAAO,WAAW,eAAe;AACjF,SAAK,MAAM,aAAa,MAAM,YAAY,OAAO,WAAW,eAAe;AAC3E,QAAI,SAAS;AACX,cAAQ,MAAM,WAAW;AAAA,IAC3B;AAAA,EACF,CAAC;AACD,QAAM,gBAAgB,6BAA6B,SAAS;AAC5D,QAAM,gBAAgB,6BAA6B,SAAS;AAC5D,QAAM,aAAa,6BAA6B,UAAQ;AACtD,UAAM,kBAAkB,mBAAmB;AAAA,MACzC;AAAA,MACA;AAAA,MACA;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AACD,SAAK,MAAM,mBAAmB,MAAM,YAAY,OAAO,WAAW,eAAe;AACjF,SAAK,MAAM,aAAa,MAAM,YAAY,OAAO,WAAW,eAAe;AAC3E,QAAI,QAAQ;AACV,aAAO,IAAI;AAAA,IACb;AAAA,EACF,CAAC;AACD,QAAM,eAAe,6BAA6B,QAAQ;AAC1D,QAAM,uBAAuB,UAAQ;AACnC,QAAI,gBAAgB;AAElB,qBAAe,QAAQ,SAAS,IAAI;AAAA,IACtC;AAAA,EACF;AACA,aAAoB,mBAAAC,KAAK,qBAAqB,SAAS;AAAA,IACrD;AAAA,IACA,IAAI;AAAA,IACJ,SAAS,yBAAyB,UAAU;AAAA,IAC5C,SAAS;AAAA,IACT,WAAW;AAAA,IACX,YAAY;AAAA,IACZ,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB;AAAA,EACF,GAAG,OAAO;AAAA,IACR,UAAU,CAAC,OAAO,eAAe;AAC/B,aAA0B,mBAAa,UAAU,SAAS;AAAA,QACxD,OAAO,SAAS;AAAA,UACd,SAAS;AAAA,UACT,YAAY,UAAU,YAAY,CAAC,SAAS,WAAW;AAAA,QACzD,GAAG,OAAO,KAAK,GAAG,OAAO,SAAS,MAAM,KAAK;AAAA,QAC7C,KAAK;AAAA,MACP,GAAG,UAAU,CAAC;AAAA,IAChB;AAAA,EACF,CAAC,CAAC;AACJ,CAAC;AACD,OAAwC,KAAK,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU9E,gBAAgB,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM1B,QAAQ,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIlB,UAAU,4BAAoB;AAAA;AAAA;AAAA;AAAA;AAAA,EAK9B,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM;AAAA,IAC3C,OAAO,kBAAAA,QAAU;AAAA,IACjB,MAAM,kBAAAA,QAAU;AAAA,EAClB,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAIrB,IAAI,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAId,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,YAAY,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAItB,QAAQ,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIlB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASjB,SAAS,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM;AAAA,IAC9D,QAAQ,kBAAAA,QAAU;AAAA,IAClB,OAAO,kBAAAA,QAAU;AAAA,IACjB,MAAM,kBAAAA,QAAU;AAAA,EAClB,CAAC,CAAC,CAAC;AACL,IAAI;AACJ,IAAO,eAAQ;;;AC7Mf;AACA;AACO,SAAS,wBAAwB,MAAM;AAC5C,SAAO,qBAAqB,eAAe,IAAI;AACjD;AACA,IAAM,kBAAkB,uBAAuB,eAAe,CAAC,QAAQ,WAAW,CAAC;AACnF,IAAO,0BAAQ;;;ACJf;AACA;AAEA,IAAAC,SAAuB;AACvB,IAAAC,qBAAsB;AACtB;AACA;AACA;AACA;AAGA,IAAAC,sBAA4B;AAT5B,IAAMC,aAAY,CAAC,YAAY,aAAa,aAAa,cAAc,mBAAmB,aAAa,QAAQ,aAAa,SAAS,uBAAuB,oBAAoB;AAUhL,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,aAAa,WAAW;AAAA,EACzC;AACA,SAAO,eAAe,OAAO,yBAAyB,OAAO;AAC/D;AACA,IAAM,eAAe,eAAO,OAAO;AAAA,EACjC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAOC,YAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAACA,QAAO,MAAM,WAAW,aAAaA,QAAO,SAAS;AAAA,EAC/D;AACF,CAAC,EAAE,CAAC;AAAA,EACF;AACF,MAAM,SAAS;AAAA,EACb,UAAU;AAAA,EACV,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,gBAAgB;AAAA,EAChB,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,MAAM;AAAA,EACN,iBAAiB;AAAA,EACjB,yBAAyB;AAC3B,GAAG,WAAW,aAAa;AAAA,EACzB,iBAAiB;AACnB,CAAC,CAAC;AACF,IAAM,WAA8B,kBAAW,SAASC,UAAS,SAAS,KAAK;AAC7E,MAAI,iBAAiB,MAAM;AAC3B,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ,aAAa,CAAC;AAAA,IACd,kBAAkB,CAAC;AAAA,IACnB,YAAY;AAAA,IACZ;AAAA,IACA,YAAY,CAAC;AAAA,IACb,QAAQ,CAAC;AAAA,IACT,sBAAsB;AAAA,IACtB;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAOF,UAAS;AACxD,QAAM,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,IACrC;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,iBAAiB,kBAAkB,UAAU,SAAS,OAAO,kBAAkB,gBAAgB;AACrG,aAAoB,oBAAAG,KAAK,qBAAqB,SAAS;AAAA,IACrD,IAAI;AAAA,IACJ,SAAS;AAAA,EACX,GAAG,OAAO;AAAA,IACR,cAAuB,oBAAAA,KAAK,cAAc,SAAS;AAAA,MACjD,eAAe;AAAA,IACjB,GAAG,eAAe;AAAA,MAChB,KAAK,QAAQ,cAAc,MAAM,SAAS,OAAO,cAAc,WAAW,SAAS,OAAO,OAAO;AAAA,MACjG,WAAW,aAAK,QAAQ,MAAM,WAAW,iBAAiB,OAAO,SAAS,cAAc,SAAS;AAAA,MACjG,YAAY,SAAS,CAAC,GAAG,YAAY,iBAAiB,OAAO,SAAS,cAAc,UAAU;AAAA,MAC9F;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC,CAAC;AAAA,EACJ,CAAC,CAAC;AACJ,CAAC;AACD,OAAwC,SAAS,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQlF,UAAU,mBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASrB,YAAY,mBAAAA,QAAU,MAAM;AAAA,IAC1B,MAAM,mBAAAA,QAAU;AAAA,EAClB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUD,iBAAiB,mBAAAA,QAAU,MAAM;AAAA,IAC/B,MAAM,mBAAAA,QAAU;AAAA,EAClB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,MAAM,mBAAAA,QAAU,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASrB,WAAW,mBAAAA,QAAU,MAAM;AAAA,IACzB,MAAM,mBAAAA,QAAU;AAAA,EAClB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,OAAO,mBAAAA,QAAU,MAAM;AAAA,IACrB,MAAM,mBAAAA,QAAU;AAAA,EAClB,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtJ,qBAAqB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/B,oBAAoB,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,MAAM;AAAA,IACzE,QAAQ,mBAAAA,QAAU;AAAA,IAClB,OAAO,mBAAAA,QAAU;AAAA,IACjB,MAAM,mBAAAA,QAAU;AAAA,EAClB,CAAC,CAAC,CAAC;AACL,IAAI;AACJ,IAAO,mBAAQ;;;AC3Lf;AAEA,SAAS,cAAc,WAAW;AAChC,QAAM,MAAM,cAAc,SAAS;AACnC,MAAI,IAAI,SAAS,WAAW;AAC1B,WAAO,YAAY,SAAS,EAAE,aAAa,IAAI,gBAAgB;AAAA,EACjE;AACA,SAAO,UAAU,eAAe,UAAU;AAC5C;AACO,SAAS,WAAW,SAAS,MAAM;AACxC,MAAI,MAAM;AACR,YAAQ,aAAa,eAAe,MAAM;AAAA,EAC5C,OAAO;AACL,YAAQ,gBAAgB,aAAa;AAAA,EACvC;AACF;AACA,SAAS,gBAAgB,SAAS;AAChC,SAAO,SAAS,YAAY,OAAO,EAAE,iBAAiB,OAAO,EAAE,cAAc,EAAE,KAAK;AACtF;AACA,SAAS,+BAA+B,SAAS;AAI/C,QAAM,oBAAoB,CAAC,YAAY,UAAU,SAAS,QAAQ,OAAO,QAAQ,YAAY,WAAW,OAAO,YAAY,SAAS,QAAQ,UAAU,OAAO;AAC7J,QAAM,qBAAqB,kBAAkB,QAAQ,QAAQ,OAAO,MAAM;AAC1E,QAAM,gBAAgB,QAAQ,YAAY,WAAW,QAAQ,aAAa,MAAM,MAAM;AACtF,SAAO,sBAAsB;AAC/B;AACA,SAAS,mBAAmB,WAAW,cAAc,gBAAgB,mBAAmB,MAAM;AAC5F,QAAM,YAAY,CAAC,cAAc,gBAAgB,GAAG,iBAAiB;AACrE,GAAC,EAAE,QAAQ,KAAK,UAAU,UAAU,aAAW;AAC7C,UAAM,uBAAuB,UAAU,QAAQ,OAAO,MAAM;AAC5D,UAAM,wBAAwB,CAAC,+BAA+B,OAAO;AACrE,QAAI,wBAAwB,uBAAuB;AACjD,iBAAW,SAAS,IAAI;AAAA,IAC1B;AAAA,EACF,CAAC;AACH;AACA,SAAS,YAAY,OAAO,UAAU;AACpC,MAAI,MAAM;AACV,QAAM,KAAK,CAAC,MAAM,UAAU;AAC1B,QAAI,SAAS,IAAI,GAAG;AAClB,YAAM;AACN,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT,CAAC;AACD,SAAO;AACT;AACA,SAAS,gBAAgB,eAAe,OAAO;AAC7C,QAAM,eAAe,CAAC;AACtB,QAAM,YAAY,cAAc;AAChC,MAAI,CAAC,MAAM,mBAAmB;AAC5B,QAAI,cAAc,SAAS,GAAG;AAE5B,YAAM,gBAAgB,iBAAiB,cAAc,SAAS,CAAC;AAC/D,mBAAa,KAAK;AAAA,QAChB,OAAO,UAAU,MAAM;AAAA,QACvB,UAAU;AAAA,QACV,IAAI;AAAA,MACN,CAAC;AAED,gBAAU,MAAM,eAAe,GAAG,gBAAgB,SAAS,IAAI,aAAa;AAG5E,YAAM,gBAAgB,cAAc,SAAS,EAAE,iBAAiB,YAAY;AAC5E,OAAC,EAAE,QAAQ,KAAK,eAAe,aAAW;AACxC,qBAAa,KAAK;AAAA,UAChB,OAAO,QAAQ,MAAM;AAAA,UACrB,UAAU;AAAA,UACV,IAAI;AAAA,QACN,CAAC;AACD,gBAAQ,MAAM,eAAe,GAAG,gBAAgB,OAAO,IAAI,aAAa;AAAA,MAC1E,CAAC;AAAA,IACH;AACA,QAAI;AACJ,QAAI,UAAU,sBAAsB,kBAAkB;AACpD,wBAAkB,cAAc,SAAS,EAAE;AAAA,IAC7C,OAAO;AAGL,YAAM,SAAS,UAAU;AACzB,YAAM,kBAAkB,YAAY,SAAS;AAC7C,yBAAmB,UAAU,OAAO,SAAS,OAAO,cAAc,UAAU,gBAAgB,iBAAiB,MAAM,EAAE,cAAc,WAAW,SAAS;AAAA,IACzJ;AAIA,iBAAa,KAAK;AAAA,MAChB,OAAO,gBAAgB,MAAM;AAAA,MAC7B,UAAU;AAAA,MACV,IAAI;AAAA,IACN,GAAG;AAAA,MACD,OAAO,gBAAgB,MAAM;AAAA,MAC7B,UAAU;AAAA,MACV,IAAI;AAAA,IACN,GAAG;AAAA,MACD,OAAO,gBAAgB,MAAM;AAAA,MAC7B,UAAU;AAAA,MACV,IAAI;AAAA,IACN,CAAC;AACD,oBAAgB,MAAM,WAAW;AAAA,EACnC;AACA,QAAM,UAAU,MAAM;AACpB,iBAAa,QAAQ,CAAC;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,IACF,MAAM;AACJ,UAAI,OAAO;AACT,WAAG,MAAM,YAAY,UAAU,KAAK;AAAA,MACtC,OAAO;AACL,WAAG,MAAM,eAAe,QAAQ;AAAA,MAClC;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,WAAW;AACpC,QAAM,iBAAiB,CAAC;AACxB,GAAC,EAAE,QAAQ,KAAK,UAAU,UAAU,aAAW;AAC7C,QAAI,QAAQ,aAAa,aAAa,MAAM,QAAQ;AAClD,qBAAe,KAAK,OAAO;AAAA,IAC7B;AAAA,EACF,CAAC;AACD,SAAO;AACT;AAQO,IAAM,eAAN,MAAmB;AAAA,EACxB,cAAc;AACZ,SAAK,aAAa;AAClB,SAAK,SAAS;AACd,SAAK,SAAS,CAAC;AACf,SAAK,aAAa,CAAC;AAAA,EACrB;AAAA,EACA,IAAI,OAAO,WAAW;AACpB,QAAI,aAAa,KAAK,OAAO,QAAQ,KAAK;AAC1C,QAAI,eAAe,IAAI;AACrB,aAAO;AAAA,IACT;AACA,iBAAa,KAAK,OAAO;AACzB,SAAK,OAAO,KAAK,KAAK;AAGtB,QAAI,MAAM,UAAU;AAClB,iBAAW,MAAM,UAAU,KAAK;AAAA,IAClC;AACA,UAAM,iBAAiB,kBAAkB,SAAS;AAClD,uBAAmB,WAAW,MAAM,OAAO,MAAM,UAAU,gBAAgB,IAAI;AAC/E,UAAM,iBAAiB,YAAY,KAAK,YAAY,UAAQ,KAAK,cAAc,SAAS;AACxF,QAAI,mBAAmB,IAAI;AACzB,WAAK,WAAW,cAAc,EAAE,OAAO,KAAK,KAAK;AACjD,aAAO;AAAA,IACT;AACA,SAAK,WAAW,KAAK;AAAA,MACnB,QAAQ,CAAC,KAAK;AAAA,MACd;AAAA,MACA,SAAS;AAAA,MACT;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,MAAM,OAAO,OAAO;AAClB,UAAM,iBAAiB,YAAY,KAAK,YAAY,UAAQ,KAAK,OAAO,QAAQ,KAAK,MAAM,EAAE;AAC7F,UAAM,gBAAgB,KAAK,WAAW,cAAc;AACpD,QAAI,CAAC,cAAc,SAAS;AAC1B,oBAAc,UAAU,gBAAgB,eAAe,KAAK;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,OAAO,OAAO,kBAAkB,MAAM;AACpC,UAAM,aAAa,KAAK,OAAO,QAAQ,KAAK;AAC5C,QAAI,eAAe,IAAI;AACrB,aAAO;AAAA,IACT;AACA,UAAM,iBAAiB,YAAY,KAAK,YAAY,UAAQ,KAAK,OAAO,QAAQ,KAAK,MAAM,EAAE;AAC7F,UAAM,gBAAgB,KAAK,WAAW,cAAc;AACpD,kBAAc,OAAO,OAAO,cAAc,OAAO,QAAQ,KAAK,GAAG,CAAC;AAClE,SAAK,OAAO,OAAO,YAAY,CAAC;AAGhC,QAAI,cAAc,OAAO,WAAW,GAAG;AAErC,UAAI,cAAc,SAAS;AACzB,sBAAc,QAAQ;AAAA,MACxB;AACA,UAAI,MAAM,UAAU;AAElB,mBAAW,MAAM,UAAU,eAAe;AAAA,MAC5C;AACA,yBAAmB,cAAc,WAAW,MAAM,OAAO,MAAM,UAAU,cAAc,gBAAgB,KAAK;AAC5G,WAAK,WAAW,OAAO,gBAAgB,CAAC;AAAA,IAC1C,OAAO;AAEL,YAAM,UAAU,cAAc,OAAO,cAAc,OAAO,SAAS,CAAC;AAIpE,UAAI,QAAQ,UAAU;AACpB,mBAAW,QAAQ,UAAU,KAAK;AAAA,MACpC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW,OAAO;AAChB,WAAO,KAAK,OAAO,SAAS,KAAK,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC,MAAM;AAAA,EAC3E;AACF;;;ACjNA,IAAAC,SAAuB;AACvB,IAAAC,qBAAsB;AACtB;AACA,IAAAC,sBAA4B;AAC5B,IAAAA,sBAA8B;AAE9B,IAAM,qBAAqB,CAAC,SAAS,UAAU,YAAY,WAAW,UAAU,cAAc,mBAAmB,mBAAmB,kDAAkD,EAAE,KAAK,GAAG;AAChM,SAAS,YAAY,MAAM;AACzB,QAAM,eAAe,SAAS,KAAK,aAAa,UAAU,KAAK,IAAI,EAAE;AACrE,MAAI,CAAC,OAAO,MAAM,YAAY,GAAG;AAC/B,WAAO;AAAA,EACT;AAUA,MAAI,KAAK,oBAAoB,WAAW,KAAK,aAAa,WAAW,KAAK,aAAa,WAAW,KAAK,aAAa,cAAc,KAAK,aAAa,UAAU,MAAM,MAAM;AACxK,WAAO;AAAA,EACT;AACA,SAAO,KAAK;AACd;AACA,SAAS,mBAAmB,MAAM;AAChC,MAAI,KAAK,YAAY,WAAW,KAAK,SAAS,SAAS;AACrD,WAAO;AAAA,EACT;AACA,MAAI,CAAC,KAAK,MAAM;AACd,WAAO;AAAA,EACT;AACA,QAAM,WAAW,cAAY,KAAK,cAAc,cAAc,sBAAsB,QAAQ,EAAE;AAC9F,MAAI,SAAS,SAAS,UAAU,KAAK,IAAI,YAAY;AACrD,MAAI,CAAC,QAAQ;AACX,aAAS,SAAS,UAAU,KAAK,IAAI,IAAI;AAAA,EAC3C;AACA,SAAO,WAAW;AACpB;AACA,SAAS,gCAAgC,MAAM;AAC7C,MAAI,KAAK,YAAY,KAAK,YAAY,WAAW,KAAK,SAAS,YAAY,mBAAmB,IAAI,GAAG;AACnG,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,mBAAmB,MAAM;AAChC,QAAM,kBAAkB,CAAC;AACzB,QAAM,kBAAkB,CAAC;AACzB,QAAM,KAAK,KAAK,iBAAiB,kBAAkB,CAAC,EAAE,QAAQ,CAAC,MAAM,MAAM;AACzE,UAAM,eAAe,YAAY,IAAI;AACrC,QAAI,iBAAiB,MAAM,CAAC,gCAAgC,IAAI,GAAG;AACjE;AAAA,IACF;AACA,QAAI,iBAAiB,GAAG;AACtB,sBAAgB,KAAK,IAAI;AAAA,IAC3B,OAAO;AACL,sBAAgB,KAAK;AAAA,QACnB,eAAe;AAAA,QACf,UAAU;AAAA,QACV;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,SAAO,gBAAgB,KAAK,CAAC,GAAG,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,WAAW,EAAE,QAAQ,EAAE,IAAI,OAAK,EAAE,IAAI,EAAE,OAAO,eAAe;AACxK;AACA,SAAS,mBAAmB;AAC1B,SAAO;AACT;AAKA,SAAS,UAAU,OAAO;AACxB,QAAM;AAAA,IACJ;AAAA,IACA,mBAAmB;AAAA,IACnB,sBAAsB;AAAA,IACtB,sBAAsB;AAAA,IACtB,cAAc;AAAA,IACd,YAAY;AAAA,IACZ;AAAA,EACF,IAAI;AACJ,QAAM,yBAA+B,cAAO,KAAK;AACjD,QAAM,gBAAsB,cAAO,IAAI;AACvC,QAAM,cAAoB,cAAO,IAAI;AACrC,QAAM,gBAAsB,cAAO,IAAI;AACvC,QAAM,wBAA8B,cAAO,IAAI;AAG/C,QAAM,YAAkB,cAAO,KAAK;AACpC,QAAM,UAAgB,cAAO,IAAI;AACjC,QAAM,YAAY,WAAW,mBAAmB,QAAQ,GAAG,OAAO;AAClE,QAAM,cAAoB,cAAO,IAAI;AACrC,EAAM,iBAAU,MAAM;AAEpB,QAAI,CAAC,QAAQ,CAAC,QAAQ,SAAS;AAC7B;AAAA,IACF;AACA,cAAU,UAAU,CAAC;AAAA,EACvB,GAAG,CAAC,kBAAkB,IAAI,CAAC;AAC3B,EAAM,iBAAU,MAAM;AAEpB,QAAI,CAAC,QAAQ,CAAC,QAAQ,SAAS;AAC7B;AAAA,IACF;AACA,UAAM,MAAM,cAAc,QAAQ,OAAO;AACzC,QAAI,CAAC,QAAQ,QAAQ,SAAS,IAAI,aAAa,GAAG;AAChD,UAAI,CAAC,QAAQ,QAAQ,aAAa,UAAU,GAAG;AAC7C,YAAI,MAAuC;AACzC,kBAAQ,MAAM,CAAC,sDAAsD,2FAAgG,EAAE,KAAK,IAAI,CAAC;AAAA,QACnL;AACA,gBAAQ,QAAQ,aAAa,YAAY,IAAI;AAAA,MAC/C;AACA,UAAI,UAAU,SAAS;AACrB,gBAAQ,QAAQ,MAAM;AAAA,MACxB;AAAA,IACF;AACA,WAAO,MAAM;AAEX,UAAI,CAAC,qBAAqB;AAKxB,YAAI,cAAc,WAAW,cAAc,QAAQ,OAAO;AACxD,iCAAuB,UAAU;AACjC,wBAAc,QAAQ,MAAM;AAAA,QAC9B;AACA,sBAAc,UAAU;AAAA,MAC1B;AAAA,IACF;AAAA,EAIF,GAAG,CAAC,IAAI,CAAC;AACT,EAAM,iBAAU,MAAM;AAEpB,QAAI,CAAC,QAAQ,CAAC,QAAQ,SAAS;AAC7B;AAAA,IACF;AACA,UAAM,MAAM,cAAc,QAAQ,OAAO;AACzC,UAAM,YAAY,iBAAe;AAC/B,kBAAY,UAAU;AACtB,UAAI,uBAAuB,CAAC,UAAU,KAAK,YAAY,QAAQ,OAAO;AACpE;AAAA,MACF;AAIA,UAAI,IAAI,kBAAkB,QAAQ,WAAW,YAAY,UAAU;AAGjE,+BAAuB,UAAU;AACjC,YAAI,YAAY,SAAS;AACvB,sBAAY,QAAQ,MAAM;AAAA,QAC5B;AAAA,MACF;AAAA,IACF;AACA,UAAM,UAAU,MAAM;AACpB,YAAM,cAAc,QAAQ;AAI5B,UAAI,gBAAgB,MAAM;AACxB;AAAA,MACF;AACA,UAAI,CAAC,IAAI,SAAS,KAAK,CAAC,UAAU,KAAK,uBAAuB,SAAS;AACrE,+BAAuB,UAAU;AACjC;AAAA,MACF;AAGA,UAAI,YAAY,SAAS,IAAI,aAAa,GAAG;AAC3C;AAAA,MACF;AAGA,UAAI,uBAAuB,IAAI,kBAAkB,cAAc,WAAW,IAAI,kBAAkB,YAAY,SAAS;AACnH;AAAA,MACF;AAGA,UAAI,IAAI,kBAAkB,sBAAsB,SAAS;AACvD,8BAAsB,UAAU;AAAA,MAClC,WAAW,sBAAsB,YAAY,MAAM;AACjD;AAAA,MACF;AACA,UAAI,CAAC,UAAU,SAAS;AACtB;AAAA,MACF;AACA,UAAI,WAAW,CAAC;AAChB,UAAI,IAAI,kBAAkB,cAAc,WAAW,IAAI,kBAAkB,YAAY,SAAS;AAC5F,mBAAW,YAAY,QAAQ,OAAO;AAAA,MACxC;AAIA,UAAI,SAAS,SAAS,GAAG;AACvB,YAAI,sBAAsB;AAC1B,cAAM,aAAa,UAAU,uBAAuB,YAAY,YAAY,OAAO,SAAS,qBAAqB,eAAe,wBAAwB,YAAY,YAAY,OAAO,SAAS,sBAAsB,SAAS,KAAK;AACpO,cAAM,YAAY,SAAS,CAAC;AAC5B,cAAM,gBAAgB,SAAS,SAAS,SAAS,CAAC;AAClD,YAAI,OAAO,cAAc,YAAY,OAAO,kBAAkB,UAAU;AACtE,cAAI,YAAY;AACd,0BAAc,MAAM;AAAA,UACtB,OAAO;AACL,sBAAU,MAAM;AAAA,UAClB;AAAA,QACF;AAAA,MAEF,OAAO;AACL,oBAAY,MAAM;AAAA,MACpB;AAAA,IACF;AACA,QAAI,iBAAiB,WAAW,OAAO;AACvC,QAAI,iBAAiB,WAAW,WAAW,IAAI;AAQ/C,UAAM,WAAW,YAAY,MAAM;AACjC,UAAI,IAAI,iBAAiB,IAAI,cAAc,YAAY,QAAQ;AAC7D,gBAAQ;AAAA,MACV;AAAA,IACF,GAAG,EAAE;AACL,WAAO,MAAM;AACX,oBAAc,QAAQ;AACtB,UAAI,oBAAoB,WAAW,OAAO;AAC1C,UAAI,oBAAoB,WAAW,WAAW,IAAI;AAAA,IACpD;AAAA,EACF,GAAG,CAAC,kBAAkB,qBAAqB,qBAAqB,WAAW,MAAM,WAAW,CAAC;AAC7F,QAAM,UAAU,WAAS;AACvB,QAAI,cAAc,YAAY,MAAM;AAClC,oBAAc,UAAU,MAAM;AAAA,IAChC;AACA,cAAU,UAAU;AACpB,0BAAsB,UAAU,MAAM;AACtC,UAAM,uBAAuB,SAAS,MAAM;AAC5C,QAAI,sBAAsB;AACxB,2BAAqB,KAAK;AAAA,IAC5B;AAAA,EACF;AACA,QAAM,sBAAsB,WAAS;AACnC,QAAI,cAAc,YAAY,MAAM;AAClC,oBAAc,UAAU,MAAM;AAAA,IAChC;AACA,cAAU,UAAU;AAAA,EACtB;AACA,aAAoB,oBAAAC,MAAY,iBAAU;AAAA,IACxC,UAAU,KAAc,oBAAAC,KAAK,OAAO;AAAA,MAClC,UAAU,OAAO,IAAI;AAAA,MACrB,SAAS;AAAA,MACT,KAAK;AAAA,MACL,eAAe;AAAA,IACjB,CAAC,GAAsB,oBAAa,UAAU;AAAA,MAC5C,KAAK;AAAA,MACL;AAAA,IACF,CAAC,OAAgB,oBAAAA,KAAK,OAAO;AAAA,MAC3B,UAAU,OAAO,IAAI;AAAA,MACrB,SAAS;AAAA,MACT,KAAK;AAAA,MACL,eAAe;AAAA,IACjB,CAAC,CAAC;AAAA,EACJ,CAAC;AACH;AACA,OAAwC,UAAU,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQnF,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUV,kBAAkB,mBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ5B,qBAAqB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM/B,qBAAqB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM/B,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUvB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,MAAM,mBAAAA,QAAU,KAAK;AACvB,IAAI;AACJ,IAAI,MAAuC;AAEzC,YAAU,WAAgB,IAAI,UAAU,UAAU,SAAS;AAC7D;AACA,IAAO,oBAAQ;;;AC1Uf;AACA;AACO,SAAS,qBAAqB,MAAM;AACzC,SAAO,qBAAqB,YAAY,IAAI;AAC9C;AACA,IAAM,eAAe,uBAAuB,YAAY,CAAC,QAAQ,UAAU,UAAU,CAAC;AACtF,IAAO,uBAAQ;;;ACJf;AACA;AAEA,IAAAC,SAAuB;AACvB,IAAAC,qBAAsB;AACtB;AACA;AACA;AACA;AACA;AAGA;AACA;;;ACbA;AACA,IAAAC,SAAuB;AACvB;AACA;AAEA,SAAS,aAAa,WAAW;AAC/B,SAAO,OAAO,cAAc,aAAa,UAAU,IAAI;AACzD;AACA,SAAS,iBAAiB,UAAU;AAClC,SAAO,WAAW,SAAS,MAAM,eAAe,IAAI,IAAI;AAC1D;AAIA,IAAM,iBAAiB,IAAI,aAAa;AAWxC,SAAS,SAAS,YAAY;AAC5B,QAAM;AAAA,IACJ;AAAA,IACA,uBAAuB;AAAA,IACvB,oBAAoB;AAAA;AAAA,IAEpB,UAAU;AAAA,IACV,uBAAuB;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAGJ,QAAM,QAAc,cAAO,CAAC,CAAC;AAC7B,QAAM,eAAqB,cAAO,IAAI;AACtC,QAAM,WAAiB,cAAO,IAAI;AAClC,QAAM,YAAY,WAAW,UAAU,OAAO;AAC9C,QAAM,CAAC,QAAQ,SAAS,IAAU,gBAAS,CAAC,IAAI;AAChD,QAAM,gBAAgB,iBAAiB,QAAQ;AAC/C,MAAI,iBAAiB;AACrB,MAAI,WAAW,aAAa,MAAM,WAAW,WAAW,aAAa,MAAM,OAAO;AAChF,qBAAiB;AAAA,EACnB;AACA,QAAM,SAAS,MAAM,cAAc,aAAa,OAAO;AACvD,QAAM,WAAW,MAAM;AACrB,UAAM,QAAQ,WAAW,SAAS;AAClC,UAAM,QAAQ,QAAQ,aAAa;AACnC,WAAO,MAAM;AAAA,EACf;AACA,QAAM,gBAAgB,MAAM;AAC1B,YAAQ,MAAM,SAAS,GAAG;AAAA,MACxB;AAAA,IACF,CAAC;AAGD,QAAI,SAAS,SAAS;AACpB,eAAS,QAAQ,YAAY;AAAA,IAC/B;AAAA,EACF;AACA,QAAM,aAAa,yBAAiB,MAAM;AACxC,UAAM,oBAAoB,aAAa,SAAS,KAAK,OAAO,EAAE;AAC9D,YAAQ,IAAI,SAAS,GAAG,iBAAiB;AAGzC,QAAI,SAAS,SAAS;AACpB,oBAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACD,QAAM,aAAmB,mBAAY,MAAM,QAAQ,WAAW,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC;AACpF,QAAM,kBAAkB,yBAAiB,UAAQ;AAC/C,iBAAa,UAAU;AACvB,QAAI,CAAC,MAAM;AACT;AAAA,IACF;AACA,QAAI,QAAQ,WAAW,GAAG;AACxB,oBAAc;AAAA,IAChB,WAAW,SAAS,SAAS;AAC3B,iBAAW,SAAS,SAAS,cAAc;AAAA,IAC7C;AAAA,EACF,CAAC;AACD,QAAM,cAAoB,mBAAY,MAAM;AAC1C,YAAQ,OAAO,SAAS,GAAG,cAAc;AAAA,EAC3C,GAAG,CAAC,gBAAgB,OAAO,CAAC;AAC5B,EAAM,iBAAU,MAAM;AACpB,WAAO,MAAM;AACX,kBAAY;AAAA,IACd;AAAA,EACF,GAAG,CAAC,WAAW,CAAC;AAChB,EAAM,iBAAU,MAAM;AACpB,QAAI,MAAM;AACR,iBAAW;AAAA,IACb,WAAW,CAAC,iBAAiB,CAAC,sBAAsB;AAClD,kBAAY;AAAA,IACd;AAAA,EACF,GAAG,CAAC,MAAM,aAAa,eAAe,sBAAsB,UAAU,CAAC;AACvE,QAAM,sBAAsB,mBAAiB,WAAS;AACpD,QAAI;AACJ,KAAC,wBAAwB,cAAc,cAAc,QAAQ,sBAAsB,KAAK,eAAe,KAAK;AAQ5G,QAAI,MAAM,QAAQ,YAAY,MAAM,UAAU;AAAA,IAE9C,CAAC,WAAW,GAAG;AACb;AAAA,IACF;AACA,QAAI,CAAC,sBAAsB;AAEzB,YAAM,gBAAgB;AACtB,UAAI,SAAS;AACX,gBAAQ,OAAO,eAAe;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AACA,QAAM,4BAA4B,mBAAiB,WAAS;AAC1D,QAAI;AACJ,KAAC,wBAAwB,cAAc,YAAY,QAAQ,sBAAsB,KAAK,eAAe,KAAK;AAC1G,QAAI,MAAM,WAAW,MAAM,eAAe;AACxC;AAAA,IACF;AACA,QAAI,SAAS;AACX,cAAQ,OAAO,eAAe;AAAA,IAChC;AAAA,EACF;AACA,QAAM,eAAe,CAAC,gBAAgB,CAAC,MAAM;AAC3C,UAAM,qBAAqB,6BAAqB,UAAU;AAG1D,WAAO,mBAAmB;AAC1B,WAAO,mBAAmB;AAC1B,UAAM,wBAAwB,SAAS,CAAC,GAAG,oBAAoB,aAAa;AAC5E,WAAO,SAAS;AAAA,MACd,MAAM;AAAA,IACR,GAAG,uBAAuB;AAAA,MACxB,WAAW,oBAAoB,qBAAqB;AAAA,MACpD,KAAK;AAAA,IACP,CAAC;AAAA,EACH;AACA,QAAM,mBAAmB,CAAC,gBAAgB,CAAC,MAAM;AAC/C,UAAM,wBAAwB;AAC9B,WAAO,SAAS;AAAA,MACd,eAAe;AAAA,IACjB,GAAG,uBAAuB;AAAA,MACxB,SAAS,0BAA0B,qBAAqB;AAAA,MACxD;AAAA,IACF,CAAC;AAAA,EACH;AACA,QAAMC,sBAAqB,MAAM;AAC/B,UAAM,cAAc,MAAM;AACxB,gBAAU,KAAK;AACf,UAAI,mBAAmB;AACrB,0BAAkB;AAAA,MACpB;AAAA,IACF;AACA,UAAM,eAAe,MAAM;AACzB,gBAAU,IAAI;AACd,UAAI,oBAAoB;AACtB,2BAAmB;AAAA,MACrB;AACA,UAAI,sBAAsB;AACxB,oBAAY;AAAA,MACd;AAAA,IACF;AACA,WAAO;AAAA,MACL,SAAS,sBAAsB,aAAa,YAAY,OAAO,SAAS,SAAS,MAAM,OAAO;AAAA,MAC9F,UAAU,sBAAsB,cAAc,YAAY,OAAO,SAAS,SAAS,MAAM,QAAQ;AAAA,IACnG;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,oBAAAA;AAAA,IACA,SAAS;AAAA,IACT,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,IAAO,mBAAQ;;;AD/Kf,IAAAC,sBAA4B;AAC5B,IAAAA,sBAA8B;AAhB9B,IAAMC,aAAY,CAAC,qBAAqB,iBAAiB,WAAW,aAAa,wBAAwB,YAAY,aAAa,aAAa,cAAc,mBAAmB,oBAAoB,uBAAuB,wBAAwB,iBAAiB,uBAAuB,qBAAqB,gBAAgB,eAAe,mBAAmB,WAAW,qBAAqB,sBAAsB,QAAQ,aAAa,SAAS,OAAO;AAiB7b,IAAMC,qBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,CAAC,QAAQ,UAAU,QAAQ;AAAA,IAC1C,UAAU,CAAC,UAAU;AAAA,EACvB;AACA,SAAO,eAAe,OAAO,sBAAsB,OAAO;AAC5D;AACA,IAAM,YAAY,eAAO,OAAO;AAAA,EAC9B,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAOC,YAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAACA,QAAO,MAAM,CAAC,WAAW,QAAQ,WAAW,UAAUA,QAAO,MAAM;AAAA,EAC7E;AACF,CAAC,EAAE,CAAC;AAAA,EACF;AAAA,EACA;AACF,MAAM,SAAS;AAAA,EACb,UAAU;AAAA,EACV,SAAS,MAAM,QAAQ,OAAO,OAAO;AAAA,EACrC,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,MAAM;AACR,GAAG,CAAC,WAAW,QAAQ,WAAW,UAAU;AAAA,EAC1C,YAAY;AACd,CAAC,CAAC;AACF,IAAM,gBAAgB,eAAO,kBAAU;AAAA,EACrC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAOA,YAAW;AACpC,WAAOA,QAAO;AAAA,EAChB;AACF,CAAC,EAAE;AAAA,EACD,QAAQ;AACV,CAAC;AAeD,IAAM,QAA2B,kBAAW,SAASC,OAAM,SAAS,KAAK;AACvE,MAAI,MAAM,aAAa,OAAO,iBAAiB,iBAAiB;AAChE,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,MAAM;AAAA,IACN,OAAO;AAAA,EACT,CAAC;AACD,QAAM;AAAA,IACF,oBAAoB;AAAA,IACpB;AAAA,IACA;AAAA,IACA,uBAAuB;AAAA,IACvB;AAAA,IACA;AAAA,IACA;AAAA,IACA,aAAa,CAAC;AAAA,IACd,kBAAkB,CAAC;AAAA,IACnB,mBAAmB;AAAA,IACnB,sBAAsB;AAAA,IACtB,uBAAuB;AAAA,IACvB,gBAAgB;AAAA,IAChB,sBAAsB;AAAA,IACtB,oBAAoB;AAAA,IACpB,eAAe;AAAA,IACf,cAAc;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA,EAEF,IAAI,OACJ,QAAQ,8BAA8B,OAAOH,UAAS;AACxD,QAAM,oBAAoB,SAAS,CAAC,GAAG,OAAO;AAAA,IAC5C;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,oBAAAI;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,iBAAS,SAAS,CAAC,GAAG,mBAAmB;AAAA,IAC3C,SAAS;AAAA,EACX,CAAC,CAAC;AACF,QAAM,aAAa,SAAS,CAAC,GAAG,mBAAmB;AAAA,IACjD;AAAA,EACF,CAAC;AACD,QAAM,UAAUH,mBAAkB,UAAU;AAC5C,QAAM,aAAa,CAAC;AACpB,MAAI,SAAS,MAAM,aAAa,QAAW;AACzC,eAAW,WAAW;AAAA,EACxB;AAGA,MAAI,eAAe;AACjB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAIG,oBAAmB;AACvB,eAAW,UAAU;AACrB,eAAW,WAAW;AAAA,EACxB;AACA,QAAM,YAAY,QAAQ,cAAc,SAAS,OAAO,SAAS,MAAM,SAAS,OAAO,cAAc,WAAW,SAAS,OAAO,OAAO;AACvI,QAAM,gBAAgB,SAAS,kBAAkB,SAAS,OAAO,SAAS,MAAM,aAAa,OAAO,kBAAkB,WAAW,aAAa,OAAO,QAAQ;AAC7J,QAAM,iBAAiB,kBAAkB,aAAa,OAAO,SAAS,UAAU,SAAS,OAAO,kBAAkB,gBAAgB;AAClI,QAAM,qBAAqB,sBAAsB,aAAa,OAAO,SAAS,UAAU,aAAa,OAAO,sBAAsB,gBAAgB;AAClJ,QAAM,YAAY,qBAAa;AAAA,IAC7B,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,wBAAwB;AAAA,IACxB,cAAc;AAAA,IACd,iBAAiB;AAAA,MACf;AAAA,MACA,IAAI;AAAA,IACN;AAAA,IACA;AAAA,IACA,WAAW,aAAK,WAAW,iBAAiB,OAAO,SAAS,cAAc,WAAW,WAAW,OAAO,SAAS,QAAQ,MAAM,CAAC,WAAW,QAAQ,WAAW,WAAW,WAAW,OAAO,SAAS,QAAQ,OAAO;AAAA,EACpN,CAAC;AACD,QAAM,gBAAgB,qBAAa;AAAA,IACjC,aAAa;AAAA,IACb,mBAAmB;AAAA,IACnB,iBAAiB;AAAA,IACjB,cAAc,mBAAiB;AAC7B,aAAO,iBAAiB,SAAS,CAAC,GAAG,eAAe;AAAA,QAClD,SAAS,OAAK;AACZ,cAAI,iBAAiB;AACnB,4BAAgB,CAAC;AAAA,UACnB;AACA,cAAI,iBAAiB,QAAQ,cAAc,SAAS;AAClD,0BAAc,QAAQ,CAAC;AAAA,UACzB;AAAA,QACF;AAAA,MACF,CAAC,CAAC;AAAA,IACJ;AAAA,IACA,WAAW,aAAK,qBAAqB,OAAO,SAAS,kBAAkB,WAAW,iBAAiB,OAAO,SAAS,cAAc,WAAW,WAAW,OAAO,SAAS,QAAQ,QAAQ;AAAA,IACvL;AAAA,EACF,CAAC;AACD,MAAI,CAAC,eAAe,CAAC,SAAS,CAAC,iBAAiB,SAAS;AACvD,WAAO;AAAA,EACT;AACA,aAAoB,oBAAAC,KAAK,gBAAQ;AAAA,IAC/B,KAAK;AAAA,IACL;AAAA,IACA;AAAA,IACA,cAAuB,oBAAAC,MAAM,UAAU,SAAS,CAAC,GAAG,WAAW;AAAA,MAC7D,UAAU,CAAC,CAAC,gBAAgB,wBAAiC,oBAAAD,KAAK,cAAc,SAAS,CAAC,GAAG,aAAa,CAAC,IAAI,UAAmB,oBAAAA,KAAK,mBAAW;AAAA,QAChJ;AAAA,QACA;AAAA,QACA;AAAA,QACA,WAAW;AAAA,QACX;AAAA,QACA,UAA6B,oBAAa,UAAU,UAAU;AAAA,MAChE,CAAC,CAAC;AAAA,IACJ,CAAC,CAAC;AAAA,EACJ,CAAC;AACH,CAAC;AACD,OAAwC,MAAM,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAmB/E,mBAAmB,mBAAAE,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7B,eAAe,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIzB,UAAU,4BAAoB;AAAA;AAAA;AAAA;AAAA,EAI9B,SAAS,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,sBAAsB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhC,WAAW,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASrB,YAAY,mBAAAA,QAAU,MAAM;AAAA,IAC1B,UAAU,mBAAAA,QAAU;AAAA,IACpB,MAAM,mBAAAA,QAAU;AAAA,EAClB,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUD,iBAAiB,mBAAAA,QAAU,MAAM;AAAA,IAC/B,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,MAAM,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,EAC9D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWD,WAAW,mBAAAA,QAAgD,UAAU,CAAC,iBAAiB,mBAAAA,QAAU,IAAI,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUtG,kBAAkB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ5B,qBAAqB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/B,sBAAsB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKhC,eAAe,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMzB,qBAAqB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/B,mBAAmB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7B,cAAc,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOxB,aAAa,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvB,iBAAiB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ3B,SAAS,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,mBAAmB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI7B,oBAAoB,mBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI9B,MAAM,mBAAAA,QAAU,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,mBAAAA,QAAU,MAAM;AAAA,IACzB,UAAU,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,IAChE,MAAM,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AAAA,EAC9D,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,OAAO,mBAAAA,QAAU,MAAM;AAAA,IACrB,UAAU,mBAAAA,QAAU;AAAA,IACpB,MAAM,mBAAAA,QAAU;AAAA,EAClB,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,IAAI,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,UAAU,CAAC,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,QAAQ,mBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,mBAAAA,QAAU,MAAM,mBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;AACJ,IAAO,gBAAQ;", "names": ["Fade", "_jsx", "PropTypes", "React", "import_prop_types", "import_jsx_runtime", "_excluded", "styles", "Backdrop", "_jsx", "PropTypes", "React", "import_prop_types", "import_jsx_runtime", "_jsxs", "_jsx", "PropTypes", "React", "import_prop_types", "React", "getTransitionProps", "import_jsx_runtime", "_excluded", "useUtilityClasses", "styles", "Modal", "getTransitionProps", "_jsx", "_jsxs", "PropTypes"]}