{"version": 3, "sources": ["../../@mui/material/FormControlLabel/FormControlLabel.js", "../../@mui/material/FormControlLabel/formControlLabelClasses.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"checked\", \"className\", \"componentsProps\", \"control\", \"disabled\", \"disableTypography\", \"inputRef\", \"label\", \"labelPlacement\", \"name\", \"onChange\", \"required\", \"slotProps\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport refType from '@mui/utils/refType';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { useFormControl } from '../FormControl';\nimport Stack from '../Stack';\nimport Typography from '../Typography';\nimport capitalize from '../utils/capitalize';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport formControlLabelClasses, { getFormControlLabelUtilityClasses } from './formControlLabelClasses';\nimport formControlState from '../FormControl/formControlState';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disabled,\n    labelPlacement,\n    error,\n    required\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', `labelPlacement${capitalize(labelPlacement)}`, error && 'error', required && 'required'],\n    label: ['label', disabled && 'disabled'],\n    asterisk: ['asterisk', error && 'error']\n  };\n  return composeClasses(slots, getFormControlLabelUtilityClasses, classes);\n};\nexport const FormControlLabelRoot = styled('label', {\n  name: 'MuiFormControlLabel',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${formControlLabelClasses.label}`]: styles.label\n    }, styles.root, styles[`labelPlacement${capitalize(ownerState.labelPlacement)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  display: 'inline-flex',\n  alignItems: 'center',\n  cursor: 'pointer',\n  // For correct alignment with the text.\n  verticalAlign: 'middle',\n  WebkitTapHighlightColor: 'transparent',\n  marginLeft: -11,\n  marginRight: 16,\n  // used for row presentation of radio/checkbox\n  [`&.${formControlLabelClasses.disabled}`]: {\n    cursor: 'default'\n  }\n}, ownerState.labelPlacement === 'start' && {\n  flexDirection: 'row-reverse',\n  marginLeft: 16,\n  // used for row presentation of radio/checkbox\n  marginRight: -11\n}, ownerState.labelPlacement === 'top' && {\n  flexDirection: 'column-reverse',\n  marginLeft: 16\n}, ownerState.labelPlacement === 'bottom' && {\n  flexDirection: 'column',\n  marginLeft: 16\n}, {\n  [`& .${formControlLabelClasses.label}`]: {\n    [`&.${formControlLabelClasses.disabled}`]: {\n      color: (theme.vars || theme).palette.text.disabled\n    }\n  }\n}));\nconst AsteriskComponent = styled('span', {\n  name: 'MuiFormControlLabel',\n  slot: 'Asterisk',\n  overridesResolver: (props, styles) => styles.asterisk\n})(({\n  theme\n}) => ({\n  [`&.${formControlLabelClasses.error}`]: {\n    color: (theme.vars || theme).palette.error.main\n  }\n}));\n\n/**\n * Drop-in replacement of the `Radio`, `Switch` and `Checkbox` component.\n * Use this component if you want to display an extra label.\n */\nconst FormControlLabel = /*#__PURE__*/React.forwardRef(function FormControlLabel(inProps, ref) {\n  var _ref, _slotProps$typography;\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiFormControlLabel'\n  });\n  const {\n      className,\n      componentsProps = {},\n      control,\n      disabled: disabledProp,\n      disableTypography,\n      label: labelProp,\n      labelPlacement = 'end',\n      required: requiredProp,\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const muiFormControl = useFormControl();\n  const disabled = (_ref = disabledProp != null ? disabledProp : control.props.disabled) != null ? _ref : muiFormControl == null ? void 0 : muiFormControl.disabled;\n  const required = requiredProp != null ? requiredProp : control.props.required;\n  const controlProps = {\n    disabled,\n    required\n  };\n  ['checked', 'name', 'onChange', 'value', 'inputRef'].forEach(key => {\n    if (typeof control.props[key] === 'undefined' && typeof props[key] !== 'undefined') {\n      controlProps[key] = props[key];\n    }\n  });\n  const fcs = formControlState({\n    props,\n    muiFormControl,\n    states: ['error']\n  });\n  const ownerState = _extends({}, props, {\n    disabled,\n    labelPlacement,\n    required,\n    error: fcs.error\n  });\n  const classes = useUtilityClasses(ownerState);\n  const typographySlotProps = (_slotProps$typography = slotProps.typography) != null ? _slotProps$typography : componentsProps.typography;\n  let label = labelProp;\n  if (label != null && label.type !== Typography && !disableTypography) {\n    label = /*#__PURE__*/_jsx(Typography, _extends({\n      component: \"span\"\n    }, typographySlotProps, {\n      className: clsx(classes.label, typographySlotProps == null ? void 0 : typographySlotProps.className),\n      children: label\n    }));\n  }\n  return /*#__PURE__*/_jsxs(FormControlLabelRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other, {\n    children: [/*#__PURE__*/React.cloneElement(control, controlProps), required ? /*#__PURE__*/_jsxs(Stack, {\n      display: \"block\",\n      children: [label, /*#__PURE__*/_jsxs(AsteriskComponent, {\n        ownerState: ownerState,\n        \"aria-hidden\": true,\n        className: classes.asterisk,\n        children: [\"\\u2009\", '*']\n      })]\n    }) : label]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? FormControlLabel.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, the component appears selected.\n   */\n  checked: PropTypes.bool,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    typography: PropTypes.object\n  }),\n  /**\n   * A control element. For instance, it can be a `Radio`, a `Switch` or a `Checkbox`.\n   */\n  control: PropTypes.element.isRequired,\n  /**\n   * If `true`, the control is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the label is rendered as it is passed without an additional typography node.\n   */\n  disableTypography: PropTypes.bool,\n  /**\n   * Pass a ref to the `input` element.\n   */\n  inputRef: refType,\n  /**\n   * A text or an element to be used in an enclosing label element.\n   */\n  label: PropTypes.node,\n  /**\n   * The position of the label.\n   * @default 'end'\n   */\n  labelPlacement: PropTypes.oneOf(['bottom', 'end', 'start', 'top']),\n  /**\n   * @ignore\n   */\n  name: PropTypes.string,\n  /**\n   * Callback fired when the state is changed.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * You can pull out the new checked state by accessing `event.target.checked` (boolean).\n   */\n  onChange: PropTypes.func,\n  /**\n   * If `true`, the label will indicate that the `input` is required.\n   */\n  required: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    typography: PropTypes.object\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the component.\n   */\n  value: PropTypes.any\n} : void 0;\nexport default FormControlLabel;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getFormControlLabelUtilityClasses(slot) {\n  return generateUtilityClass('MuiFormControlLabel', slot);\n}\nconst formControlLabelClasses = generateUtilityClasses('MuiFormControlLabel', ['root', 'labelPlacementStart', 'labelPlacementTop', 'labelPlacementBottom', 'disabled', 'label', 'error', 'required', 'asterisk']);\nexport default formControlLabelClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AAEA,YAAuB;AACvB,wBAAsB;AACtB;AACA;AACA;AAIA;AACA;AACA;;;ACfA;AACA;AACO,SAAS,kCAAkC,MAAM;AACtD,SAAO,qBAAqB,uBAAuB,IAAI;AACzD;AACA,IAAM,0BAA0B,uBAAuB,uBAAuB,CAAC,QAAQ,uBAAuB,qBAAqB,wBAAwB,YAAY,SAAS,SAAS,YAAY,UAAU,CAAC;AAChN,IAAO,kCAAQ;;;ADYf,yBAA4B;AAC5B,IAAAA,sBAA8B;AAf9B,IAAM,YAAY,CAAC,WAAW,aAAa,mBAAmB,WAAW,YAAY,qBAAqB,YAAY,SAAS,kBAAkB,QAAQ,YAAY,YAAY,aAAa,OAAO;AAgBrM,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,YAAY,YAAY,iBAAiB,mBAAW,cAAc,CAAC,IAAI,SAAS,SAAS,YAAY,UAAU;AAAA,IAC9H,OAAO,CAAC,SAAS,YAAY,UAAU;AAAA,IACvC,UAAU,CAAC,YAAY,SAAS,OAAO;AAAA,EACzC;AACA,SAAO,eAAe,OAAO,mCAAmC,OAAO;AACzE;AACO,IAAM,uBAAuB,eAAO,SAAS;AAAA,EAClD,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC;AAAA,MACN,CAAC,MAAM,gCAAwB,KAAK,EAAE,GAAG,OAAO;AAAA,IAClD,GAAG,OAAO,MAAM,OAAO,iBAAiB,mBAAW,WAAW,cAAc,CAAC,EAAE,CAAC;AAAA,EAClF;AACF,CAAC,EAAE,CAAC;AAAA,EACF;AAAA,EACA;AACF,MAAM,SAAS;AAAA,EACb,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,QAAQ;AAAA;AAAA,EAER,eAAe;AAAA,EACf,yBAAyB;AAAA,EACzB,YAAY;AAAA,EACZ,aAAa;AAAA;AAAA,EAEb,CAAC,KAAK,gCAAwB,QAAQ,EAAE,GAAG;AAAA,IACzC,QAAQ;AAAA,EACV;AACF,GAAG,WAAW,mBAAmB,WAAW;AAAA,EAC1C,eAAe;AAAA,EACf,YAAY;AAAA;AAAA,EAEZ,aAAa;AACf,GAAG,WAAW,mBAAmB,SAAS;AAAA,EACxC,eAAe;AAAA,EACf,YAAY;AACd,GAAG,WAAW,mBAAmB,YAAY;AAAA,EAC3C,eAAe;AAAA,EACf,YAAY;AACd,GAAG;AAAA,EACD,CAAC,MAAM,gCAAwB,KAAK,EAAE,GAAG;AAAA,IACvC,CAAC,KAAK,gCAAwB,QAAQ,EAAE,GAAG;AAAA,MACzC,QAAQ,MAAM,QAAQ,OAAO,QAAQ,KAAK;AAAA,IAC5C;AAAA,EACF;AACF,CAAC,CAAC;AACF,IAAM,oBAAoB,eAAO,QAAQ;AAAA,EACvC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW,OAAO;AAC/C,CAAC,EAAE,CAAC;AAAA,EACF;AACF,OAAO;AAAA,EACL,CAAC,KAAK,gCAAwB,KAAK,EAAE,GAAG;AAAA,IACtC,QAAQ,MAAM,QAAQ,OAAO,QAAQ,MAAM;AAAA,EAC7C;AACF,EAAE;AAMF,IAAM,mBAAsC,iBAAW,SAASC,kBAAiB,SAAS,KAAK;AAC7F,MAAI,MAAM;AACV,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF;AAAA,IACA,kBAAkB,CAAC;AAAA,IACnB;AAAA,IACA,UAAU;AAAA,IACV;AAAA,IACA,OAAO;AAAA,IACP,iBAAiB;AAAA,IACjB,UAAU;AAAA,IACV,YAAY,CAAC;AAAA,EACf,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM,iBAAiB,eAAe;AACtC,QAAM,YAAY,OAAO,gBAAgB,OAAO,eAAe,QAAQ,MAAM,aAAa,OAAO,OAAO,kBAAkB,OAAO,SAAS,eAAe;AACzJ,QAAM,WAAW,gBAAgB,OAAO,eAAe,QAAQ,MAAM;AACrE,QAAM,eAAe;AAAA,IACnB;AAAA,IACA;AAAA,EACF;AACA,GAAC,WAAW,QAAQ,YAAY,SAAS,UAAU,EAAE,QAAQ,SAAO;AAClE,QAAI,OAAO,QAAQ,MAAM,GAAG,MAAM,eAAe,OAAO,MAAM,GAAG,MAAM,aAAa;AAClF,mBAAa,GAAG,IAAI,MAAM,GAAG;AAAA,IAC/B;AAAA,EACF,CAAC;AACD,QAAM,MAAM,iBAAiB;AAAA,IAC3B;AAAA,IACA;AAAA,IACA,QAAQ,CAAC,OAAO;AAAA,EAClB,CAAC;AACD,QAAM,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,IAAI;AAAA,EACb,CAAC;AACD,QAAM,UAAU,kBAAkB,UAAU;AAC5C,QAAM,uBAAuB,wBAAwB,UAAU,eAAe,OAAO,wBAAwB,gBAAgB;AAC7H,MAAI,QAAQ;AACZ,MAAI,SAAS,QAAQ,MAAM,SAAS,sBAAc,CAAC,mBAAmB;AACpE,gBAAqB,mBAAAC,KAAK,oBAAY,SAAS;AAAA,MAC7C,WAAW;AAAA,IACb,GAAG,qBAAqB;AAAA,MACtB,WAAW,aAAK,QAAQ,OAAO,uBAAuB,OAAO,SAAS,oBAAoB,SAAS;AAAA,MACnG,UAAU;AAAA,IACZ,CAAC,CAAC;AAAA,EACJ;AACA,aAAoB,oBAAAC,MAAM,sBAAsB,SAAS;AAAA,IACvD,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,IACA;AAAA,EACF,GAAG,OAAO;AAAA,IACR,UAAU,CAAoB,mBAAa,SAAS,YAAY,GAAG,eAAwB,oBAAAA,MAAM,eAAO;AAAA,MACtG,SAAS;AAAA,MACT,UAAU,CAAC,WAAoB,oBAAAA,MAAM,mBAAmB;AAAA,QACtD;AAAA,QACA,eAAe;AAAA,QACf,WAAW,QAAQ;AAAA,QACnB,UAAU,CAAC,KAAU,GAAG;AAAA,MAC1B,CAAC,CAAC;AAAA,IACJ,CAAC,IAAI,KAAK;AAAA,EACZ,CAAC,CAAC;AACJ,CAAC;AACD,OAAwC,iBAAiB,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ1F,SAAS,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,iBAAiB,kBAAAA,QAAU,MAAM;AAAA,IAC/B,YAAY,kBAAAA,QAAU;AAAA,EACxB,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,SAAS,kBAAAA,QAAU,QAAQ;AAAA;AAAA;AAAA;AAAA,EAI3B,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,mBAAmB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAI7B,UAAU;AAAA;AAAA;AAAA;AAAA,EAIV,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,gBAAgB,kBAAAA,QAAU,MAAM,CAAC,UAAU,OAAO,SAAS,KAAK,CAAC;AAAA;AAAA;AAAA;AAAA,EAIjE,MAAM,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOhB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKpB,WAAW,kBAAAA,QAAU,MAAM;AAAA,IACzB,YAAY,kBAAAA,QAAU;AAAA,EACxB,CAAC;AAAA;AAAA;AAAA;AAAA,EAID,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,EAItJ,OAAO,kBAAAA,QAAU;AACnB,IAAI;AACJ,IAAO,2BAAQ;", "names": ["import_jsx_runtime", "FormControlLabel", "_jsx", "_jsxs", "PropTypes"]}