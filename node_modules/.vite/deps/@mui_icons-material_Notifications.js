"use client";
import {
  require_createSvgIcon
} from "./chunk-QPWZELLP.js";
import "./chunk-A6QB5YIE.js";
import "./chunk-QYCGFL4C.js";
import "./chunk-TMVHJIPJ.js";
import "./chunk-MSA5JN5C.js";
import "./chunk-LHDW4QOV.js";
import "./chunk-DQ3JRQWR.js";
import "./chunk-IMQJR6KS.js";
import "./chunk-MJHJ3JER.js";
import "./chunk-XTTIVZZL.js";
import "./chunk-4RI5CGR7.js";
import "./chunk-RH6Y4V25.js";
import "./chunk-KSGB4JOC.js";
import "./chunk-BLNZ42K6.js";
import "./chunk-UBOJQM3L.js";
import "./chunk-U4QGQNYT.js";
import "./chunk-ENYJEBDR.js";
import "./chunk-ZGER5LY4.js";
import "./chunk-5JCGZ3GQ.js";
import "./chunk-SZQXC2AO.js";
import {
  require_interopRequireDefault
} from "./chunk-557MVSUQ.js";
import "./chunk-IMXWEDBK.js";
import "./chunk-TRLI7EVB.js";
import "./chunk-R6OTLWZC.js";
import "./chunk-VBAPX7JU.js";
import {
  require_jsx_runtime
} from "./chunk-NRBATONI.js";
import "./chunk-QJTFJ6OV.js";
import {
  __commonJS
} from "./chunk-V4OQ3NZ2.js";

// node_modules/@mui/icons-material/Notifications.js
var require_Notifications = __commonJS({
  "node_modules/@mui/icons-material/Notifications.js"(exports) {
    var _interopRequireDefault = require_interopRequireDefault();
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _createSvgIcon = _interopRequireDefault(require_createSvgIcon());
    var _jsxRuntime = require_jsx_runtime();
    var _default = exports.default = (0, _createSvgIcon.default)((0, _jsxRuntime.jsx)("path", {
      d: "M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2m6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1z"
    }), "Notifications");
  }
});
export default require_Notifications();
//# sourceMappingURL=@mui_icons-material_Notifications.js.map
