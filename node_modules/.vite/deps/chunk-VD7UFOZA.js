import {
  useThemeProps
} from "./chunk-3VBGT7RV.js";
import {
  defaultTheme_default,
  identifier_default,
  init_defaultTheme,
  init_identifier
} from "./chunk-S6CHQNGA.js";

// node_modules/@mui/material/styles/useThemeProps.js
init_defaultTheme();
init_identifier();
function useThemeProps2({
  props,
  name
}) {
  return useThemeProps({
    props,
    name,
    defaultTheme: defaultTheme_default,
    themeId: identifier_default
  });
}

export {
  useThemeProps2 as useThemeProps
};
//# sourceMappingURL=chunk-VD7UFOZA.js.map
