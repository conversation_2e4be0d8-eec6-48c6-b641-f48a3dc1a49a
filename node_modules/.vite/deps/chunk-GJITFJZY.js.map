{"version": 3, "sources": ["../../@mui/material/AppBar/AppBar.js", "../../@mui/material/AppBar/appBarClasses.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"color\", \"enableColorOnDark\", \"position\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport capitalize from '../utils/capitalize';\nimport Paper from '../Paper';\nimport { getAppBarUtilityClass } from './appBarClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    color,\n    position,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', `color${capitalize(color)}`, `position${capitalize(position)}`]\n  };\n  return composeClasses(slots, getAppBarUtilityClass, classes);\n};\n\n// var2 is the fallback.\n// Ex. var1: 'var(--a)', var2: 'var(--b)'; return: 'var(--a, var(--b))'\nconst joinVars = (var1, var2) => var1 ? `${var1 == null ? void 0 : var1.replace(')', '')}, ${var2})` : var2;\nconst AppBarRoot = styled(Paper, {\n  name: 'MuiAppBar',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[`position${capitalize(ownerState.position)}`], styles[`color${capitalize(ownerState.color)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  const backgroundColorDefault = theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[900];\n  return _extends({\n    display: 'flex',\n    flexDirection: 'column',\n    width: '100%',\n    boxSizing: 'border-box',\n    // Prevent padding issue with the Modal and fixed positioned AppBar.\n    flexShrink: 0\n  }, ownerState.position === 'fixed' && {\n    position: 'fixed',\n    zIndex: (theme.vars || theme).zIndex.appBar,\n    top: 0,\n    left: 'auto',\n    right: 0,\n    '@media print': {\n      // Prevent the app bar to be visible on each printed page.\n      position: 'absolute'\n    }\n  }, ownerState.position === 'absolute' && {\n    position: 'absolute',\n    zIndex: (theme.vars || theme).zIndex.appBar,\n    top: 0,\n    left: 'auto',\n    right: 0\n  }, ownerState.position === 'sticky' && {\n    // ⚠️ sticky is not supported by IE11.\n    position: 'sticky',\n    zIndex: (theme.vars || theme).zIndex.appBar,\n    top: 0,\n    left: 'auto',\n    right: 0\n  }, ownerState.position === 'static' && {\n    position: 'static'\n  }, ownerState.position === 'relative' && {\n    position: 'relative'\n  }, !theme.vars && _extends({}, ownerState.color === 'default' && {\n    backgroundColor: backgroundColorDefault,\n    color: theme.palette.getContrastText(backgroundColorDefault)\n  }, ownerState.color && ownerState.color !== 'default' && ownerState.color !== 'inherit' && ownerState.color !== 'transparent' && {\n    backgroundColor: theme.palette[ownerState.color].main,\n    color: theme.palette[ownerState.color].contrastText\n  }, ownerState.color === 'inherit' && {\n    color: 'inherit'\n  }, theme.palette.mode === 'dark' && !ownerState.enableColorOnDark && {\n    backgroundColor: null,\n    color: null\n  }, ownerState.color === 'transparent' && _extends({\n    backgroundColor: 'transparent',\n    color: 'inherit'\n  }, theme.palette.mode === 'dark' && {\n    backgroundImage: 'none'\n  })), theme.vars && _extends({}, ownerState.color === 'default' && {\n    '--AppBar-background': ownerState.enableColorOnDark ? theme.vars.palette.AppBar.defaultBg : joinVars(theme.vars.palette.AppBar.darkBg, theme.vars.palette.AppBar.defaultBg),\n    '--AppBar-color': ownerState.enableColorOnDark ? theme.vars.palette.text.primary : joinVars(theme.vars.palette.AppBar.darkColor, theme.vars.palette.text.primary)\n  }, ownerState.color && !ownerState.color.match(/^(default|inherit|transparent)$/) && {\n    '--AppBar-background': ownerState.enableColorOnDark ? theme.vars.palette[ownerState.color].main : joinVars(theme.vars.palette.AppBar.darkBg, theme.vars.palette[ownerState.color].main),\n    '--AppBar-color': ownerState.enableColorOnDark ? theme.vars.palette[ownerState.color].contrastText : joinVars(theme.vars.palette.AppBar.darkColor, theme.vars.palette[ownerState.color].contrastText)\n  }, !['inherit', 'transparent'].includes(ownerState.color) && {\n    backgroundColor: 'var(--AppBar-background)'\n  }, {\n    color: ownerState.color === 'inherit' ? 'inherit' : 'var(--AppBar-color)'\n  }, ownerState.color === 'transparent' && {\n    backgroundImage: 'none',\n    backgroundColor: 'transparent',\n    color: 'inherit'\n  }));\n});\nconst AppBar = /*#__PURE__*/React.forwardRef(function AppBar(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAppBar'\n  });\n  const {\n      className,\n      color = 'primary',\n      enableColorOnDark = false,\n      position = 'fixed'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    color,\n    position,\n    enableColorOnDark\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(AppBarRoot, _extends({\n    square: true,\n    component: \"header\",\n    ownerState: ownerState,\n    elevation: 4,\n    className: clsx(classes.root, className, position === 'fixed' && 'mui-fixed'),\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? AppBar.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * @default 'primary'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['default', 'inherit', 'primary', 'secondary', 'transparent', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * If true, the `color` prop is applied in dark mode.\n   * @default false\n   */\n  enableColorOnDark: PropTypes.bool,\n  /**\n   * The positioning type. The behavior of the different options is described\n   * [in the MDN web docs](https://developer.mozilla.org/en-US/docs/Learn/CSS/CSS_layout/Positioning).\n   * Note: `sticky` is not universally supported and will fall back to `static` when unavailable.\n   * @default 'fixed'\n   */\n  position: PropTypes.oneOf(['absolute', 'fixed', 'relative', 'static', 'sticky']),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default AppBar;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAppBarUtilityClass(slot) {\n  return generateUtilityClass('MuiAppBar', slot);\n}\nconst appBarClasses = generateUtilityClasses('MuiAppBar', ['root', 'positionFixed', 'positionAbsolute', 'positionSticky', 'positionStatic', 'positionRelative', 'colorDefault', 'colorPrimary', 'colorSecondary', 'colorInherit', 'colorTransparent', 'colorError', 'colorInfo', 'colorSuccess', 'colorWarning']);\nexport default appBarClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AAEA,YAAuB;AACvB,wBAAsB;AACtB;AACA;AACA;AACA;AACA;;;ACXA;AACA;AACO,SAAS,sBAAsB,MAAM;AAC1C,SAAO,qBAAqB,aAAa,IAAI;AAC/C;AACA,IAAM,gBAAgB,uBAAuB,aAAa,CAAC,QAAQ,iBAAiB,oBAAoB,kBAAkB,kBAAkB,oBAAoB,gBAAgB,gBAAgB,kBAAkB,gBAAgB,oBAAoB,cAAc,aAAa,gBAAgB,cAAc,CAAC;AAChT,IAAO,wBAAQ;;;ADQf,yBAA4B;AAV5B,IAAM,YAAY,CAAC,aAAa,SAAS,qBAAqB,UAAU;AAWxE,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,QAAQ,mBAAW,KAAK,CAAC,IAAI,WAAW,mBAAW,QAAQ,CAAC,EAAE;AAAA,EAC/E;AACA,SAAO,eAAe,OAAO,uBAAuB,OAAO;AAC7D;AAIA,IAAM,WAAW,CAAC,MAAM,SAAS,OAAO,GAAG,QAAQ,OAAO,SAAS,KAAK,QAAQ,KAAK,EAAE,CAAC,KAAK,IAAI,MAAM;AACvG,IAAM,aAAa,eAAO,eAAO;AAAA,EAC/B,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,OAAO,WAAW,mBAAW,WAAW,QAAQ,CAAC,EAAE,GAAG,OAAO,QAAQ,mBAAW,WAAW,KAAK,CAAC,EAAE,CAAC;AAAA,EAC3H;AACF,CAAC,EAAE,CAAC;AAAA,EACF;AAAA,EACA;AACF,MAAM;AACJ,QAAM,yBAAyB,MAAM,QAAQ,SAAS,UAAU,MAAM,QAAQ,KAAK,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AAChH,SAAO,SAAS;AAAA,IACd,SAAS;AAAA,IACT,eAAe;AAAA,IACf,OAAO;AAAA,IACP,WAAW;AAAA;AAAA,IAEX,YAAY;AAAA,EACd,GAAG,WAAW,aAAa,WAAW;AAAA,IACpC,UAAU;AAAA,IACV,SAAS,MAAM,QAAQ,OAAO,OAAO;AAAA,IACrC,KAAK;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,IACP,gBAAgB;AAAA;AAAA,MAEd,UAAU;AAAA,IACZ;AAAA,EACF,GAAG,WAAW,aAAa,cAAc;AAAA,IACvC,UAAU;AAAA,IACV,SAAS,MAAM,QAAQ,OAAO,OAAO;AAAA,IACrC,KAAK;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,EACT,GAAG,WAAW,aAAa,YAAY;AAAA;AAAA,IAErC,UAAU;AAAA,IACV,SAAS,MAAM,QAAQ,OAAO,OAAO;AAAA,IACrC,KAAK;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,EACT,GAAG,WAAW,aAAa,YAAY;AAAA,IACrC,UAAU;AAAA,EACZ,GAAG,WAAW,aAAa,cAAc;AAAA,IACvC,UAAU;AAAA,EACZ,GAAG,CAAC,MAAM,QAAQ,SAAS,CAAC,GAAG,WAAW,UAAU,aAAa;AAAA,IAC/D,iBAAiB;AAAA,IACjB,OAAO,MAAM,QAAQ,gBAAgB,sBAAsB;AAAA,EAC7D,GAAG,WAAW,SAAS,WAAW,UAAU,aAAa,WAAW,UAAU,aAAa,WAAW,UAAU,iBAAiB;AAAA,IAC/H,iBAAiB,MAAM,QAAQ,WAAW,KAAK,EAAE;AAAA,IACjD,OAAO,MAAM,QAAQ,WAAW,KAAK,EAAE;AAAA,EACzC,GAAG,WAAW,UAAU,aAAa;AAAA,IACnC,OAAO;AAAA,EACT,GAAG,MAAM,QAAQ,SAAS,UAAU,CAAC,WAAW,qBAAqB;AAAA,IACnE,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT,GAAG,WAAW,UAAU,iBAAiB,SAAS;AAAA,IAChD,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT,GAAG,MAAM,QAAQ,SAAS,UAAU;AAAA,IAClC,iBAAiB;AAAA,EACnB,CAAC,CAAC,GAAG,MAAM,QAAQ,SAAS,CAAC,GAAG,WAAW,UAAU,aAAa;AAAA,IAChE,uBAAuB,WAAW,oBAAoB,MAAM,KAAK,QAAQ,OAAO,YAAY,SAAS,MAAM,KAAK,QAAQ,OAAO,QAAQ,MAAM,KAAK,QAAQ,OAAO,SAAS;AAAA,IAC1K,kBAAkB,WAAW,oBAAoB,MAAM,KAAK,QAAQ,KAAK,UAAU,SAAS,MAAM,KAAK,QAAQ,OAAO,WAAW,MAAM,KAAK,QAAQ,KAAK,OAAO;AAAA,EAClK,GAAG,WAAW,SAAS,CAAC,WAAW,MAAM,MAAM,iCAAiC,KAAK;AAAA,IACnF,uBAAuB,WAAW,oBAAoB,MAAM,KAAK,QAAQ,WAAW,KAAK,EAAE,OAAO,SAAS,MAAM,KAAK,QAAQ,OAAO,QAAQ,MAAM,KAAK,QAAQ,WAAW,KAAK,EAAE,IAAI;AAAA,IACtL,kBAAkB,WAAW,oBAAoB,MAAM,KAAK,QAAQ,WAAW,KAAK,EAAE,eAAe,SAAS,MAAM,KAAK,QAAQ,OAAO,WAAW,MAAM,KAAK,QAAQ,WAAW,KAAK,EAAE,YAAY;AAAA,EACtM,GAAG,CAAC,CAAC,WAAW,aAAa,EAAE,SAAS,WAAW,KAAK,KAAK;AAAA,IAC3D,iBAAiB;AAAA,EACnB,GAAG;AAAA,IACD,OAAO,WAAW,UAAU,YAAY,YAAY;AAAA,EACtD,GAAG,WAAW,UAAU,iBAAiB;AAAA,IACvC,iBAAiB;AAAA,IACjB,iBAAiB;AAAA,IACjB,OAAO;AAAA,EACT,CAAC,CAAC;AACJ,CAAC;AACD,IAAM,SAA4B,iBAAW,SAASA,QAAO,SAAS,KAAK;AACzE,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,IACR,oBAAoB;AAAA,IACpB,WAAW;AAAA,EACb,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,UAAU,kBAAkB,UAAU;AAC5C,aAAoB,mBAAAC,KAAK,YAAY,SAAS;AAAA,IAC5C,QAAQ;AAAA,IACR,WAAW;AAAA,IACX;AAAA,IACA,WAAW;AAAA,IACX,WAAW,aAAK,QAAQ,MAAM,WAAW,aAAa,WAAW,WAAW;AAAA,IAC5E;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,OAAwC,OAAO,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQhF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOrB,OAAO,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,WAAW,WAAW,WAAW,aAAa,eAAe,SAAS,QAAQ,WAAW,SAAS,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1M,mBAAmB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAO7B,UAAU,kBAAAA,QAAU,MAAM,CAAC,YAAY,SAAS,YAAY,UAAU,QAAQ,CAAC;AAAA;AAAA;AAAA;AAAA,EAI/E,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;AACJ,IAAO,iBAAQ;", "names": ["AppBar", "_jsx", "PropTypes"]}