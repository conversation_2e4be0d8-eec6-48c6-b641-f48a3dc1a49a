{"version": 3, "sources": ["../../@mui/material/Divider/Divider.js", "../../@mui/material/Divider/dividerClasses.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"absolute\", \"children\", \"className\", \"component\", \"flexItem\", \"light\", \"orientation\", \"role\", \"textAlign\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getDividerUtilityClass } from './dividerClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    absolute,\n    children,\n    classes,\n    flexItem,\n    light,\n    orientation,\n    textAlign,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', absolute && 'absolute', variant, light && 'light', orientation === 'vertical' && 'vertical', flexItem && 'flexItem', children && 'withChildren', children && orientation === 'vertical' && 'withChildrenVertical', textAlign === 'right' && orientation !== 'vertical' && 'textAlignRight', textAlign === 'left' && orientation !== 'vertical' && 'textAlignLeft'],\n    wrapper: ['wrapper', orientation === 'vertical' && 'wrapperVertical']\n  };\n  return composeClasses(slots, getDividerUtilityClass, classes);\n};\nconst DividerRoot = styled('div', {\n  name: 'MuiDivider',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.absolute && styles.absolute, styles[ownerState.variant], ownerState.light && styles.light, ownerState.orientation === 'vertical' && styles.vertical, ownerState.flexItem && styles.flexItem, ownerState.children && styles.withChildren, ownerState.children && ownerState.orientation === 'vertical' && styles.withChildrenVertical, ownerState.textAlign === 'right' && ownerState.orientation !== 'vertical' && styles.textAlignRight, ownerState.textAlign === 'left' && ownerState.orientation !== 'vertical' && styles.textAlignLeft];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  margin: 0,\n  // Reset browser default style.\n  flexShrink: 0,\n  borderWidth: 0,\n  borderStyle: 'solid',\n  borderColor: (theme.vars || theme).palette.divider,\n  borderBottomWidth: 'thin'\n}, ownerState.absolute && {\n  position: 'absolute',\n  bottom: 0,\n  left: 0,\n  width: '100%'\n}, ownerState.light && {\n  borderColor: theme.vars ? `rgba(${theme.vars.palette.dividerChannel} / 0.08)` : alpha(theme.palette.divider, 0.08)\n}, ownerState.variant === 'inset' && {\n  marginLeft: 72\n}, ownerState.variant === 'middle' && ownerState.orientation === 'horizontal' && {\n  marginLeft: theme.spacing(2),\n  marginRight: theme.spacing(2)\n}, ownerState.variant === 'middle' && ownerState.orientation === 'vertical' && {\n  marginTop: theme.spacing(1),\n  marginBottom: theme.spacing(1)\n}, ownerState.orientation === 'vertical' && {\n  height: '100%',\n  borderBottomWidth: 0,\n  borderRightWidth: 'thin'\n}, ownerState.flexItem && {\n  alignSelf: 'stretch',\n  height: 'auto'\n}), ({\n  ownerState\n}) => _extends({}, ownerState.children && {\n  display: 'flex',\n  whiteSpace: 'nowrap',\n  textAlign: 'center',\n  border: 0,\n  borderTopStyle: 'solid',\n  borderLeftStyle: 'solid',\n  '&::before, &::after': {\n    content: '\"\"',\n    alignSelf: 'center'\n  }\n}), ({\n  theme,\n  ownerState\n}) => _extends({}, ownerState.children && ownerState.orientation !== 'vertical' && {\n  '&::before, &::after': {\n    width: '100%',\n    borderTop: `thin solid ${(theme.vars || theme).palette.divider}`,\n    borderTopStyle: 'inherit'\n  }\n}), ({\n  theme,\n  ownerState\n}) => _extends({}, ownerState.children && ownerState.orientation === 'vertical' && {\n  flexDirection: 'column',\n  '&::before, &::after': {\n    height: '100%',\n    borderLeft: `thin solid ${(theme.vars || theme).palette.divider}`,\n    borderLeftStyle: 'inherit'\n  }\n}), ({\n  ownerState\n}) => _extends({}, ownerState.textAlign === 'right' && ownerState.orientation !== 'vertical' && {\n  '&::before': {\n    width: '90%'\n  },\n  '&::after': {\n    width: '10%'\n  }\n}, ownerState.textAlign === 'left' && ownerState.orientation !== 'vertical' && {\n  '&::before': {\n    width: '10%'\n  },\n  '&::after': {\n    width: '90%'\n  }\n}));\nconst DividerWrapper = styled('span', {\n  name: 'MuiDivider',\n  slot: 'Wrapper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.wrapper, ownerState.orientation === 'vertical' && styles.wrapperVertical];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  display: 'inline-block',\n  paddingLeft: `calc(${theme.spacing(1)} * 1.2)`,\n  paddingRight: `calc(${theme.spacing(1)} * 1.2)`\n}, ownerState.orientation === 'vertical' && {\n  paddingTop: `calc(${theme.spacing(1)} * 1.2)`,\n  paddingBottom: `calc(${theme.spacing(1)} * 1.2)`\n}));\nconst Divider = /*#__PURE__*/React.forwardRef(function Divider(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiDivider'\n  });\n  const {\n      absolute = false,\n      children,\n      className,\n      component = children ? 'div' : 'hr',\n      flexItem = false,\n      light = false,\n      orientation = 'horizontal',\n      role = component !== 'hr' ? 'separator' : undefined,\n      textAlign = 'center',\n      variant = 'fullWidth'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    absolute,\n    component,\n    flexItem,\n    light,\n    orientation,\n    role,\n    textAlign,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(DividerRoot, _extends({\n    as: component,\n    className: clsx(classes.root, className),\n    role: role,\n    ref: ref,\n    ownerState: ownerState\n  }, other, {\n    children: children ? /*#__PURE__*/_jsx(DividerWrapper, {\n      className: classes.wrapper,\n      ownerState: ownerState,\n      children: children\n    }) : null\n  }));\n});\n\n/**\n * The following flag is used to ensure that this component isn't tabbable i.e.\n * does not get highlight/focus inside of MUI List.\n */\nDivider.muiSkipListHighlight = true;\nprocess.env.NODE_ENV !== \"production\" ? Divider.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Absolutely position the element.\n   * @default false\n   */\n  absolute: PropTypes.bool,\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, a vertical divider will have the correct height when used in flex container.\n   * (By default, a vertical divider will have a calculated height of `0px` if it is the child of a flex container.)\n   * @default false\n   */\n  flexItem: PropTypes.bool,\n  /**\n   * If `true`, the divider will have a lighter color.\n   * @default false\n   * @deprecated Use <Divider sx={{ opacity: 0.6 }} /> (or any opacity or color) instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)\n   */\n  light: PropTypes.bool,\n  /**\n   * The component orientation.\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * @ignore\n   */\n  role: PropTypes /* @typescript-to-proptypes-ignore */.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The text alignment.\n   * @default 'center'\n   */\n  textAlign: PropTypes.oneOf(['center', 'left', 'right']),\n  /**\n   * The variant to use.\n   * @default 'fullWidth'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['fullWidth', 'inset', 'middle']), PropTypes.string])\n} : void 0;\nexport default Divider;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getDividerUtilityClass(slot) {\n  return generateUtilityClass('MuiDivider', slot);\n}\nconst dividerClasses = generateUtilityClasses('MuiDivider', ['root', 'absolute', 'fullWidth', 'inset', 'middle', 'flexItem', 'light', 'vertical', 'withChildren', 'withChildrenVertical', 'textAlignRight', 'textAlignLeft', 'wrapper', 'wrapperVertical']);\nexport default dividerClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AAEA,YAAuB;AACvB,wBAAsB;AACtB;AACA;AACA,8BAAsB;AACtB;AACA;;;ACXA;AACA;AACO,SAAS,uBAAuB,MAAM;AAC3C,SAAO,qBAAqB,cAAc,IAAI;AAChD;AACA,IAAM,iBAAiB,uBAAuB,cAAc,CAAC,QAAQ,YAAY,aAAa,SAAS,UAAU,YAAY,SAAS,YAAY,gBAAgB,wBAAwB,kBAAkB,iBAAiB,WAAW,iBAAiB,CAAC;AAC1P,IAAO,yBAAQ;;;ADOf,yBAA4B;AAT5B,IAAM,YAAY,CAAC,YAAY,YAAY,aAAa,aAAa,YAAY,SAAS,eAAe,QAAQ,aAAa,SAAS;AAUvI,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,YAAY,YAAY,SAAS,SAAS,SAAS,gBAAgB,cAAc,YAAY,YAAY,YAAY,YAAY,gBAAgB,YAAY,gBAAgB,cAAc,wBAAwB,cAAc,WAAW,gBAAgB,cAAc,kBAAkB,cAAc,UAAU,gBAAgB,cAAc,eAAe;AAAA,IAChX,SAAS,CAAC,WAAW,gBAAgB,cAAc,iBAAiB;AAAA,EACtE;AACA,SAAO,eAAe,OAAO,wBAAwB,OAAO;AAC9D;AACA,IAAM,cAAc,eAAO,OAAO;AAAA,EAChC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,WAAW,YAAY,OAAO,UAAU,OAAO,WAAW,OAAO,GAAG,WAAW,SAAS,OAAO,OAAO,WAAW,gBAAgB,cAAc,OAAO,UAAU,WAAW,YAAY,OAAO,UAAU,WAAW,YAAY,OAAO,cAAc,WAAW,YAAY,WAAW,gBAAgB,cAAc,OAAO,sBAAsB,WAAW,cAAc,WAAW,WAAW,gBAAgB,cAAc,OAAO,gBAAgB,WAAW,cAAc,UAAU,WAAW,gBAAgB,cAAc,OAAO,aAAa;AAAA,EAC5iB;AACF,CAAC,EAAE,CAAC;AAAA,EACF;AAAA,EACA;AACF,MAAM,SAAS;AAAA,EACb,QAAQ;AAAA;AAAA,EAER,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,aAAa;AAAA,EACb,cAAc,MAAM,QAAQ,OAAO,QAAQ;AAAA,EAC3C,mBAAmB;AACrB,GAAG,WAAW,YAAY;AAAA,EACxB,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,OAAO;AACT,GAAG,WAAW,SAAS;AAAA,EACrB,aAAa,MAAM,OAAO,QAAQ,MAAM,KAAK,QAAQ,cAAc,iBAAa,+BAAM,MAAM,QAAQ,SAAS,IAAI;AACnH,GAAG,WAAW,YAAY,WAAW;AAAA,EACnC,YAAY;AACd,GAAG,WAAW,YAAY,YAAY,WAAW,gBAAgB,gBAAgB;AAAA,EAC/E,YAAY,MAAM,QAAQ,CAAC;AAAA,EAC3B,aAAa,MAAM,QAAQ,CAAC;AAC9B,GAAG,WAAW,YAAY,YAAY,WAAW,gBAAgB,cAAc;AAAA,EAC7E,WAAW,MAAM,QAAQ,CAAC;AAAA,EAC1B,cAAc,MAAM,QAAQ,CAAC;AAC/B,GAAG,WAAW,gBAAgB,cAAc;AAAA,EAC1C,QAAQ;AAAA,EACR,mBAAmB;AAAA,EACnB,kBAAkB;AACpB,GAAG,WAAW,YAAY;AAAA,EACxB,WAAW;AAAA,EACX,QAAQ;AACV,CAAC,GAAG,CAAC;AAAA,EACH;AACF,MAAM,SAAS,CAAC,GAAG,WAAW,YAAY;AAAA,EACxC,SAAS;AAAA,EACT,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,uBAAuB;AAAA,IACrB,SAAS;AAAA,IACT,WAAW;AAAA,EACb;AACF,CAAC,GAAG,CAAC;AAAA,EACH;AAAA,EACA;AACF,MAAM,SAAS,CAAC,GAAG,WAAW,YAAY,WAAW,gBAAgB,cAAc;AAAA,EACjF,uBAAuB;AAAA,IACrB,OAAO;AAAA,IACP,WAAW,eAAe,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,IAC9D,gBAAgB;AAAA,EAClB;AACF,CAAC,GAAG,CAAC;AAAA,EACH;AAAA,EACA;AACF,MAAM,SAAS,CAAC,GAAG,WAAW,YAAY,WAAW,gBAAgB,cAAc;AAAA,EACjF,eAAe;AAAA,EACf,uBAAuB;AAAA,IACrB,QAAQ;AAAA,IACR,YAAY,eAAe,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,IAC/D,iBAAiB;AAAA,EACnB;AACF,CAAC,GAAG,CAAC;AAAA,EACH;AACF,MAAM,SAAS,CAAC,GAAG,WAAW,cAAc,WAAW,WAAW,gBAAgB,cAAc;AAAA,EAC9F,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACV,OAAO;AAAA,EACT;AACF,GAAG,WAAW,cAAc,UAAU,WAAW,gBAAgB,cAAc;AAAA,EAC7E,aAAa;AAAA,IACX,OAAO;AAAA,EACT;AAAA,EACA,YAAY;AAAA,IACV,OAAO;AAAA,EACT;AACF,CAAC,CAAC;AACF,IAAM,iBAAiB,eAAO,QAAQ;AAAA,EACpC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,SAAS,WAAW,gBAAgB,cAAc,OAAO,eAAe;AAAA,EACzF;AACF,CAAC,EAAE,CAAC;AAAA,EACF;AAAA,EACA;AACF,MAAM,SAAS;AAAA,EACb,SAAS;AAAA,EACT,aAAa,QAAQ,MAAM,QAAQ,CAAC,CAAC;AAAA,EACrC,cAAc,QAAQ,MAAM,QAAQ,CAAC,CAAC;AACxC,GAAG,WAAW,gBAAgB,cAAc;AAAA,EAC1C,YAAY,QAAQ,MAAM,QAAQ,CAAC,CAAC;AAAA,EACpC,eAAe,QAAQ,MAAM,QAAQ,CAAC,CAAC;AACzC,CAAC,CAAC;AACF,IAAM,UAA6B,iBAAW,SAASA,SAAQ,SAAS,KAAK;AAC3E,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA,YAAY,WAAW,QAAQ;AAAA,IAC/B,WAAW;AAAA,IACX,QAAQ;AAAA,IACR,cAAc;AAAA,IACd,OAAO,cAAc,OAAO,cAAc;AAAA,IAC1C,YAAY;AAAA,IACZ,UAAU;AAAA,EACZ,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,IACrC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,QAAM,UAAU,kBAAkB,UAAU;AAC5C,aAAoB,mBAAAC,KAAK,aAAa,SAAS;AAAA,IAC7C,IAAI;AAAA,IACJ,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,OAAO;AAAA,IACR,UAAU,eAAwB,mBAAAA,KAAK,gBAAgB;AAAA,MACrD,WAAW,QAAQ;AAAA,MACnB;AAAA,MACA;AAAA,IACF,CAAC,IAAI;AAAA,EACP,CAAC,CAAC;AACJ,CAAC;AAMD,QAAQ,uBAAuB;AAC/B,OAAwC,QAAQ,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASjF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKrB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMrB,UAAU,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMpB,OAAO,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjB,aAAa,kBAAAA,QAAU,MAAM,CAAC,cAAc,UAAU,CAAC;AAAA;AAAA;AAAA;AAAA,EAIvD,MAAM,kBAAAA,QAAgD;AAAA;AAAA;AAAA;AAAA,EAItD,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtJ,WAAW,kBAAAA,QAAU,MAAM,CAAC,UAAU,QAAQ,OAAO,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtD,SAAS,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,aAAa,SAAS,QAAQ,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAC1I,IAAI;AACJ,IAAO,kBAAQ;", "names": ["Divider", "_jsx", "PropTypes"]}