import {
  init_useControlled,
  useControlled
} from "./chunk-R3XGLXFX.js";
import {
  __esm
} from "./chunk-V4OQ3NZ2.js";

// node_modules/@mui/material/utils/useControlled.js
var useControlled_default;
var init_useControlled2 = __esm({
  "node_modules/@mui/material/utils/useControlled.js"() {
    "use client";
    init_useControlled();
    useControlled_default = useControlled;
  }
});

export {
  useControlled_default,
  init_useControlled2 as init_useControlled
};
//# sourceMappingURL=chunk-JCL4BSAP.js.map
