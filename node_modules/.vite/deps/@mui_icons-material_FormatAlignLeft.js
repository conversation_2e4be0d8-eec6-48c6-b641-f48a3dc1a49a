"use client";
import {
  require_createSvgIcon
} from "./chunk-4OIJTMIH.js";
import "./chunk-QAR7CZ6K.js";
import "./chunk-YE53AYF7.js";
import "./chunk-2CE2MJAS.js";
import "./chunk-YOO6A65J.js";
import "./chunk-P7URYDGB.js";
import "./chunk-HCALP6TC.js";
import "./chunk-HAKFVE3P.js";
import "./chunk-DGORGNSZ.js";
import "./chunk-E4DQEAQY.js";
import "./chunk-ZTG5HMY4.js";
import "./chunk-NIK4EWGR.js";
import "./chunk-BLNZ42K6.js";
import "./chunk-X4NELWXS.js";
import "./chunk-UBOJQM3L.js";
import "./chunk-MSA5JN5C.js";
import "./chunk-KSGB4JOC.js";
import "./chunk-BJWUOKI3.js";
import "./chunk-YJLHJE65.js";
import "./chunk-GFHYJEDL.js";
import {
  require_interopRequireDefault
} from "./chunk-5BYOH2ZS.js";
import "./chunk-IMXWEDBK.js";
import "./chunk-TRLI7EVB.js";
import "./chunk-R6OTLWZC.js";
import "./chunk-VBAPX7JU.js";
import {
  require_jsx_runtime
} from "./chunk-NRBATONI.js";
import "./chunk-QJTFJ6OV.js";
import {
  __commonJS
} from "./chunk-V4OQ3NZ2.js";

// node_modules/@mui/icons-material/FormatAlignLeft.js
var require_FormatAlignLeft = __commonJS({
  "node_modules/@mui/icons-material/FormatAlignLeft.js"(exports) {
    var _interopRequireDefault = require_interopRequireDefault();
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _createSvgIcon = _interopRequireDefault(require_createSvgIcon());
    var _jsxRuntime = require_jsx_runtime();
    var _default = exports.default = (0, _createSvgIcon.default)((0, _jsxRuntime.jsx)("path", {
      d: "M15 15H3v2h12zm0-8H3v2h12zM3 13h18v-2H3zm0 8h18v-2H3zM3 3v2h18V3z"
    }), "FormatAlignLeft");
  }
});
export default require_FormatAlignLeft();
//# sourceMappingURL=@mui_icons-material_FormatAlignLeft.js.map
