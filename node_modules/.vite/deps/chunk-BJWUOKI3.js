import {
  capitalize,
  init_capitalize
} from "./chunk-5BYOH2ZS.js";
import {
  __esm
} from "./chunk-V4OQ3NZ2.js";

// node_modules/@mui/material/utils/capitalize.js
var capitalize_default;
var init_capitalize2 = __esm({
  "node_modules/@mui/material/utils/capitalize.js"() {
    init_capitalize();
    capitalize_default = capitalize;
  }
});

export {
  capitalize_default,
  init_capitalize2 as init_capitalize
};
//# sourceMappingURL=chunk-BJWUOKI3.js.map
