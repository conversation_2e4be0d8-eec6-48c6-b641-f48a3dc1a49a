"use client";
import {
  C<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Theme<PERSON>rovider,
  adaptV4Theme,
  createMuiStrictModeTheme,
  createStyles,
  excludeVariablesFromRoot_default,
  experimental_sx,
  extendTheme,
  getInitColorSchemeScript,
  getUnit,
  makeStyles,
  responsiveFontSizes,
  shouldSkipGeneratingVar,
  toUnitless,
  useColorScheme,
  withStyles,
  withTheme
} from "./chunk-MG6VMMJV.js";
import {
  useThemeProps
} from "./chunk-QMUCN5CP.js";
import {
  getOverlayAlpha_default
} from "./chunk-FMFFUJ5P.js";
import {
  useTheme
} from "./chunk-4TQYIVBR.js";
import {
  alpha,
  darken,
  decomposeColor,
  emphasize,
  getContrastRatio,
  getLuminance,
  hexToRgb,
  hslToRgb,
  lighten,
  recomposeColor,
  rgbToHex
} from "./chunk-2GA2UBUH.js";
import "./chunk-L4AXDUFP.js";
import "./chunk-NIK4EWGR.js";
import "./chunk-BLNZ42K6.js";
import "./chunk-X4NELWXS.js";
import "./chunk-UBOJQM3L.js";
import "./chunk-KSGB4JOC.js";
import {
  styled_default
} from "./chunk-YJLHJE65.js";
import {
  StyledEngineProvider,
  createMixins,
  createMuiTheme,
  createTheme_default2 as createTheme_default,
  createTypography,
  duration,
  easing,
  identifier_default
} from "./chunk-5BYOH2ZS.js";
import {
  css,
  keyframes
} from "./chunk-IMXWEDBK.js";
import "./chunk-TRLI7EVB.js";
import "./chunk-R6OTLWZC.js";
import "./chunk-VBAPX7JU.js";
import "./chunk-NRBATONI.js";
import "./chunk-QJTFJ6OV.js";
import "./chunk-V4OQ3NZ2.js";
export {
  CssVarsProvider as Experimental_CssVarsProvider,
  StyledEngineProvider,
  identifier_default as THEME_ID,
  ThemeProvider,
  adaptV4Theme,
  alpha,
  createMuiTheme,
  createStyles,
  createTheme_default as createTheme,
  css,
  darken,
  decomposeColor,
  duration,
  easing,
  emphasize,
  styled_default as experimentalStyled,
  extendTheme as experimental_extendTheme,
  experimental_sx,
  getContrastRatio,
  getInitColorSchemeScript,
  getLuminance,
  getOverlayAlpha_default as getOverlayAlpha,
  hexToRgb,
  hslToRgb,
  keyframes,
  lighten,
  makeStyles,
  createMixins as private_createMixins,
  createTypography as private_createTypography,
  excludeVariablesFromRoot_default as private_excludeVariablesFromRoot,
  recomposeColor,
  responsiveFontSizes,
  rgbToHex,
  shouldSkipGeneratingVar,
  styled_default as styled,
  createMuiStrictModeTheme as unstable_createMuiStrictModeTheme,
  getUnit as unstable_getUnit,
  toUnitless as unstable_toUnitless,
  useColorScheme,
  useTheme,
  useThemeProps,
  withStyles,
  withTheme
};
//# sourceMappingURL=@mui_material_styles.js.map
