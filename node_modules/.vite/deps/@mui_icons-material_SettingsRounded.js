"use client";
import {
  require_createSvgIcon
} from "./chunk-GNJLXBEX.js";
import "./chunk-M5EXRLMY.js";
import "./chunk-73TMVH5A.js";
import "./chunk-SRTUHBRG.js";
import "./chunk-3FB7GQDA.js";
import "./chunk-CXXRYEIZ.js";
import "./chunk-YSUV7NJ5.js";
import "./chunk-MSA5JN5C.js";
import "./chunk-SA5C6WJB.js";
import "./chunk-NFXL4JXQ.js";
import "./chunk-SOIKCP6A.js";
import "./chunk-DDEYKIDP.js";
import "./chunk-IBZ2U5DW.js";
import "./chunk-LGYNEGWF.js";
import "./chunk-XNX5LQGV.js";
import "./chunk-KSGB4JOC.js";
import "./chunk-UBOJQM3L.js";
import "./chunk-BLNZ42K6.js";
import "./chunk-YFRMLBHE.js";
import "./chunk-PUZPG6Q3.js";
import {
  require_interopRequireDefault
} from "./chunk-DUYCQOLQ.js";
import {
  require_jsx_runtime
} from "./chunk-NRBATONI.js";
import "./chunk-ELXHZ66Z.js";
import "./chunk-TRLI7EVB.js";
import "./chunk-QJTFJ6OV.js";
import "./chunk-R6OTLWZC.js";
import "./chunk-VBAPX7JU.js";
import {
  __commonJS
} from "./chunk-V4OQ3NZ2.js";

// node_modules/@mui/icons-material/SettingsRounded.js
var require_SettingsRounded = __commonJS({
  "node_modules/@mui/icons-material/SettingsRounded.js"(exports) {
    var _interopRequireDefault = require_interopRequireDefault();
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _createSvgIcon = _interopRequireDefault(require_createSvgIcon());
    var _jsxRuntime = require_jsx_runtime();
    var _default = exports.default = (0, _createSvgIcon.default)((0, _jsxRuntime.jsx)("path", {
      d: "M19.5 12c0-.23-.01-.45-.03-.68l1.86-1.41c.4-.3.51-.86.26-1.3l-1.87-3.23c-.25-.44-.79-.62-1.25-.42l-2.15.91c-.37-.26-.76-.49-1.17-.68l-.29-2.31c-.06-.5-.49-.88-.99-.88h-3.73c-.51 0-.94.38-1 .88l-.29 2.31c-.41.19-.8.42-1.17.68l-2.15-.91c-.46-.2-1-.02-1.25.42L2.41 8.62c-.25.44-.14.99.26 1.3l1.86 1.41c-.02.22-.03.44-.03.67s.01.45.03.68l-1.86 1.41c-.4.3-.51.86-.26 1.3l1.87 3.23c.25.44.79.62 1.25.42l2.15-.91c.37.26.76.49 1.17.68l.29 2.31c.06.5.49.88.99.88h3.73c.5 0 .93-.38.99-.88l.29-2.31c.41-.19.8-.42 1.17-.68l2.15.91c.46.2 1 .02 1.25-.42l1.87-3.23c.25-.44.14-.99-.26-1.3l-1.86-1.41c.03-.23.04-.45.04-.68m-7.46 3.5c-1.93 0-3.5-1.57-3.5-3.5s1.57-3.5 3.5-3.5 3.5 1.57 3.5 3.5-1.57 3.5-3.5 3.5"
    }), "SettingsRounded");
  }
});
export default require_SettingsRounded();
//# sourceMappingURL=@mui_icons-material_SettingsRounded.js.map
