{"version": 3, "sources": ["../../@mui/material/SvgIcon/svgIconClasses.js", "../../@mui/material/SvgIcon/SvgIcon.js", "../../@mui/material/SvgIcon/index.js", "../../@mui/material/utils/createSvgIcon.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSvgIconUtilityClass(slot) {\n  return generateUtilityClass('MuiSvgIcon', slot);\n}\nconst svgIconClasses = generateUtilityClasses('MuiSvgIcon', ['root', 'colorPrimary', 'colorSecondary', 'colorAction', 'colorError', 'colorDisabled', 'fontSizeInherit', 'fontSizeSmall', 'fontSizeMedium', 'fontSizeLarge']);\nexport default svgIconClasses;", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"className\", \"color\", \"component\", \"fontSize\", \"htmlColor\", \"inheritViewBox\", \"titleAccess\", \"viewBox\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport capitalize from '../utils/capitalize';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport styled from '../styles/styled';\nimport { getSvgIconUtilityClass } from './svgIconClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    color,\n    fontSize,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', color !== 'inherit' && `color${capitalize(color)}`, `fontSize${capitalize(fontSize)}`]\n  };\n  return composeClasses(slots, getSvgIconUtilityClass, classes);\n};\nconst SvgIconRoot = styled('svg', {\n  name: 'MuiSvgIcon',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.color !== 'inherit' && styles[`color${capitalize(ownerState.color)}`], styles[`fontSize${capitalize(ownerState.fontSize)}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  var _theme$transitions, _theme$transitions$cr, _theme$transitions2, _theme$typography, _theme$typography$pxT, _theme$typography2, _theme$typography2$px, _theme$typography3, _theme$typography3$px, _palette$ownerState$c, _palette, _palette2, _palette3;\n  return {\n    userSelect: 'none',\n    width: '1em',\n    height: '1em',\n    display: 'inline-block',\n    // the <svg> will define the property that has `currentColor`\n    // for example heroicons uses fill=\"none\" and stroke=\"currentColor\"\n    fill: ownerState.hasSvgAsChild ? undefined : 'currentColor',\n    flexShrink: 0,\n    transition: (_theme$transitions = theme.transitions) == null || (_theme$transitions$cr = _theme$transitions.create) == null ? void 0 : _theme$transitions$cr.call(_theme$transitions, 'fill', {\n      duration: (_theme$transitions2 = theme.transitions) == null || (_theme$transitions2 = _theme$transitions2.duration) == null ? void 0 : _theme$transitions2.shorter\n    }),\n    fontSize: {\n      inherit: 'inherit',\n      small: ((_theme$typography = theme.typography) == null || (_theme$typography$pxT = _theme$typography.pxToRem) == null ? void 0 : _theme$typography$pxT.call(_theme$typography, 20)) || '1.25rem',\n      medium: ((_theme$typography2 = theme.typography) == null || (_theme$typography2$px = _theme$typography2.pxToRem) == null ? void 0 : _theme$typography2$px.call(_theme$typography2, 24)) || '1.5rem',\n      large: ((_theme$typography3 = theme.typography) == null || (_theme$typography3$px = _theme$typography3.pxToRem) == null ? void 0 : _theme$typography3$px.call(_theme$typography3, 35)) || '2.1875rem'\n    }[ownerState.fontSize],\n    // TODO v5 deprecate, v6 remove for sx\n    color: (_palette$ownerState$c = (_palette = (theme.vars || theme).palette) == null || (_palette = _palette[ownerState.color]) == null ? void 0 : _palette.main) != null ? _palette$ownerState$c : {\n      action: (_palette2 = (theme.vars || theme).palette) == null || (_palette2 = _palette2.action) == null ? void 0 : _palette2.active,\n      disabled: (_palette3 = (theme.vars || theme).palette) == null || (_palette3 = _palette3.action) == null ? void 0 : _palette3.disabled,\n      inherit: undefined\n    }[ownerState.color]\n  };\n});\nconst SvgIcon = /*#__PURE__*/React.forwardRef(function SvgIcon(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiSvgIcon'\n  });\n  const {\n      children,\n      className,\n      color = 'inherit',\n      component = 'svg',\n      fontSize = 'medium',\n      htmlColor,\n      inheritViewBox = false,\n      titleAccess,\n      viewBox = '0 0 24 24'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const hasSvgAsChild = /*#__PURE__*/React.isValidElement(children) && children.type === 'svg';\n  const ownerState = _extends({}, props, {\n    color,\n    component,\n    fontSize,\n    instanceFontSize: inProps.fontSize,\n    inheritViewBox,\n    viewBox,\n    hasSvgAsChild\n  });\n  const more = {};\n  if (!inheritViewBox) {\n    more.viewBox = viewBox;\n  }\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsxs(SvgIconRoot, _extends({\n    as: component,\n    className: clsx(classes.root, className),\n    focusable: \"false\",\n    color: htmlColor,\n    \"aria-hidden\": titleAccess ? undefined : true,\n    role: titleAccess ? 'img' : undefined,\n    ref: ref\n  }, more, other, hasSvgAsChild && children.props, {\n    ownerState: ownerState,\n    children: [hasSvgAsChild ? children.props.children : children, titleAccess ? /*#__PURE__*/_jsx(\"title\", {\n      children: titleAccess\n    }) : null]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? SvgIcon.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Node passed into the SVG element.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component.\n   * It supports both default and custom theme colors, which can be added as shown in the\n   * [palette customization guide](https://mui.com/material-ui/customization/palette/#custom-colors).\n   * You can use the `htmlColor` prop to apply a color attribute to the SVG element.\n   * @default 'inherit'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['inherit', 'action', 'disabled', 'primary', 'secondary', 'error', 'info', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The fontSize applied to the icon. Defaults to 24px, but can be configure to inherit font size.\n   * @default 'medium'\n   */\n  fontSize: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['inherit', 'large', 'medium', 'small']), PropTypes.string]),\n  /**\n   * Applies a color attribute to the SVG element.\n   */\n  htmlColor: PropTypes.string,\n  /**\n   * If `true`, the root node will inherit the custom `component`'s viewBox and the `viewBox`\n   * prop will be ignored.\n   * Useful when you want to reference a custom `component` and have `SvgIcon` pass that\n   * `component`'s viewBox to the root node.\n   * @default false\n   */\n  inheritViewBox: PropTypes.bool,\n  /**\n   * The shape-rendering attribute. The behavior of the different options is described on the\n   * [MDN Web Docs](https://developer.mozilla.org/en-US/docs/Web/SVG/Attribute/shape-rendering).\n   * If you are having issues with blurry icons you should investigate this prop.\n   */\n  shapeRendering: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Provides a human-readable title for the element that contains it.\n   * https://www.w3.org/TR/SVG-access/#Equivalent\n   */\n  titleAccess: PropTypes.string,\n  /**\n   * Allows you to redefine what the coordinates without units mean inside an SVG element.\n   * For example, if the SVG element is 500 (width) by 200 (height),\n   * and you pass viewBox=\"0 0 50 20\",\n   * this means that the coordinates inside the SVG will go from the top left corner (0,0)\n   * to bottom right (50,20) and each unit will be worth 10px.\n   * @default '0 0 24 24'\n   */\n  viewBox: PropTypes.string\n} : void 0;\nSvgIcon.muiName = 'SvgIcon';\nexport default SvgIcon;", "'use client';\n\nexport { default } from './SvgIcon';\nexport { default as svgIconClasses } from './svgIconClasses';\nexport * from './svgIconClasses';", "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport SvgIcon from '../SvgIcon';\n\n/**\n * Private module reserved for @mui packages.\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function createSvgIcon(path, displayName) {\n  function Component(props, ref) {\n    return /*#__PURE__*/_jsx(SvgIcon, _extends({\n      \"data-testid\": `${displayName}Icon`,\n      ref: ref\n    }, props, {\n      children: path\n    }));\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    // Need to set `displayName` on the inner component for React.memo.\n    // React prior to 16.14 ignores `displayName` on the wrapper.\n    Component.displayName = `${displayName}Icon`;\n  }\n  Component.muiName = SvgIcon.muiName;\n  return /*#__PURE__*/React.memo( /*#__PURE__*/React.forwardRef(Component));\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEO,SAAS,uBAAuB,MAAM;AAC3C,SAAO,qBAAqB,cAAc,IAAI;AAChD;AAJA,IAKM,gBACC;AANP;AAAA;AAAA;AACA;AAIA,IAAM,iBAAiB,uBAAuB,cAAc,CAAC,QAAQ,gBAAgB,kBAAkB,eAAe,cAAc,iBAAiB,mBAAmB,iBAAiB,kBAAkB,eAAe,CAAC;AAC3N,IAAO,yBAAQ;AAAA;AAAA;;;ACNf,IAKA,OACA,mBAOA,oBACAA,qBAVM,WAWA,mBAWA,aAwCA,SAwHC;AA1LP;AAAA;AAAA;AAEA;AACA;AAEA,YAAuB;AACvB,wBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,yBAA4B;AAC5B,IAAAA,sBAA8B;AAV9B,IAAM,YAAY,CAAC,YAAY,aAAa,SAAS,aAAa,YAAY,aAAa,kBAAkB,eAAe,SAAS;AAWrI,IAAM,oBAAoB,gBAAc;AACtC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,YAAM,QAAQ;AAAA,QACZ,MAAM,CAAC,QAAQ,UAAU,aAAa,QAAQ,mBAAW,KAAK,CAAC,IAAI,WAAW,mBAAW,QAAQ,CAAC,EAAE;AAAA,MACtG;AACA,aAAO,eAAe,OAAO,wBAAwB,OAAO;AAAA,IAC9D;AACA,IAAM,cAAc,eAAO,OAAO;AAAA,MAChC,MAAM;AAAA,MACN,MAAM;AAAA,MACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,eAAO,CAAC,OAAO,MAAM,WAAW,UAAU,aAAa,OAAO,QAAQ,mBAAW,WAAW,KAAK,CAAC,EAAE,GAAG,OAAO,WAAW,mBAAW,WAAW,QAAQ,CAAC,EAAE,CAAC;AAAA,MAC7J;AAAA,IACF,CAAC,EAAE,CAAC;AAAA,MACF;AAAA,MACA;AAAA,IACF,MAAM;AACJ,UAAI,oBAAoB,uBAAuB,qBAAqB,mBAAmB,uBAAuB,oBAAoB,uBAAuB,oBAAoB,uBAAuB,uBAAuB,UAAU,WAAW;AAChP,aAAO;AAAA,QACL,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,SAAS;AAAA;AAAA;AAAA,QAGT,MAAM,WAAW,gBAAgB,SAAY;AAAA,QAC7C,YAAY;AAAA,QACZ,aAAa,qBAAqB,MAAM,gBAAgB,SAAS,wBAAwB,mBAAmB,WAAW,OAAO,SAAS,sBAAsB,KAAK,oBAAoB,QAAQ;AAAA,UAC5L,WAAW,sBAAsB,MAAM,gBAAgB,SAAS,sBAAsB,oBAAoB,aAAa,OAAO,SAAS,oBAAoB;AAAA,QAC7J,CAAC;AAAA,QACD,UAAU;AAAA,UACR,SAAS;AAAA,UACT,SAAS,oBAAoB,MAAM,eAAe,SAAS,wBAAwB,kBAAkB,YAAY,OAAO,SAAS,sBAAsB,KAAK,mBAAmB,EAAE,MAAM;AAAA,UACvL,UAAU,qBAAqB,MAAM,eAAe,SAAS,wBAAwB,mBAAmB,YAAY,OAAO,SAAS,sBAAsB,KAAK,oBAAoB,EAAE,MAAM;AAAA,UAC3L,SAAS,qBAAqB,MAAM,eAAe,SAAS,wBAAwB,mBAAmB,YAAY,OAAO,SAAS,sBAAsB,KAAK,oBAAoB,EAAE,MAAM;AAAA,QAC5L,EAAE,WAAW,QAAQ;AAAA;AAAA,QAErB,QAAQ,yBAAyB,YAAY,MAAM,QAAQ,OAAO,YAAY,SAAS,WAAW,SAAS,WAAW,KAAK,MAAM,OAAO,SAAS,SAAS,SAAS,OAAO,wBAAwB;AAAA,UAChM,SAAS,aAAa,MAAM,QAAQ,OAAO,YAAY,SAAS,YAAY,UAAU,WAAW,OAAO,SAAS,UAAU;AAAA,UAC3H,WAAW,aAAa,MAAM,QAAQ,OAAO,YAAY,SAAS,YAAY,UAAU,WAAW,OAAO,SAAS,UAAU;AAAA,UAC7H,SAAS;AAAA,QACX,EAAE,WAAW,KAAK;AAAA,MACpB;AAAA,IACF,CAAC;AACD,IAAM,UAA6B,iBAAW,SAASC,SAAQ,SAAS,KAAK;AAC3E,YAAM,QAAQ,gBAAgB;AAAA,QAC5B,OAAO;AAAA,QACP,MAAM;AAAA,MACR,CAAC;AACD,YAAM;AAAA,QACF;AAAA,QACA;AAAA,QACA,QAAQ;AAAA,QACR,YAAY;AAAA,QACZ,WAAW;AAAA,QACX;AAAA,QACA,iBAAiB;AAAA,QACjB;AAAA,QACA,UAAU;AAAA,MACZ,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,YAAM,gBAAmC,qBAAe,QAAQ,KAAK,SAAS,SAAS;AACvF,YAAM,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,QACrC;AAAA,QACA;AAAA,QACA;AAAA,QACA,kBAAkB,QAAQ;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,YAAM,OAAO,CAAC;AACd,UAAI,CAAC,gBAAgB;AACnB,aAAK,UAAU;AAAA,MACjB;AACA,YAAM,UAAU,kBAAkB,UAAU;AAC5C,iBAAoB,oBAAAC,MAAM,aAAa,SAAS;AAAA,QAC9C,IAAI;AAAA,QACJ,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,QACvC,WAAW;AAAA,QACX,OAAO;AAAA,QACP,eAAe,cAAc,SAAY;AAAA,QACzC,MAAM,cAAc,QAAQ;AAAA,QAC5B;AAAA,MACF,GAAG,MAAM,OAAO,iBAAiB,SAAS,OAAO;AAAA,QAC/C;AAAA,QACA,UAAU,CAAC,gBAAgB,SAAS,MAAM,WAAW,UAAU,kBAA2B,mBAAAC,KAAK,SAAS;AAAA,UACtG,UAAU;AAAA,QACZ,CAAC,IAAI,IAAI;AAAA,MACX,CAAC,CAAC;AAAA,IACJ,CAAC;AACD,WAAwC,QAAQ,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQjF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,MAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,MAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQrB,OAAO,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,WAAW,UAAU,YAAY,WAAW,aAAa,SAAS,QAAQ,WAAW,SAAS,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKtM,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA,MAKrB,UAAU,kBAAAA,QAAgD,UAAU,CAAC,kBAAAA,QAAU,MAAM,CAAC,WAAW,SAAS,UAAU,OAAO,CAAC,GAAG,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA,MAIhJ,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAQrB,gBAAgB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAM1B,gBAAgB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,MAI1B,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,MAKtJ,aAAa,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MASvB,SAAS,kBAAAA,QAAU;AAAA,IACrB,IAAI;AACJ,YAAQ,UAAU;AAClB,IAAO,kBAAQ;AAAA;AAAA;;;AC1Lf,IAAAC,gBAAA;AAAA;AAAA;AAEA;AACA;AACA;AAAA;AAAA;;;ACMe,SAAR,cAA+B,MAAM,aAAa;AACvD,WAAS,UAAU,OAAO,KAAK;AAC7B,eAAoB,oBAAAC,KAAK,iBAAS,SAAS;AAAA,MACzC,eAAe,GAAG,WAAW;AAAA,MAC7B;AAAA,IACF,GAAG,OAAO;AAAA,MACR,UAAU;AAAA,IACZ,CAAC,CAAC;AAAA,EACJ;AACA,MAAI,MAAuC;AAGzC,cAAU,cAAc,GAAG,WAAW;AAAA,EACxC;AACA,YAAU,UAAU,gBAAQ;AAC5B,SAA0B,YAAyB,kBAAW,SAAS,CAAC;AAC1E;AA1BA,IAGAC,QAMAC;AATA;AAAA;AAAA;AAEA;AACA,IAAAD,SAAuB;AACvB,IAAAE;AAKA,IAAAD,sBAA4B;AAAA;AAAA;", "names": ["import_jsx_runtime", "SvgIcon", "_jsxs", "_jsx", "PropTypes", "init_SvgIcon", "_jsx", "React", "import_jsx_runtime", "init_SvgIcon"]}