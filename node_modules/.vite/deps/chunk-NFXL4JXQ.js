import {
  init_useEventCallback,
  init_useIsFocusVisible,
  useEventCallback_default,
  useIsFocusVisible
} from "./chunk-XNX5LQGV.js";
import {
  __esm
} from "./chunk-V4OQ3NZ2.js";

// node_modules/@mui/material/utils/useEventCallback.js
var useEventCallback_default2;
var init_useEventCallback2 = __esm({
  "node_modules/@mui/material/utils/useEventCallback.js"() {
    "use client";
    init_useEventCallback();
    useEventCallback_default2 = useEventCallback_default;
  }
});

// node_modules/@mui/material/utils/useIsFocusVisible.js
var useIsFocusVisible_default;
var init_useIsFocusVisible2 = __esm({
  "node_modules/@mui/material/utils/useIsFocusVisible.js"() {
    "use client";
    init_useIsFocusVisible();
    useIsFocusVisible_default = useIsFocusVisible;
  }
});

export {
  useEventCallback_default2 as useEventCallback_default,
  init_useEventCallback2 as init_useEventCallback,
  useIsFocusVisible_default,
  init_useIsFocusVisible2 as init_useIsFocusVisible
};
//# sourceMappingURL=chunk-NFXL4JXQ.js.map
