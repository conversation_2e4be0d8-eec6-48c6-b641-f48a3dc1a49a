"use client";
import {
  require_createSvgIcon
} from "./chunk-MLDA5AZV.js";
import "./chunk-RJR4UXDO.js";
import "./chunk-FBGNOGQ7.js";
import "./chunk-X5PN2FZT.js";
import "./chunk-6ISMRK6N.js";
import "./chunk-E2I2DTY5.js";
import "./chunk-MSA5JN5C.js";
import "./chunk-JCL4BSAP.js";
import "./chunk-FJAAUDRP.js";
import "./chunk-U4QGQNYT.js";
import "./chunk-2SO7IEV5.js";
import "./chunk-4RI5CGR7.js";
import "./chunk-ENYJEBDR.js";
import "./chunk-R3XGLXFX.js";
import "./chunk-KSGB4JOC.js";
import "./chunk-BLNZ42K6.js";
import "./chunk-ZGER5LY4.js";
import "./chunk-UBOJQM3L.js";
import "./chunk-5JCGZ3GQ.js";
import "./chunk-SZQXC2AO.js";
import {
  require_interopRequireDefault
} from "./chunk-557MVSUQ.js";
import "./chunk-IMXWEDBK.js";
import "./chunk-TRLI7EVB.js";
import "./chunk-R6OTLWZC.js";
import "./chunk-VBAPX7JU.js";
import {
  require_jsx_runtime
} from "./chunk-NRBATONI.js";
import "./chunk-QJTFJ6OV.js";
import {
  __commonJS
} from "./chunk-V4OQ3NZ2.js";

// node_modules/@mui/icons-material/Work.js
var require_Work = __commonJS({
  "node_modules/@mui/icons-material/Work.js"(exports) {
    var _interopRequireDefault = require_interopRequireDefault();
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _createSvgIcon = _interopRequireDefault(require_createSvgIcon());
    var _jsxRuntime = require_jsx_runtime();
    var _default = exports.default = (0, _createSvgIcon.default)((0, _jsxRuntime.jsx)("path", {
      d: "M20 6h-4V4c0-1.11-.89-2-2-2h-4c-1.11 0-2 .89-2 2v2H4c-1.11 0-1.99.89-1.99 2L2 19c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2m-6 0h-4V4h4z"
    }), "Work");
  }
});
export default require_Work();
//# sourceMappingURL=@mui_icons-material_Work.js.map
