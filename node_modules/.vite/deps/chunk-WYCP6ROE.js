import {
  useThemeProps
} from "./chunk-VSAUWO6Z.js";
import {
  defaultTheme_default,
  identifier_default,
  init_defaultTheme,
  init_identifier
} from "./chunk-DUYCQOLQ.js";

// node_modules/@mui/material/styles/useThemeProps.js
init_defaultTheme();
init_identifier();
function useThemeProps2({
  props,
  name
}) {
  return useThemeProps({
    props,
    name,
    defaultTheme: defaultTheme_default,
    themeId: identifier_default
  });
}

export {
  useThemeProps2 as useThemeProps
};
//# sourceMappingURL=chunk-WYCP6ROE.js.map
