"use client";
import {
  require_createSvgIcon
} from "./chunk-GNJLXBEX.js";
import "./chunk-M5EXRLMY.js";
import "./chunk-73TMVH5A.js";
import "./chunk-SRTUHBRG.js";
import "./chunk-3FB7GQDA.js";
import "./chunk-CXXRYEIZ.js";
import "./chunk-YSUV7NJ5.js";
import "./chunk-MSA5JN5C.js";
import "./chunk-SA5C6WJB.js";
import "./chunk-NFXL4JXQ.js";
import "./chunk-SOIKCP6A.js";
import "./chunk-DDEYKIDP.js";
import "./chunk-IBZ2U5DW.js";
import "./chunk-LGYNEGWF.js";
import "./chunk-XNX5LQGV.js";
import "./chunk-KSGB4JOC.js";
import "./chunk-UBOJQM3L.js";
import "./chunk-BLNZ42K6.js";
import "./chunk-YFRMLBHE.js";
import "./chunk-PUZPG6Q3.js";
import {
  require_interopRequireDefault
} from "./chunk-DUYCQOLQ.js";
import {
  require_jsx_runtime
} from "./chunk-NRBATONI.js";
import "./chunk-ELXHZ66Z.js";
import "./chunk-TRLI7EVB.js";
import "./chunk-QJTFJ6OV.js";
import "./chunk-R6OTLWZC.js";
import "./chunk-VBAPX7JU.js";
import {
  __commonJS
} from "./chunk-V4OQ3NZ2.js";

// node_modules/@mui/icons-material/LocationOn.js
var require_LocationOn = __commonJS({
  "node_modules/@mui/icons-material/LocationOn.js"(exports) {
    var _interopRequireDefault = require_interopRequireDefault();
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _createSvgIcon = _interopRequireDefault(require_createSvgIcon());
    var _jsxRuntime = require_jsx_runtime();
    var _default = exports.default = (0, _createSvgIcon.default)((0, _jsxRuntime.jsx)("path", {
      d: "M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7m0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5"
    }), "LocationOn");
  }
});
export default require_LocationOn();
//# sourceMappingURL=@mui_icons-material_LocationOn.js.map
