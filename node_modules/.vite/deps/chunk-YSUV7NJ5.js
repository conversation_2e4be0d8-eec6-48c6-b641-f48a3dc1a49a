import {
  init_unsupportedProp,
  unsupportedProp
} from "./chunk-XNX5LQGV.js";
import {
  __esm
} from "./chunk-V4OQ3NZ2.js";

// node_modules/@mui/material/utils/unsupportedProp.js
var unsupportedProp_default;
var init_unsupportedProp2 = __esm({
  "node_modules/@mui/material/utils/unsupportedProp.js"() {
    init_unsupportedProp();
    unsupportedProp_default = unsupportedProp;
  }
});

export {
  unsupportedProp_default,
  init_unsupportedProp2 as init_unsupportedProp
};
//# sourceMappingURL=chunk-YSUV7NJ5.js.map
