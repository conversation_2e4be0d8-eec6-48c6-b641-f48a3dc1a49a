{"version": 3, "sources": ["../../@mui/material/ListItemIcon/ListItemIcon.js", "../../@mui/material/ListItemIcon/listItemIconClasses.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport { getListItemIconUtilityClass } from './listItemIconClasses';\nimport ListContext from '../List/ListContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    alignItems,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', alignItems === 'flex-start' && 'alignItemsFlexStart']\n  };\n  return composeClasses(slots, getListItemIconUtilityClass, classes);\n};\nconst ListItemIconRoot = styled('div', {\n  name: 'MuiListItemIcon',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.alignItems === 'flex-start' && styles.alignItemsFlexStart];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  minWidth: 56,\n  color: (theme.vars || theme).palette.action.active,\n  flexShrink: 0,\n  display: 'inline-flex'\n}, ownerState.alignItems === 'flex-start' && {\n  marginTop: 8\n}));\n\n/**\n * A simple wrapper to apply `List` styles to an `Icon` or `SvgIcon`.\n */\nconst ListItemIcon = /*#__PURE__*/React.forwardRef(function ListItemIcon(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListItemIcon'\n  });\n  const {\n      className\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const context = React.useContext(ListContext);\n  const ownerState = _extends({}, props, {\n    alignItems: context.alignItems\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ListItemIconRoot, _extends({\n    className: clsx(classes.root, className),\n    ownerState: ownerState,\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItemIcon.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally `Icon`, `SvgIcon`,\n   * or a `@mui/icons-material` SVG icon element.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ListItemIcon;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemIconUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemIcon', slot);\n}\nconst listItemIconClasses = generateUtilityClasses('MuiListItemIcon', ['root', 'alignItemsFlexStart']);\nexport default listItemIconClasses;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AAEA,YAAuB;AACvB,wBAAsB;AACtB;AACA;AACA;AACA;;;ACVA;AACA;AACO,SAAS,4BAA4B,MAAM;AAChD,SAAO,qBAAqB,mBAAmB,IAAI;AACrD;AACA,IAAM,sBAAsB,uBAAuB,mBAAmB,CAAC,QAAQ,qBAAqB,CAAC;AACrG,IAAO,8BAAQ;;;ADOf,yBAA4B;AAT5B,IAAM,YAAY,CAAC,WAAW;AAU9B,IAAM,oBAAoB,gBAAc;AACtC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,QAAQ;AAAA,IACZ,MAAM,CAAC,QAAQ,eAAe,gBAAgB,qBAAqB;AAAA,EACrE;AACA,SAAO,eAAe,OAAO,6BAA6B,OAAO;AACnE;AACA,IAAM,mBAAmB,eAAO,OAAO;AAAA,EACrC,MAAM;AAAA,EACN,MAAM;AAAA,EACN,mBAAmB,CAAC,OAAO,WAAW;AACpC,UAAM;AAAA,MACJ;AAAA,IACF,IAAI;AACJ,WAAO,CAAC,OAAO,MAAM,WAAW,eAAe,gBAAgB,OAAO,mBAAmB;AAAA,EAC3F;AACF,CAAC,EAAE,CAAC;AAAA,EACF;AAAA,EACA;AACF,MAAM,SAAS;AAAA,EACb,UAAU;AAAA,EACV,QAAQ,MAAM,QAAQ,OAAO,QAAQ,OAAO;AAAA,EAC5C,YAAY;AAAA,EACZ,SAAS;AACX,GAAG,WAAW,eAAe,gBAAgB;AAAA,EAC3C,WAAW;AACb,CAAC,CAAC;AAKF,IAAM,eAAkC,iBAAW,SAASA,cAAa,SAAS,KAAK;AACrF,QAAM,QAAQ,gBAAgB;AAAA,IAC5B,OAAO;AAAA,IACP,MAAM;AAAA,EACR,CAAC;AACD,QAAM;AAAA,IACF;AAAA,EACF,IAAI,OACJ,QAAQ,8BAA8B,OAAO,SAAS;AACxD,QAAM,UAAgB,iBAAW,mBAAW;AAC5C,QAAM,aAAa,SAAS,CAAC,GAAG,OAAO;AAAA,IACrC,YAAY,QAAQ;AAAA,EACtB,CAAC;AACD,QAAM,UAAU,kBAAkB,UAAU;AAC5C,aAAoB,mBAAAC,KAAK,kBAAkB,SAAS;AAAA,IAClD,WAAW,aAAK,QAAQ,MAAM,SAAS;AAAA,IACvC;AAAA,IACA;AAAA,EACF,GAAG,KAAK,CAAC;AACX,CAAC;AACD,OAAwC,aAAa,YAAmC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAStF,UAAU,kBAAAC,QAAU;AAAA;AAAA;AAAA;AAAA,EAIpB,SAAS,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAInB,WAAW,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAIrB,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,IAAI,CAAC,CAAC,GAAG,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AACxJ,IAAI;AACJ,IAAO,uBAAQ;", "names": ["ListItemIcon", "_jsx", "PropTypes"]}