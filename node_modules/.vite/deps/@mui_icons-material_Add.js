"use client";
import {
  require_createSvgIcon
} from "./chunk-QM2QW2NC.js";
import "./chunk-DWCOA3OE.js";
import "./chunk-6TJ6WPJ3.js";
import "./chunk-VFU72CD7.js";
import "./chunk-UBTKOXBX.js";
import "./chunk-ISY6YG46.js";
import "./chunk-MSA5JN5C.js";
import "./chunk-XFN3FSYN.js";
import "./chunk-NQYFTKWS.js";
import "./chunk-BRCJZCNH.js";
import "./chunk-2YRJKC42.js";
import "./chunk-I2DJYIFT.js";
import "./chunk-VNUDLZAX.js";
import "./chunk-XRCYO5Y6.js";
import "./chunk-LYFASN5Z.js";
import "./chunk-KSGB4JOC.js";
import "./chunk-UBOJQM3L.js";
import "./chunk-BLNZ42K6.js";
import "./chunk-5JNHQNYD.js";
import "./chunk-EEVJAGPP.js";
import {
  require_interopRequireDefault
} from "./chunk-S6CHQNGA.js";
import "./chunk-IMXWEDBK.js";
import "./chunk-TRLI7EVB.js";
import {
  require_jsx_runtime
} from "./chunk-NRBATONI.js";
import "./chunk-R6OTLWZC.js";
import "./chunk-VBAPX7JU.js";
import "./chunk-QJTFJ6OV.js";
import {
  __commonJS
} from "./chunk-V4OQ3NZ2.js";

// node_modules/@mui/icons-material/Add.js
var require_Add = __commonJS({
  "node_modules/@mui/icons-material/Add.js"(exports) {
    var _interopRequireDefault = require_interopRequireDefault();
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
    exports.default = void 0;
    var _createSvgIcon = _interopRequireDefault(require_createSvgIcon());
    var _jsxRuntime = require_jsx_runtime();
    var _default = exports.default = (0, _createSvgIcon.default)((0, _jsxRuntime.jsx)("path", {
      d: "M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6z"
    }), "Add");
  }
});
export default require_Add();
//# sourceMappingURL=@mui_icons-material_Add.js.map
