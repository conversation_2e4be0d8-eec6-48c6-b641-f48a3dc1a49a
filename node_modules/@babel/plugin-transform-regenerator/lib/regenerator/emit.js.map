{"version": 3, "names": ["_assert", "require", "leap", "meta", "util", "_core", "PENDING_LOCATION", "Number", "MAX_VALUE", "getDeclError", "node", "Error", "JSON", "stringify", "catchParamVisitor", "Identifier", "path", "state", "name", "catchParamName", "isReference", "replaceWithOrRemove", "getSafeParam", "<PERSON><PERSON>", "scope", "hasOwnBinding", "skip", "Emitter", "constructor", "contextId", "nextTempId", "listing", "marked", "insertedLocs", "finalLoc", "tryEntries", "leapManager", "Set", "loc", "LeapManager", "l", "t", "numericLiteral", "add", "getInsertedLocs", "getContextId", "cloneNode", "mark", "index", "length", "value", "assert", "strictEqual", "emit", "isExpression", "expressionStatement", "assertStatement", "push", "emitAssign", "lhs", "rhs", "assign", "assignmentExpression", "contextProperty", "computed", "memberExpression", "stringLiteral", "identifier", "clearPendingException", "tryLoc", "assignee", "catchCall", "callExpression", "jump", "toLoc", "breakStatement", "jumpIf", "test", "ifStatement", "blockStatement", "jumpIfNot", "negatedTest", "isUnaryExpression", "operator", "argument", "unaryExpression", "makeTempVar", "getContextFunction", "id", "functionExpression", "getDispatchLoop", "self", "cases", "current", "alreadyEnded", "for<PERSON>ach", "stmt", "i", "hasOwnProperty", "switchCase", "isCompletionStatement", "returnStatement", "whileStatement", "switchStatement", "getTryLocsList", "lastLocValue", "arrayExpression", "map", "tryEntry", "thisLocValue", "firstLoc", "ok", "ce", "catchEntry", "fe", "finallyEntry", "locs", "afterLoc", "explode", "ignoreResult", "isDeclaration", "isStatement", "explodeStatement", "explodeExpression", "type", "labelId", "before", "after", "head", "isBlockStatement", "get", "containsLeap", "with<PERSON><PERSON>ry", "LabeledEntry", "label", "LoopEntry", "first", "update", "init", "keyIterNextFn", "runtimeProperty", "keyInfoTmpVar", "left", "emitAbruptCompletion", "target", "getBreakLoc", "getContinueLoc", "disc", "defaultLoc", "condition", "caseLocs", "c", "conditionalExpression", "binaryExpression", "discriminant", "SwitchEntry", "casePath", "key", "elseLoc", "alternate", "handler", "catchLoc", "CatchEntry", "param", "finallyLoc", "finalizer", "FinallyEntry", "TryEntry", "getUnmarkedCurrentLoc", "updateContextPrevLoc", "bodyPath", "safeParam", "traverse", "throwStatement", "explodeClass", "record", "notStrictEqual", "<PERSON><PERSON><PERSON><PERSON>", "has", "explodeViaTempVar", "tempVar", "child<PERSON><PERSON>", "hasLeaping<PERSON><PERSON><PERSON>n", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "result", "isLiteral", "expr", "finish", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "property", "<PERSON><PERSON><PERSON><PERSON>", "args<PERSON><PERSON>", "newCallee", "newArgs", "has<PERSON><PERSON>ping<PERSON>rg<PERSON>", "some", "<PERSON><PERSON><PERSON><PERSON>", "injectFirstArg", "isMemberExpression", "newObject", "newProperty", "sequenceExpression", "unshift", "arg", "arguments", "newExpression", "objectExpression", "prop<PERSON>ath", "isObjectProperty", "objectProperty", "elemPath", "isSpreadElement", "spreadElement", "lastIndex", "expressions", "exprPath", "prefix", "temp", "updateExpression", "delegate", "ret", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "superClass", "member", "child", "isLast", "replaceWith", "exports"], "sources": ["../../src/regenerator/emit.ts"], "sourcesContent": ["/* eslint-disable no-case-declarations */\nimport assert from \"node:assert\";\nimport * as leap from \"./leap.ts\";\nimport * as meta from \"./meta.ts\";\nimport * as util from \"./util.ts\";\n\nimport type { NodePath, Visitor } from \"@babel/core\";\nimport { types as t } from \"@babel/core\";\n\ntype AbruptCompletion =\n  | {\n      type: \"break\" | \"continue\";\n      target: t.NumericLiteral;\n    }\n  | {\n      type: \"return\" | \"throw\";\n      value: t.Expression | null;\n    };\n\n// Offsets into this.listing that could be used as targets for branches or\n// jumps are represented as numeric Literal nodes. This representation has\n// the amazingly convenient benefit of allowing the exact value of the\n// location to be determined at any time, even after generating code that\n// refers to the location.\n// We use 'Number.MAX_VALUE' to mark uninitialized location. We can safely do\n// so because no code can realistically have about 1.8e+308 locations before\n// hitting memory limit of the machine it's running on. For comparison, the\n// estimated number of atoms in the observable universe is around 1e+80.\nconst PENDING_LOCATION = Number.MAX_VALUE;\n\nfunction getDeclError(node: t.Node) {\n  return new Error(\n    \"all declarations should have been transformed into \" +\n      \"assignments before the Exploder began its work: \" +\n      JSON.stringify(node),\n  );\n}\n\nconst catchParamVisitor: Visitor = {\n  Identifier: function (path, state: any) {\n    if (path.node.name === state.catchParamName && util.isReference(path)) {\n      util.replaceWithOrRemove(path, state.getSafeParam());\n    }\n  },\n\n  Scope: function (path, state: any) {\n    if (path.scope.hasOwnBinding(state.catchParamName)) {\n      // Don't descend into nested scopes that shadow the catch\n      // parameter with their own declarations.\n      path.skip();\n    }\n  },\n};\n\nexport class Emitter {\n  nextTempId: number;\n  contextId: t.Identifier;\n  listing: t.Statement[];\n  marked: boolean[];\n  insertedLocs: Set<t.NumericLiteral>;\n  finalLoc: t.NumericLiteral;\n  tryEntries: leap.TryEntry[];\n  leapManager: leap.LeapManager;\n\n  constructor(contextId: t.Identifier) {\n    // Used to generate unique temporary names.\n    this.nextTempId = 0;\n\n    // In order to make sure the context object does not collide with\n    // anything in the local scope, we might have to rename it, so we\n    // refer to it symbolically instead of just assuming that it will be\n    // called \"context\".\n    this.contextId = contextId;\n\n    // An append-only list of Statements that grows each time this.emit is\n    // called.\n    this.listing = [];\n\n    // A sparse array whose keys correspond to locations in this.listing\n    // that have been marked as branch/jump targets.\n    this.marked = [true];\n\n    this.insertedLocs = new Set();\n\n    // The last location will be marked when this.getDispatchLoop is\n    // called.\n    this.finalLoc = this.loc();\n\n    // A list of all leap.TryEntry statements emitted.\n    this.tryEntries = [];\n\n    // Each time we evaluate the body of a loop, we tell this.leapManager\n    // to enter a nested loop context that determines the meaning of break\n    // and continue statements therein.\n    this.leapManager = new leap.LeapManager(this);\n  }\n\n  loc() {\n    const l = t.numericLiteral(PENDING_LOCATION);\n    this.insertedLocs.add(l);\n    return l;\n  }\n\n  getInsertedLocs() {\n    return this.insertedLocs;\n  }\n\n  getContextId() {\n    return t.cloneNode(this.contextId);\n  }\n\n  // Sets the exact value of the given location to the offset of the next\n  // Statement emitted.\n  mark(loc: t.NumericLiteral) {\n    const index = this.listing.length;\n    if (loc.value === PENDING_LOCATION) {\n      loc.value = index;\n    } else {\n      // Locations can be marked redundantly, but their values cannot change\n      // once set the first time.\n      assert.strictEqual(loc.value, index);\n    }\n    this.marked[index] = true;\n    return loc;\n  }\n\n  emit(node: t.Node) {\n    if (t.isExpression(node)) {\n      node = t.expressionStatement(node);\n    }\n    t.assertStatement(node);\n    this.listing.push(node);\n  }\n\n  // Shorthand for emitting assignment statements. This will come in handy\n  // for assignments to temporary variables.\n  emitAssign<T extends t.AssignmentExpression[\"left\"]>(\n    lhs: T,\n    rhs: t.Expression,\n  ) {\n    this.emit(this.assign(lhs, rhs));\n    return lhs;\n  }\n\n  // Shorthand for an assignment statement.\n  assign(lhs: t.AssignmentExpression[\"left\"], rhs: t.Expression) {\n    return t.expressionStatement(\n      t.assignmentExpression(\"=\", t.cloneNode(lhs), rhs),\n    );\n  }\n\n  // Convenience function for generating expressions like context.next,\n  // context.sent, and context.rval.\n  contextProperty(name: string, computed?: boolean) {\n    return t.memberExpression(\n      this.getContextId(),\n      computed ? t.stringLiteral(name) : t.identifier(name),\n      !!computed,\n    );\n  }\n\n  clearPendingException(\n    tryLoc: t.NumericLiteral,\n    assignee: t.AssignmentExpression[\"left\"],\n  ) {\n    const catchCall = t.callExpression(this.contextProperty(\"catch\", true), [\n      t.cloneNode(tryLoc),\n    ]);\n\n    if (assignee) {\n      this.emitAssign(assignee, catchCall);\n    } else {\n      this.emit(catchCall);\n    }\n  }\n\n  // Emits code for an unconditional jump to the given location, even if the\n  // exact value of the location is not yet known.\n  jump(toLoc: t.Expression) {\n    this.emitAssign(this.contextProperty(\"next\"), toLoc);\n    this.emit(t.breakStatement());\n  }\n\n  // Conditional jump.\n  jumpIf(test: t.Expression, toLoc: t.NumericLiteral) {\n    this.emit(\n      t.ifStatement(\n        test,\n        t.blockStatement([\n          this.assign(this.contextProperty(\"next\"), toLoc),\n          t.breakStatement(),\n        ]),\n      ),\n    );\n  }\n\n  // Conditional jump, with the condition negated.\n  jumpIfNot(test: t.Expression, toLoc: t.NumericLiteral) {\n    let negatedTest;\n    if (t.isUnaryExpression(test) && test.operator === \"!\") {\n      // Avoid double negation.\n      negatedTest = test.argument;\n    } else {\n      negatedTest = t.unaryExpression(\"!\", test);\n    }\n\n    this.emit(\n      t.ifStatement(\n        negatedTest,\n        t.blockStatement([\n          this.assign(this.contextProperty(\"next\"), toLoc),\n          t.breakStatement(),\n        ]),\n      ),\n    );\n  }\n\n  // Returns a unique MemberExpression that can be used to store and\n  // retrieve temporary values. Since the object of the member expression is\n  // the context object, which is presumed to coexist peacefully with all\n  // other local variables, and since we just increment `nextTempId`\n  // monotonically, uniqueness is assured.\n  makeTempVar() {\n    return this.contextProperty(\"t\" + this.nextTempId++);\n  }\n\n  getContextFunction(id: t.Identifier) {\n    return t.functionExpression(\n      id || null /*Anonymous*/,\n      [this.getContextId()],\n      t.blockStatement([this.getDispatchLoop()]),\n      false, // Not a generator anymore!\n      false, // Nor an expression.\n    );\n  }\n\n  // Turns this.listing into a loop of the form\n  //\n  //   while (1) switch (context.next) {\n  //   case 0:\n  //   ...\n  //   case n:\n  //     return context.stop();\n  //   }\n  //\n  // Each marked location in this.listing will correspond to one generated\n  // case statement.\n  getDispatchLoop() {\n    const self = this;\n    const cases = [];\n    let current;\n\n    // If we encounter a break, continue, or return statement in a switch\n    // case, we can skip the rest of the statements until the next case.\n    let alreadyEnded = false;\n\n    self.listing.forEach(function (stmt, i) {\n      // eslint-disable-next-line no-prototype-builtins\n      if (self.marked.hasOwnProperty(i)) {\n        cases.push(t.switchCase(t.numericLiteral(i), (current = [])));\n        alreadyEnded = false;\n      }\n\n      if (!alreadyEnded) {\n        current.push(stmt);\n        if (t.isCompletionStatement(stmt)) alreadyEnded = true;\n      }\n    });\n\n    // Now that we know how many statements there will be in this.listing,\n    // we can finally resolve this.finalLoc.value.\n    this.finalLoc.value = this.listing.length;\n\n    cases.push(\n      t.switchCase(this.finalLoc, [\n        // Intentionally fall through to the \"end\" case...\n      ]),\n\n      // So that the runtime can jump to the final location without having\n      // to know its offset, we provide the \"end\" case as a synonym.\n      t.switchCase(t.stringLiteral(\"end\"), [\n        // This will check/clear both context.thrown and context.rval.\n        t.returnStatement(t.callExpression(this.contextProperty(\"stop\"), [])),\n      ]),\n    );\n\n    return t.whileStatement(\n      t.numericLiteral(1),\n      t.switchStatement(\n        t.assignmentExpression(\n          \"=\",\n          this.contextProperty(\"prev\"),\n          this.contextProperty(\"next\"),\n        ),\n        cases,\n      ),\n    );\n  }\n\n  getTryLocsList() {\n    if (this.tryEntries.length === 0) {\n      // To avoid adding a needless [] to the majority of runtime.wrap\n      // argument lists, force the caller to handle this case specially.\n      return null;\n    }\n\n    let lastLocValue = 0;\n\n    return t.arrayExpression(\n      this.tryEntries.map(function (tryEntry) {\n        const thisLocValue = tryEntry.firstLoc.value;\n        assert.ok(thisLocValue >= lastLocValue, \"try entries out of order\");\n        lastLocValue = thisLocValue;\n\n        const ce = tryEntry.catchEntry;\n        const fe = tryEntry.finallyEntry;\n\n        const locs = [\n          tryEntry.firstLoc,\n          // The null here makes a hole in the array.\n          ce ? ce.firstLoc : null,\n        ];\n\n        if (fe) {\n          locs[2] = fe.firstLoc;\n          locs[3] = fe.afterLoc;\n        }\n\n        return t.arrayExpression(locs.map(loc => loc && t.cloneNode(loc)));\n      }),\n    );\n  }\n\n  // All side effects must be realized in order.\n\n  // If any subexpression harbors a leap, all subexpressions must be\n  // neutered of side effects.\n\n  // No destructive modification of AST nodes.\n  explode(path: NodePath, ignoreResult?: boolean) {\n    const node = path.node;\n    const self = this;\n\n    if (t.isDeclaration(node)) throw getDeclError(node);\n\n    if (path.isStatement()) return self.explodeStatement(path);\n\n    if (path.isExpression()) return self.explodeExpression(path, ignoreResult);\n\n    switch (node.type) {\n      case \"VariableDeclarator\":\n        throw getDeclError(node);\n\n      // These node types should be handled by their parent nodes\n      // (ObjectExpression, SwitchStatement, and TryStatement, respectively).\n      case \"ObjectProperty\":\n      case \"SwitchCase\":\n      case \"CatchClause\":\n        throw new Error(\n          node.type + \" nodes should be handled by their parents\",\n        );\n\n      default:\n        throw new Error(\"unknown Node of type \" + JSON.stringify(node.type));\n    }\n  }\n\n  explodeStatement(path: NodePath<t.Statement>, labelId: t.Identifier = null) {\n    const stmt = path.node;\n    const self = this;\n    let before: t.NumericLiteral,\n      after: t.NumericLiteral,\n      head: t.NumericLiteral;\n\n    // Explode BlockStatement nodes even if they do not contain a yield,\n    // because we don't want or need the curly braces.\n    if (path.isBlockStatement()) {\n      path.get(\"body\").forEach(function (path) {\n        self.explodeStatement(path);\n      });\n      return;\n    }\n\n    if (!meta.containsLeap(stmt)) {\n      // Technically we should be able to avoid emitting the statement\n      // altogether if !meta.hasSideEffects(stmt), but that leads to\n      // confusing generated code (for instance, `while (true) {}` just\n      // disappears) and is probably a more appropriate job for a dedicated\n      // dead code elimination pass.\n      self.emit(stmt);\n      return;\n    }\n\n    switch (path.type) {\n      case \"ExpressionStatement\":\n        self.explodeExpression(path.get(\"expression\"), true);\n        break;\n\n      case \"LabeledStatement\":\n        after = this.loc();\n\n        // Did you know you can break from any labeled block statement or\n        // control structure? Well, you can! Note: when a labeled loop is\n        // encountered, the leap.LabeledEntry created here will immediately\n        // enclose a leap.LoopEntry on the leap manager's stack, and both\n        // entries will have the same label. Though this works just fine, it\n        // may seem a bit redundant. In theory, we could check here to\n        // determine if stmt knows how to handle its own label; for example,\n        // stmt happens to be a WhileStatement and so we know it's going to\n        // establish its own LoopEntry when we explode it (below). Then this\n        // LabeledEntry would be unnecessary. Alternatively, we might be\n        // tempted not to pass stmt.label down into self.explodeStatement,\n        // because we've handled the label here, but that's a mistake because\n        // labeled loops may contain labeled continue statements, which is not\n        // something we can handle in this generic case. All in all, I think a\n        // little redundancy greatly simplifies the logic of this case, since\n        // it's clear that we handle all possible LabeledStatements correctly\n        // here, regardless of whether they interact with the leap manager\n        // themselves. Also remember that labels and break/continue-to-label\n        // statements are rare, and all of this logic happens at transform\n        // time, so it has no additional runtime cost.\n        self.leapManager.withEntry(\n          new leap.LabeledEntry(after, path.node.label),\n          function () {\n            self.explodeStatement(path.get(\"body\"), path.node.label);\n          },\n        );\n\n        self.mark(after);\n\n        break;\n\n      case \"WhileStatement\":\n        before = this.loc();\n        after = this.loc();\n\n        self.mark(before);\n        self.jumpIfNot(self.explodeExpression(path.get(\"test\")), after);\n        self.leapManager.withEntry(\n          new leap.LoopEntry(after, before, labelId),\n          function () {\n            self.explodeStatement(path.get(\"body\"));\n          },\n        );\n        self.jump(before);\n        self.mark(after);\n\n        break;\n\n      case \"DoWhileStatement\":\n        const first = this.loc();\n        const test = this.loc();\n        after = this.loc();\n\n        self.mark(first);\n        self.leapManager.withEntry(\n          new leap.LoopEntry(after, test, labelId),\n          function () {\n            self.explode(path.get(\"body\"));\n          },\n        );\n        self.mark(test);\n        self.jumpIf(self.explodeExpression(path.get(\"test\")), first);\n        self.mark(after);\n\n        break;\n\n      case \"ForStatement\":\n        head = this.loc();\n        const update = this.loc();\n        after = this.loc();\n\n        if (path.node.init) {\n          // We pass true here to indicate that if stmt.init is an expression\n          // then we do not care about its result.\n          self.explode(path.get(\"init\"), true);\n        }\n\n        self.mark(head);\n\n        if (path.node.test) {\n          self.jumpIfNot(self.explodeExpression(path.get(\"test\")), after);\n        } else {\n          // No test means continue unconditionally.\n        }\n\n        self.leapManager.withEntry(\n          new leap.LoopEntry(after, update, labelId),\n          function () {\n            self.explodeStatement(path.get(\"body\"));\n          },\n        );\n\n        self.mark(update);\n\n        if (path.node.update) {\n          // We pass true here to indicate that if stmt.update is an\n          // expression then we do not care about its result.\n          self.explode(path.get(\"update\"), true);\n        }\n\n        self.jump(head);\n\n        self.mark(after);\n\n        break;\n\n      // @ts-expect-error flow type\n      case \"TypeCastExpression\":\n        return self.explodeExpression((path as any).get(\"expression\"));\n\n      case \"ForInStatement\":\n        head = this.loc();\n        after = this.loc();\n\n        const keyIterNextFn = self.makeTempVar();\n        self.emitAssign(\n          keyIterNextFn,\n          t.callExpression(util.runtimeProperty(\"keys\"), [\n            self.explodeExpression(path.get(\"right\")),\n          ]),\n        );\n\n        self.mark(head);\n\n        const keyInfoTmpVar = self.makeTempVar();\n        self.jumpIf(\n          t.memberExpression(\n            t.assignmentExpression(\n              \"=\",\n              keyInfoTmpVar,\n              t.callExpression(t.cloneNode(keyIterNextFn), []),\n            ),\n            t.identifier(\"done\"),\n            false,\n          ),\n          after,\n        );\n\n        self.emitAssign(\n          path.node.left as t.AssignmentExpression[\"left\"],\n          t.memberExpression(\n            t.cloneNode(keyInfoTmpVar),\n            t.identifier(\"value\"),\n            false,\n          ),\n        );\n\n        self.leapManager.withEntry(\n          new leap.LoopEntry(after, head, labelId),\n          function () {\n            self.explodeStatement(path.get(\"body\"));\n          },\n        );\n\n        self.jump(head);\n\n        self.mark(after);\n\n        break;\n\n      case \"BreakStatement\":\n        self.emitAbruptCompletion({\n          type: \"break\",\n          target: self.leapManager.getBreakLoc(path.node.label),\n        });\n\n        break;\n\n      case \"ContinueStatement\":\n        self.emitAbruptCompletion({\n          type: \"continue\",\n          target: self.leapManager.getContinueLoc(path.node.label),\n        });\n\n        break;\n\n      case \"SwitchStatement\":\n        // Always save the discriminant into a temporary variable in case the\n        // test expressions overwrite values like context.sent.\n        const disc = self.emitAssign(\n          self.makeTempVar(),\n          self.explodeExpression(path.get(\"discriminant\")),\n        );\n\n        after = this.loc();\n        const defaultLoc = this.loc();\n        let condition: t.Expression = defaultLoc;\n        const caseLocs: t.NumericLiteral[] = [];\n\n        // If there are no cases, .cases might be undefined.\n        const cases = path.node.cases || [];\n\n        for (let i = cases.length - 1; i >= 0; --i) {\n          const c = cases[i];\n\n          if (c.test) {\n            condition = t.conditionalExpression(\n              t.binaryExpression(\"===\", t.cloneNode(disc), c.test),\n              (caseLocs[i] = this.loc()),\n              condition,\n            );\n          } else {\n            caseLocs[i] = defaultLoc;\n          }\n        }\n\n        const discriminant = path.get(\"discriminant\");\n        util.replaceWithOrRemove(discriminant, condition);\n        self.jump(self.explodeExpression(discriminant));\n\n        self.leapManager.withEntry(new leap.SwitchEntry(after), function () {\n          path.get(\"cases\").forEach(function (casePath) {\n            const i = casePath.key as number;\n            self.mark(caseLocs[i]);\n\n            casePath.get(\"consequent\").forEach(function (path) {\n              self.explodeStatement(path);\n            });\n          });\n        });\n\n        self.mark(after);\n        if (defaultLoc.value === PENDING_LOCATION) {\n          self.mark(defaultLoc);\n          assert.strictEqual(after.value, defaultLoc.value);\n        }\n\n        break;\n\n      case \"IfStatement\":\n        const elseLoc = path.node.alternate && this.loc();\n        after = this.loc();\n\n        self.jumpIfNot(\n          self.explodeExpression(path.get(\"test\")),\n          elseLoc || after,\n        );\n\n        self.explodeStatement(path.get(\"consequent\"));\n\n        if (elseLoc) {\n          self.jump(after);\n          self.mark(elseLoc);\n          self.explodeStatement(path.get(\"alternate\"));\n        }\n\n        self.mark(after);\n\n        break;\n\n      case \"ReturnStatement\":\n        self.emitAbruptCompletion({\n          type: \"return\",\n          value: self.explodeExpression(path.get(\"argument\")),\n        });\n\n        break;\n\n      case \"WithStatement\":\n        throw new Error(\"WithStatement not supported in generator functions.\");\n\n      case \"TryStatement\":\n        after = this.loc();\n\n        const handler = path.node.handler;\n\n        const catchLoc = handler && this.loc();\n        const catchEntry =\n          catchLoc && new leap.CatchEntry(catchLoc, handler.param as any);\n\n        const finallyLoc = path.node.finalizer && this.loc();\n        const finallyEntry =\n          finallyLoc && new leap.FinallyEntry(finallyLoc, after);\n\n        const tryEntry = new leap.TryEntry(\n          self.getUnmarkedCurrentLoc(),\n          catchEntry,\n          finallyEntry,\n        );\n\n        self.tryEntries.push(tryEntry);\n        self.updateContextPrevLoc(tryEntry.firstLoc);\n\n        self.leapManager.withEntry(tryEntry, function () {\n          self.explodeStatement(path.get(\"block\"));\n\n          if (catchLoc) {\n            if (finallyLoc) {\n              // If we have both a catch block and a finally block, then\n              // because we emit the catch block first, we need to jump over\n              // it to the finally block.\n              self.jump(finallyLoc);\n            } else {\n              // If there is no finally block, then we need to jump over the\n              // catch block to the fall-through location.\n              self.jump(after);\n            }\n\n            self.updateContextPrevLoc(self.mark(catchLoc));\n\n            const bodyPath = path.get(\"handler.body\");\n            const safeParam = self.makeTempVar();\n            self.clearPendingException(tryEntry.firstLoc, safeParam);\n\n            bodyPath.traverse(catchParamVisitor, {\n              getSafeParam: () => t.cloneNode(safeParam),\n              catchParamName:\n                // @ts-expect-error Assuming `handler.param` is `t.Identifier`\n                handler.param.name,\n            });\n\n            self.leapManager.withEntry(catchEntry, function () {\n              self.explodeStatement(bodyPath);\n            });\n          }\n\n          if (finallyLoc) {\n            self.updateContextPrevLoc(self.mark(finallyLoc));\n\n            self.leapManager.withEntry(finallyEntry, function () {\n              self.explodeStatement(path.get(\"finalizer\"));\n            });\n\n            self.emit(\n              t.returnStatement(\n                t.callExpression(self.contextProperty(\"finish\"), [\n                  finallyEntry.firstLoc,\n                ]),\n              ),\n            );\n          }\n        });\n\n        self.mark(after);\n\n        break;\n\n      case \"ThrowStatement\":\n        self.emit(\n          t.throwStatement(self.explodeExpression(path.get(\"argument\"))),\n        );\n\n        break;\n\n      case \"ClassDeclaration\":\n        self.emit(self.explodeClass(path));\n        break;\n\n      default:\n        throw new Error(\n          \"unknown Statement of type \" + JSON.stringify(stmt.type),\n        );\n    }\n  }\n\n  emitAbruptCompletion(record: AbruptCompletion) {\n    assert.notStrictEqual(\n      record.type,\n      \"normal\",\n      \"normal completions are not abrupt\",\n    );\n\n    const abruptArgs: [t.StringLiteral, t.Expression?] = [\n      t.stringLiteral(record.type),\n    ];\n\n    if (record.type === \"break\" || record.type === \"continue\") {\n      abruptArgs[1] = this.insertedLocs.has(record.target)\n        ? record.target\n        : t.cloneNode(record.target);\n    } else if (record.type === \"return\" || record.type === \"throw\") {\n      if (record.value) {\n        abruptArgs[1] = t.cloneNode(record.value);\n      }\n    }\n\n    this.emit(\n      t.returnStatement(\n        t.callExpression(this.contextProperty(\"abrupt\"), abruptArgs),\n      ),\n    );\n  }\n\n  // Not all offsets into emitter.listing are potential jump targets. For\n  // example, execution typically falls into the beginning of a try block\n  // without jumping directly there. This method returns the current offset\n  // without marking it, so that a switch case will not necessarily be\n  // generated for this offset (I say \"not necessarily\" because the same\n  // location might end up being marked in the process of emitting other\n  // statements). There's no logical harm in marking such locations as jump\n  // targets, but minimizing the number of switch cases keeps the generated\n  // code shorter.\n  getUnmarkedCurrentLoc() {\n    return t.numericLiteral(this.listing.length);\n  }\n\n  // The context.prev property takes the value of context.next whenever we\n  // evaluate the switch statement discriminant, which is generally good\n  // enough for tracking the last location we jumped to, but sometimes\n  // context.prev needs to be more precise, such as when we fall\n  // successfully out of a try block and into a finally block without\n  // jumping. This method exists to update context.prev to the freshest\n  // available location. If we were implementing a full interpreter, we\n  // would know the location of the current instruction with complete\n  // precision at all times, but we don't have that luxury here, as it would\n  // be costly and verbose to set context.prev before every statement.\n  updateContextPrevLoc(loc: t.NumericLiteral) {\n    if (loc) {\n      if (loc.value === PENDING_LOCATION) {\n        // If an uninitialized location literal was passed in, set its value\n        // to the current this.listing.length.\n        loc.value = this.listing.length;\n      } else {\n        // Otherwise assert that the location matches the current offset.\n        assert.strictEqual(loc.value, this.listing.length);\n      }\n    } else {\n      loc = this.getUnmarkedCurrentLoc();\n    }\n\n    // Make sure context.prev is up to date in case we fell into this try\n    // statement without jumping to it. TODO Consider avoiding this\n    // assignment when we know control must have jumped here.\n    this.emitAssign(this.contextProperty(\"prev\"), loc);\n  }\n\n  // In order to save the rest of explodeExpression from a combinatorial\n  // trainwreck of special cases, explodeViaTempVar is responsible for\n  // deciding when a subexpression needs to be \"exploded,\" which is my\n  // very technical term for emitting the subexpression as an assignment\n  // to a temporary variable and the substituting the temporary variable\n  // for the original subexpression. Think of exploded view diagrams, not\n  // Michael Bay movies. The point of exploding subexpressions is to\n  // control the precise order in which the generated code realizes the\n  // side effects of those subexpressions.\n  explodeViaTempVar(\n    tempVar: t.MemberExpression,\n    childPath: NodePath<t.Expression>,\n    hasLeapingChildren: boolean,\n    ignoreChildResult?: boolean,\n  ) {\n    assert.ok(\n      !ignoreChildResult || !tempVar,\n      \"Ignoring the result of a child expression but forcing it to \" +\n        \"be assigned to a temporary variable?\",\n    );\n\n    let result = this.explodeExpression(childPath, ignoreChildResult);\n\n    if (ignoreChildResult) {\n      // Side effects already emitted above.\n    } else if (tempVar || (hasLeapingChildren && !t.isLiteral(result))) {\n      // If tempVar was provided, then the result will always be assigned\n      // to it, even if the result does not otherwise need to be assigned\n      // to a temporary variable.  When no tempVar is provided, we have\n      // the flexibility to decide whether a temporary variable is really\n      // necessary.  Unfortunately, in general, a temporary variable is\n      // required whenever any child contains a yield expression, since it\n      // is difficult to prove (at all, let alone efficiently) whether\n      // this result would evaluate to the same value before and after the\n      // yield (see #206).  One narrow case where we can prove it doesn't\n      // matter (and thus we do not need a temporary variable) is when the\n      // result in question is a Literal value.\n      result = this.emitAssign(tempVar || this.makeTempVar(), result);\n    }\n    return result;\n  }\n\n  explodeExpression(\n    path: NodePath<t.Expression>,\n    ignoreResult?: boolean,\n  ): t.Expression {\n    const expr = path.node;\n    if (!expr) {\n      return expr;\n    }\n\n    const self = this;\n    let result; // Used optionally by several cases below.\n    let after;\n\n    function finish(expr: t.Expression) {\n      if (ignoreResult) {\n        self.emit(expr);\n      }\n      return expr;\n    }\n\n    // If the expression does not contain a leap, then we either emit the\n    // expression as a standalone statement or return it whole.\n    if (!meta.containsLeap(expr)) {\n      return finish(expr);\n    }\n\n    // If any child contains a leap (such as a yield or labeled continue or\n    // break statement), then any sibling subexpressions will almost\n    // certainly have to be exploded in order to maintain the order of their\n    // side effects relative to the leaping child(ren).\n    const hasLeapingChildren = meta.containsLeap.onlyChildren(expr);\n\n    // If ignoreResult is true, then we must take full responsibility for\n    // emitting the expression with all its side effects, and we should not\n    // return a result.\n\n    switch (path.type) {\n      case \"MemberExpression\":\n        return finish(\n          t.memberExpression(\n            self.explodeExpression(\n              path.get(\"object\") as NodePath<t.Expression>,\n            ),\n            path.node.computed\n              ? self.explodeViaTempVar(\n                  null,\n                  path.get(\"property\") as NodePath<t.Expression>,\n                  hasLeapingChildren,\n                )\n              : path.node.property,\n            path.node.computed,\n          ),\n        );\n\n      case \"CallExpression\":\n        const calleePath = path.get(\"callee\");\n        const argsPath = path.get(\"arguments\");\n\n        let newCallee;\n        let newArgs;\n\n        const hasLeapingArgs = argsPath.some(argPath =>\n          meta.containsLeap(argPath.node),\n        );\n\n        let injectFirstArg = null;\n\n        if (t.isMemberExpression(calleePath.node)) {\n          if (hasLeapingArgs) {\n            // If the arguments of the CallExpression contained any yield\n            // expressions, then we need to be sure to evaluate the callee\n            // before evaluating the arguments, but if the callee was a member\n            // expression, then we must be careful that the object of the\n            // member expression still gets bound to `this` for the call.\n\n            const newObject = self.explodeViaTempVar(\n              // Assign the exploded callee.object expression to a temporary\n              // variable so that we can use it twice without reevaluating it.\n              self.makeTempVar(),\n              calleePath.get(\"object\") as NodePath<t.Expression>,\n              hasLeapingChildren,\n            );\n\n            const newProperty = calleePath.node.computed\n              ? self.explodeViaTempVar(\n                  null,\n                  calleePath.get(\"property\") as NodePath<t.Expression>,\n                  hasLeapingChildren,\n                )\n              : calleePath.node.property;\n\n            injectFirstArg = newObject;\n\n            newCallee = t.memberExpression(\n              t.memberExpression(\n                t.cloneNode(newObject),\n                newProperty,\n                calleePath.node.computed,\n              ),\n              t.identifier(\"call\"),\n              false,\n            );\n          } else {\n            newCallee = self.explodeExpression(\n              calleePath as NodePath<t.Expression>,\n            );\n          }\n        } else {\n          newCallee = self.explodeViaTempVar(\n            null,\n            calleePath as NodePath<t.Expression>,\n            hasLeapingChildren,\n          );\n\n          if (t.isMemberExpression(newCallee)) {\n            // If the callee was not previously a MemberExpression, then the\n            // CallExpression was \"unqualified,\" meaning its `this` object\n            // should be the global object. If the exploded expression has\n            // become a MemberExpression (e.g. a context property, probably a\n            // temporary variable), then we need to force it to be unqualified\n            // by using the (0, object.property)(...) trick; otherwise, it\n            // will receive the object of the MemberExpression as its `this`\n            // object.\n            newCallee = t.sequenceExpression([\n              t.numericLiteral(0),\n              t.cloneNode(newCallee),\n            ]);\n          }\n        }\n\n        if (hasLeapingArgs) {\n          newArgs = argsPath.map(argPath =>\n            self.explodeViaTempVar(\n              null,\n              argPath as NodePath<t.Expression>,\n              hasLeapingChildren,\n            ),\n          );\n          if (injectFirstArg) newArgs.unshift(injectFirstArg);\n\n          newArgs = newArgs.map(arg => t.cloneNode(arg));\n        } else {\n          newArgs = path.node.arguments;\n        }\n\n        return finish(t.callExpression(newCallee, newArgs));\n\n      case \"NewExpression\":\n        return finish(\n          t.newExpression(\n            self.explodeViaTempVar(\n              null,\n              path.get(\"callee\") as NodePath<t.Expression>,\n              hasLeapingChildren,\n            ),\n            path.get(\"arguments\").map(function (argPath: any) {\n              return self.explodeViaTempVar(null, argPath, hasLeapingChildren);\n            }),\n          ),\n        );\n\n      case \"ObjectExpression\":\n        return finish(\n          t.objectExpression(\n            path.get(\"properties\").map(function (propPath) {\n              if (propPath.isObjectProperty()) {\n                return t.objectProperty(\n                  propPath.node.key,\n                  self.explodeViaTempVar(\n                    null,\n                    // @ts-expect-error `ArrayPattern` cannot cast to `ArrayExpression`\n                    propPath.get(\"value\"),\n                    hasLeapingChildren,\n                  ),\n                  propPath.node.computed,\n                );\n              } else {\n                return propPath.node;\n              }\n            }),\n          ),\n        );\n\n      case \"ArrayExpression\":\n        return finish(\n          t.arrayExpression(\n            path.get(\"elements\").map(function (elemPath: any) {\n              if (!elemPath.node) {\n                return null;\n              }\n              if (elemPath.isSpreadElement()) {\n                return t.spreadElement(\n                  self.explodeViaTempVar(\n                    null,\n                    elemPath.get(\"argument\"),\n                    hasLeapingChildren,\n                  ),\n                );\n              } else {\n                return self.explodeViaTempVar(\n                  null,\n                  elemPath,\n                  hasLeapingChildren,\n                );\n              }\n            }),\n          ),\n        );\n\n      case \"SequenceExpression\":\n        const lastIndex = path.node.expressions.length - 1;\n\n        path.get(\"expressions\").forEach(function (exprPath: any) {\n          if (exprPath.key === lastIndex) {\n            result = self.explodeExpression(exprPath, ignoreResult);\n          } else {\n            self.explodeExpression(exprPath, true);\n          }\n        });\n\n        return result;\n\n      case \"LogicalExpression\":\n        after = this.loc();\n\n        if (!ignoreResult) {\n          result = self.makeTempVar();\n        }\n\n        const left = self.explodeViaTempVar(\n          result,\n          path.get(\"left\"),\n          hasLeapingChildren,\n        );\n\n        if (path.node.operator === \"&&\") {\n          self.jumpIfNot(left, after);\n        } else {\n          assert.strictEqual(path.node.operator, \"||\");\n          self.jumpIf(left, after);\n        }\n\n        self.explodeViaTempVar(\n          result,\n          path.get(\"right\"),\n          hasLeapingChildren,\n          ignoreResult,\n        );\n\n        self.mark(after);\n\n        return result;\n\n      case \"ConditionalExpression\":\n        const elseLoc = this.loc();\n        after = this.loc();\n        const test = self.explodeExpression(path.get(\"test\"));\n\n        self.jumpIfNot(test, elseLoc);\n\n        if (!ignoreResult) {\n          result = self.makeTempVar();\n        }\n\n        self.explodeViaTempVar(\n          result,\n          path.get(\"consequent\"),\n          hasLeapingChildren,\n          ignoreResult,\n        );\n        self.jump(after);\n\n        self.mark(elseLoc);\n        self.explodeViaTempVar(\n          result,\n          path.get(\"alternate\"),\n          hasLeapingChildren,\n          ignoreResult,\n        );\n\n        self.mark(after);\n\n        return result;\n\n      case \"UnaryExpression\":\n        return finish(\n          t.unaryExpression(\n            path.node.operator,\n            // Can't (and don't need to) break up the syntax of the argument.\n            // Think about delete a[b].\n            self.explodeExpression(path.get(\"argument\")),\n            !!path.node.prefix,\n          ),\n        );\n\n      case \"BinaryExpression\":\n        return finish(\n          t.binaryExpression(\n            path.node.operator,\n            self.explodeViaTempVar(\n              null,\n              path.get(\"left\") as NodePath<t.Expression>,\n              hasLeapingChildren,\n            ),\n            self.explodeViaTempVar(null, path.get(\"right\"), hasLeapingChildren),\n          ),\n        );\n\n      case \"AssignmentExpression\":\n        if (path.node.operator === \"=\") {\n          // If this is a simple assignment, the left hand side does not need\n          // to be read before the right hand side is evaluated, so we can\n          // avoid the more complicated logic below.\n          return finish(\n            t.assignmentExpression(\n              path.node.operator,\n              // @ts-expect-error `ArrayPattern` cannot cast to `ArrayExpression`\n              self.explodeExpression(path.get(\"left\")),\n              self.explodeExpression(path.get(\"right\")),\n            ),\n          );\n        }\n\n        // @ts-expect-error `ArrayPattern` cannot cast to `ArrayExpression`\n        const lhs = self.explodeExpression(path.get(\"left\"));\n        const temp = self.emitAssign(self.makeTempVar(), lhs);\n\n        // For example,\n        //\n        //   x += yield y\n        //\n        // becomes\n        //\n        //   context.t0 = x\n        //   x = context.t0 += yield y\n        //\n        // so that the left-hand side expression is read before the yield.\n        // Fixes https://github.com/facebook/regenerator/issues/345.\n\n        return finish(\n          t.assignmentExpression(\n            \"=\",\n            // @ts-expect-error `ArrayPattern` cannot cast to `ArrayExpression`\n            t.cloneNode(lhs),\n            t.assignmentExpression(\n              path.node.operator,\n              t.cloneNode(temp),\n              self.explodeExpression(path.get(\"right\")),\n            ),\n          ),\n        );\n\n      case \"UpdateExpression\":\n        return finish(\n          t.updateExpression(\n            path.node.operator,\n            self.explodeExpression(path.get(\"argument\")) as\n              | t.Identifier\n              | t.MemberExpression,\n            path.node.prefix,\n          ),\n        );\n\n      case \"YieldExpression\":\n        after = this.loc();\n        const arg =\n          path.node.argument && self.explodeExpression(path.get(\"argument\"));\n\n        if (arg && path.node.delegate) {\n          const result = self.makeTempVar();\n\n          const ret = t.returnStatement(\n            t.callExpression(self.contextProperty(\"delegateYield\"), [\n              arg,\n              t.stringLiteral((result.property as t.Identifier).name),\n              after,\n            ]),\n          );\n          ret.loc = expr.loc;\n\n          self.emit(ret);\n          self.mark(after);\n\n          return result;\n        }\n\n        self.emitAssign(self.contextProperty(\"next\"), after);\n\n        const ret = t.returnStatement(t.cloneNode(arg) || null);\n        // Preserve the `yield` location so that source mappings for the statements\n        // link back to the yield properly.\n        ret.loc = expr.loc;\n        self.emit(ret);\n        self.mark(after);\n\n        return self.contextProperty(\"sent\");\n\n      case \"ClassExpression\":\n        return finish(self.explodeClass(path));\n\n      default:\n        throw new Error(\n          \"unknown Expression of type \" + JSON.stringify(expr.type),\n        );\n    }\n  }\n\n  explodeClass<T extends t.Class>(path: NodePath<T>): T {\n    const explodingChildren = [];\n\n    if (path.node.superClass) {\n      explodingChildren.push(path.get(\"superClass\"));\n    }\n\n    path.get(\"body.body\").forEach((member: any) => {\n      if (member.node.computed) {\n        explodingChildren.push(member.get(\"key\"));\n      }\n    });\n\n    const hasLeapingChildren = explodingChildren.some(child =>\n      meta.containsLeap(child),\n    );\n\n    for (let i = 0; i < explodingChildren.length; i++) {\n      const child = explodingChildren[i];\n      const isLast = i === explodingChildren.length - 1;\n\n      if (isLast) {\n        child.replaceWith(this.explodeExpression(child));\n      } else {\n        child.replaceWith(\n          this.explodeViaTempVar(null, child, hasLeapingChildren),\n        );\n      }\n    }\n\n    return path.node as T;\n  }\n}\n"], "mappings": ";;;;;;AACA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,IAAA,GAAAD,OAAA;AACA,IAAAE,IAAA,GAAAF,OAAA;AACA,IAAAG,IAAA,GAAAH,OAAA;AAGA,IAAAI,KAAA,GAAAJ,OAAA;AAqBA,MAAMK,gBAAgB,GAAGC,MAAM,CAACC,SAAS;AAEzC,SAASC,YAAYA,CAACC,IAAY,EAAE;EAClC,OAAO,IAAIC,KAAK,CACd,qDAAqD,GACnD,kDAAkD,GAClDC,IAAI,CAACC,SAAS,CAACH,IAAI,CACvB,CAAC;AACH;AAEA,MAAMI,iBAA0B,GAAG;EACjCC,UAAU,EAAE,SAAAA,CAAUC,IAAI,EAAEC,KAAU,EAAE;IACtC,IAAID,IAAI,CAACN,IAAI,CAACQ,IAAI,KAAKD,KAAK,CAACE,cAAc,IAAIf,IAAI,CAACgB,WAAW,CAACJ,IAAI,CAAC,EAAE;MACrEZ,IAAI,CAACiB,mBAAmB,CAACL,IAAI,EAAEC,KAAK,CAACK,YAAY,CAAC,CAAC,CAAC;IACtD;EACF,CAAC;EAEDC,KAAK,EAAE,SAAAA,CAAUP,IAAI,EAAEC,KAAU,EAAE;IACjC,IAAID,IAAI,CAACQ,KAAK,CAACC,aAAa,CAACR,KAAK,CAACE,cAAc,CAAC,EAAE;MAGlDH,IAAI,CAACU,IAAI,CAAC,CAAC;IACb;EACF;AACF,CAAC;AAEM,MAAMC,OAAO,CAAC;EAUnBC,WAAWA,CAACC,SAAuB,EAAE;IAAA,KATrCC,UAAU;IAAA,KACVD,SAAS;IAAA,KACTE,OAAO;IAAA,KACPC,MAAM;IAAA,KACNC,YAAY;IAAA,KACZC,QAAQ;IAAA,KACRC,UAAU;IAAA,KACVC,WAAW;IAIT,IAAI,CAACN,UAAU,GAAG,CAAC;IAMnB,IAAI,CAACD,SAAS,GAAGA,SAAS;IAI1B,IAAI,CAACE,OAAO,GAAG,EAAE;IAIjB,IAAI,CAACC,MAAM,GAAG,CAAC,IAAI,CAAC;IAEpB,IAAI,CAACC,YAAY,GAAG,IAAII,GAAG,CAAC,CAAC;IAI7B,IAAI,CAACH,QAAQ,GAAG,IAAI,CAACI,GAAG,CAAC,CAAC;IAG1B,IAAI,CAACH,UAAU,GAAG,EAAE;IAKpB,IAAI,CAACC,WAAW,GAAG,IAAIlC,IAAI,CAACqC,WAAW,CAAC,IAAI,CAAC;EAC/C;EAEAD,GAAGA,CAAA,EAAG;IACJ,MAAME,CAAC,GAAGC,WAAC,CAACC,cAAc,CAACpC,gBAAgB,CAAC;IAC5C,IAAI,CAAC2B,YAAY,CAACU,GAAG,CAACH,CAAC,CAAC;IACxB,OAAOA,CAAC;EACV;EAEAI,eAAeA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACX,YAAY;EAC1B;EAEAY,YAAYA,CAAA,EAAG;IACb,OAAOJ,WAAC,CAACK,SAAS,CAAC,IAAI,CAACjB,SAAS,CAAC;EACpC;EAIAkB,IAAIA,CAACT,GAAqB,EAAE;IAC1B,MAAMU,KAAK,GAAG,IAAI,CAACjB,OAAO,CAACkB,MAAM;IACjC,IAAIX,GAAG,CAACY,KAAK,KAAK5C,gBAAgB,EAAE;MAClCgC,GAAG,CAACY,KAAK,GAAGF,KAAK;IACnB,CAAC,MAAM;MAGLG,OAAM,CAACC,WAAW,CAACd,GAAG,CAACY,KAAK,EAAEF,KAAK,CAAC;IACtC;IACA,IAAI,CAAChB,MAAM,CAACgB,KAAK,CAAC,GAAG,IAAI;IACzB,OAAOV,GAAG;EACZ;EAEAe,IAAIA,CAAC3C,IAAY,EAAE;IACjB,IAAI+B,WAAC,CAACa,YAAY,CAAC5C,IAAI,CAAC,EAAE;MACxBA,IAAI,GAAG+B,WAAC,CAACc,mBAAmB,CAAC7C,IAAI,CAAC;IACpC;IACA+B,WAAC,CAACe,eAAe,CAAC9C,IAAI,CAAC;IACvB,IAAI,CAACqB,OAAO,CAAC0B,IAAI,CAAC/C,IAAI,CAAC;EACzB;EAIAgD,UAAUA,CACRC,GAAM,EACNC,GAAiB,EACjB;IACA,IAAI,CAACP,IAAI,CAAC,IAAI,CAACQ,MAAM,CAACF,GAAG,EAAEC,GAAG,CAAC,CAAC;IAChC,OAAOD,GAAG;EACZ;EAGAE,MAAMA,CAACF,GAAmC,EAAEC,GAAiB,EAAE;IAC7D,OAAOnB,WAAC,CAACc,mBAAmB,CAC1Bd,WAAC,CAACqB,oBAAoB,CAAC,GAAG,EAAErB,WAAC,CAACK,SAAS,CAACa,GAAG,CAAC,EAAEC,GAAG,CACnD,CAAC;EACH;EAIAG,eAAeA,CAAC7C,IAAY,EAAE8C,QAAkB,EAAE;IAChD,OAAOvB,WAAC,CAACwB,gBAAgB,CACvB,IAAI,CAACpB,YAAY,CAAC,CAAC,EACnBmB,QAAQ,GAAGvB,WAAC,CAACyB,aAAa,CAAChD,IAAI,CAAC,GAAGuB,WAAC,CAAC0B,UAAU,CAACjD,IAAI,CAAC,EACrD,CAAC,CAAC8C,QACJ,CAAC;EACH;EAEAI,qBAAqBA,CACnBC,MAAwB,EACxBC,QAAwC,EACxC;IACA,MAAMC,SAAS,GAAG9B,WAAC,CAAC+B,cAAc,CAAC,IAAI,CAACT,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,CACtEtB,WAAC,CAACK,SAAS,CAACuB,MAAM,CAAC,CACpB,CAAC;IAEF,IAAIC,QAAQ,EAAE;MACZ,IAAI,CAACZ,UAAU,CAACY,QAAQ,EAAEC,SAAS,CAAC;IACtC,CAAC,MAAM;MACL,IAAI,CAAClB,IAAI,CAACkB,SAAS,CAAC;IACtB;EACF;EAIAE,IAAIA,CAACC,KAAmB,EAAE;IACxB,IAAI,CAAChB,UAAU,CAAC,IAAI,CAACK,eAAe,CAAC,MAAM,CAAC,EAAEW,KAAK,CAAC;IACpD,IAAI,CAACrB,IAAI,CAACZ,WAAC,CAACkC,cAAc,CAAC,CAAC,CAAC;EAC/B;EAGAC,MAAMA,CAACC,IAAkB,EAAEH,KAAuB,EAAE;IAClD,IAAI,CAACrB,IAAI,CACPZ,WAAC,CAACqC,WAAW,CACXD,IAAI,EACJpC,WAAC,CAACsC,cAAc,CAAC,CACf,IAAI,CAAClB,MAAM,CAAC,IAAI,CAACE,eAAe,CAAC,MAAM,CAAC,EAAEW,KAAK,CAAC,EAChDjC,WAAC,CAACkC,cAAc,CAAC,CAAC,CACnB,CACH,CACF,CAAC;EACH;EAGAK,SAASA,CAACH,IAAkB,EAAEH,KAAuB,EAAE;IACrD,IAAIO,WAAW;IACf,IAAIxC,WAAC,CAACyC,iBAAiB,CAACL,IAAI,CAAC,IAAIA,IAAI,CAACM,QAAQ,KAAK,GAAG,EAAE;MAEtDF,WAAW,GAAGJ,IAAI,CAACO,QAAQ;IAC7B,CAAC,MAAM;MACLH,WAAW,GAAGxC,WAAC,CAAC4C,eAAe,CAAC,GAAG,EAAER,IAAI,CAAC;IAC5C;IAEA,IAAI,CAACxB,IAAI,CACPZ,WAAC,CAACqC,WAAW,CACXG,WAAW,EACXxC,WAAC,CAACsC,cAAc,CAAC,CACf,IAAI,CAAClB,MAAM,CAAC,IAAI,CAACE,eAAe,CAAC,MAAM,CAAC,EAAEW,KAAK,CAAC,EAChDjC,WAAC,CAACkC,cAAc,CAAC,CAAC,CACnB,CACH,CACF,CAAC;EACH;EAOAW,WAAWA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACvB,eAAe,CAAC,GAAG,GAAG,IAAI,CAACjC,UAAU,EAAE,CAAC;EACtD;EAEAyD,kBAAkBA,CAACC,EAAgB,EAAE;IACnC,OAAO/C,WAAC,CAACgD,kBAAkB,CACzBD,EAAE,IAAI,IAAI,EACV,CAAC,IAAI,CAAC3C,YAAY,CAAC,CAAC,CAAC,EACrBJ,WAAC,CAACsC,cAAc,CAAC,CAAC,IAAI,CAACW,eAAe,CAAC,CAAC,CAAC,CAAC,EAC1C,KAAK,EACL,KACF,CAAC;EACH;EAaAA,eAAeA,CAAA,EAAG;IAChB,MAAMC,IAAI,GAAG,IAAI;IACjB,MAAMC,KAAK,GAAG,EAAE;IAChB,IAAIC,OAAO;IAIX,IAAIC,YAAY,GAAG,KAAK;IAExBH,IAAI,CAAC5D,OAAO,CAACgE,OAAO,CAAC,UAAUC,IAAI,EAAEC,CAAC,EAAE;MAEtC,IAAIN,IAAI,CAAC3D,MAAM,CAACkE,cAAc,CAACD,CAAC,CAAC,EAAE;QACjCL,KAAK,CAACnC,IAAI,CAAChB,WAAC,CAAC0D,UAAU,CAAC1D,WAAC,CAACC,cAAc,CAACuD,CAAC,CAAC,EAAGJ,OAAO,GAAG,EAAG,CAAC,CAAC;QAC7DC,YAAY,GAAG,KAAK;MACtB;MAEA,IAAI,CAACA,YAAY,EAAE;QACjBD,OAAO,CAACpC,IAAI,CAACuC,IAAI,CAAC;QAClB,IAAIvD,WAAC,CAAC2D,qBAAqB,CAACJ,IAAI,CAAC,EAAEF,YAAY,GAAG,IAAI;MACxD;IACF,CAAC,CAAC;IAIF,IAAI,CAAC5D,QAAQ,CAACgB,KAAK,GAAG,IAAI,CAACnB,OAAO,CAACkB,MAAM;IAEzC2C,KAAK,CAACnC,IAAI,CACRhB,WAAC,CAAC0D,UAAU,CAAC,IAAI,CAACjE,QAAQ,EAAE,EAE3B,CAAC,EAIFO,WAAC,CAAC0D,UAAU,CAAC1D,WAAC,CAACyB,aAAa,CAAC,KAAK,CAAC,EAAE,CAEnCzB,WAAC,CAAC4D,eAAe,CAAC5D,WAAC,CAAC+B,cAAc,CAAC,IAAI,CAACT,eAAe,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CACtE,CACH,CAAC;IAED,OAAOtB,WAAC,CAAC6D,cAAc,CACrB7D,WAAC,CAACC,cAAc,CAAC,CAAC,CAAC,EACnBD,WAAC,CAAC8D,eAAe,CACf9D,WAAC,CAACqB,oBAAoB,CACpB,GAAG,EACH,IAAI,CAACC,eAAe,CAAC,MAAM,CAAC,EAC5B,IAAI,CAACA,eAAe,CAAC,MAAM,CAC7B,CAAC,EACD6B,KACF,CACF,CAAC;EACH;EAEAY,cAAcA,CAAA,EAAG;IACf,IAAI,IAAI,CAACrE,UAAU,CAACc,MAAM,KAAK,CAAC,EAAE;MAGhC,OAAO,IAAI;IACb;IAEA,IAAIwD,YAAY,GAAG,CAAC;IAEpB,OAAOhE,WAAC,CAACiE,eAAe,CACtB,IAAI,CAACvE,UAAU,CAACwE,GAAG,CAAC,UAAUC,QAAQ,EAAE;MACtC,MAAMC,YAAY,GAAGD,QAAQ,CAACE,QAAQ,CAAC5D,KAAK;MAC5CC,OAAM,CAAC4D,EAAE,CAACF,YAAY,IAAIJ,YAAY,EAAE,0BAA0B,CAAC;MACnEA,YAAY,GAAGI,YAAY;MAE3B,MAAMG,EAAE,GAAGJ,QAAQ,CAACK,UAAU;MAC9B,MAAMC,EAAE,GAAGN,QAAQ,CAACO,YAAY;MAEhC,MAAMC,IAAI,GAAG,CACXR,QAAQ,CAACE,QAAQ,EAEjBE,EAAE,GAAGA,EAAE,CAACF,QAAQ,GAAG,IAAI,CACxB;MAED,IAAII,EAAE,EAAE;QACNE,IAAI,CAAC,CAAC,CAAC,GAAGF,EAAE,CAACJ,QAAQ;QACrBM,IAAI,CAAC,CAAC,CAAC,GAAGF,EAAE,CAACG,QAAQ;MACvB;MAEA,OAAO5E,WAAC,CAACiE,eAAe,CAACU,IAAI,CAACT,GAAG,CAACrE,GAAG,IAAIA,GAAG,IAAIG,WAAC,CAACK,SAAS,CAACR,GAAG,CAAC,CAAC,CAAC;IACpE,CAAC,CACH,CAAC;EACH;EAQAgF,OAAOA,CAACtG,IAAc,EAAEuG,YAAsB,EAAE;IAC9C,MAAM7G,IAAI,GAAGM,IAAI,CAACN,IAAI;IACtB,MAAMiF,IAAI,GAAG,IAAI;IAEjB,IAAIlD,WAAC,CAAC+E,aAAa,CAAC9G,IAAI,CAAC,EAAE,MAAMD,YAAY,CAACC,IAAI,CAAC;IAEnD,IAAIM,IAAI,CAACyG,WAAW,CAAC,CAAC,EAAE,OAAO9B,IAAI,CAAC+B,gBAAgB,CAAC1G,IAAI,CAAC;IAE1D,IAAIA,IAAI,CAACsC,YAAY,CAAC,CAAC,EAAE,OAAOqC,IAAI,CAACgC,iBAAiB,CAAC3G,IAAI,EAAEuG,YAAY,CAAC;IAE1E,QAAQ7G,IAAI,CAACkH,IAAI;MACf,KAAK,oBAAoB;QACvB,MAAMnH,YAAY,CAACC,IAAI,CAAC;MAI1B,KAAK,gBAAgB;MACrB,KAAK,YAAY;MACjB,KAAK,aAAa;QAChB,MAAM,IAAIC,KAAK,CACbD,IAAI,CAACkH,IAAI,GAAG,2CACd,CAAC;MAEH;QACE,MAAM,IAAIjH,KAAK,CAAC,uBAAuB,GAAGC,IAAI,CAACC,SAAS,CAACH,IAAI,CAACkH,IAAI,CAAC,CAAC;IACxE;EACF;EAEAF,gBAAgBA,CAAC1G,IAA2B,EAAE6G,OAAqB,GAAG,IAAI,EAAE;IAC1E,MAAM7B,IAAI,GAAGhF,IAAI,CAACN,IAAI;IACtB,MAAMiF,IAAI,GAAG,IAAI;IACjB,IAAImC,MAAwB,EAC1BC,KAAuB,EACvBC,IAAsB;IAIxB,IAAIhH,IAAI,CAACiH,gBAAgB,CAAC,CAAC,EAAE;MAC3BjH,IAAI,CAACkH,GAAG,CAAC,MAAM,CAAC,CAACnC,OAAO,CAAC,UAAU/E,IAAI,EAAE;QACvC2E,IAAI,CAAC+B,gBAAgB,CAAC1G,IAAI,CAAC;MAC7B,CAAC,CAAC;MACF;IACF;IAEA,IAAI,CAACb,IAAI,CAACgI,YAAY,CAACnC,IAAI,CAAC,EAAE;MAM5BL,IAAI,CAACtC,IAAI,CAAC2C,IAAI,CAAC;MACf;IACF;IAEA,QAAQhF,IAAI,CAAC4G,IAAI;MACf,KAAK,qBAAqB;QACxBjC,IAAI,CAACgC,iBAAiB,CAAC3G,IAAI,CAACkH,GAAG,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC;QACpD;MAEF,KAAK,kBAAkB;QACrBH,KAAK,GAAG,IAAI,CAACzF,GAAG,CAAC,CAAC;QAsBlBqD,IAAI,CAACvD,WAAW,CAACgG,SAAS,CACxB,IAAIlI,IAAI,CAACmI,YAAY,CAACN,KAAK,EAAE/G,IAAI,CAACN,IAAI,CAAC4H,KAAK,CAAC,EAC7C,YAAY;UACV3C,IAAI,CAAC+B,gBAAgB,CAAC1G,IAAI,CAACkH,GAAG,CAAC,MAAM,CAAC,EAAElH,IAAI,CAACN,IAAI,CAAC4H,KAAK,CAAC;QAC1D,CACF,CAAC;QAED3C,IAAI,CAAC5C,IAAI,CAACgF,KAAK,CAAC;QAEhB;MAEF,KAAK,gBAAgB;QACnBD,MAAM,GAAG,IAAI,CAACxF,GAAG,CAAC,CAAC;QACnByF,KAAK,GAAG,IAAI,CAACzF,GAAG,CAAC,CAAC;QAElBqD,IAAI,CAAC5C,IAAI,CAAC+E,MAAM,CAAC;QACjBnC,IAAI,CAACX,SAAS,CAACW,IAAI,CAACgC,iBAAiB,CAAC3G,IAAI,CAACkH,GAAG,CAAC,MAAM,CAAC,CAAC,EAAEH,KAAK,CAAC;QAC/DpC,IAAI,CAACvD,WAAW,CAACgG,SAAS,CACxB,IAAIlI,IAAI,CAACqI,SAAS,CAACR,KAAK,EAAED,MAAM,EAAED,OAAO,CAAC,EAC1C,YAAY;UACVlC,IAAI,CAAC+B,gBAAgB,CAAC1G,IAAI,CAACkH,GAAG,CAAC,MAAM,CAAC,CAAC;QACzC,CACF,CAAC;QACDvC,IAAI,CAAClB,IAAI,CAACqD,MAAM,CAAC;QACjBnC,IAAI,CAAC5C,IAAI,CAACgF,KAAK,CAAC;QAEhB;MAEF,KAAK,kBAAkB;QACrB,MAAMS,KAAK,GAAG,IAAI,CAAClG,GAAG,CAAC,CAAC;QACxB,MAAMuC,IAAI,GAAG,IAAI,CAACvC,GAAG,CAAC,CAAC;QACvByF,KAAK,GAAG,IAAI,CAACzF,GAAG,CAAC,CAAC;QAElBqD,IAAI,CAAC5C,IAAI,CAACyF,KAAK,CAAC;QAChB7C,IAAI,CAACvD,WAAW,CAACgG,SAAS,CACxB,IAAIlI,IAAI,CAACqI,SAAS,CAACR,KAAK,EAAElD,IAAI,EAAEgD,OAAO,CAAC,EACxC,YAAY;UACVlC,IAAI,CAAC2B,OAAO,CAACtG,IAAI,CAACkH,GAAG,CAAC,MAAM,CAAC,CAAC;QAChC,CACF,CAAC;QACDvC,IAAI,CAAC5C,IAAI,CAAC8B,IAAI,CAAC;QACfc,IAAI,CAACf,MAAM,CAACe,IAAI,CAACgC,iBAAiB,CAAC3G,IAAI,CAACkH,GAAG,CAAC,MAAM,CAAC,CAAC,EAAEM,KAAK,CAAC;QAC5D7C,IAAI,CAAC5C,IAAI,CAACgF,KAAK,CAAC;QAEhB;MAEF,KAAK,cAAc;QACjBC,IAAI,GAAG,IAAI,CAAC1F,GAAG,CAAC,CAAC;QACjB,MAAMmG,MAAM,GAAG,IAAI,CAACnG,GAAG,CAAC,CAAC;QACzByF,KAAK,GAAG,IAAI,CAACzF,GAAG,CAAC,CAAC;QAElB,IAAItB,IAAI,CAACN,IAAI,CAACgI,IAAI,EAAE;UAGlB/C,IAAI,CAAC2B,OAAO,CAACtG,IAAI,CAACkH,GAAG,CAAC,MAAM,CAAC,EAAE,IAAI,CAAC;QACtC;QAEAvC,IAAI,CAAC5C,IAAI,CAACiF,IAAI,CAAC;QAEf,IAAIhH,IAAI,CAACN,IAAI,CAACmE,IAAI,EAAE;UAClBc,IAAI,CAACX,SAAS,CAACW,IAAI,CAACgC,iBAAiB,CAAC3G,IAAI,CAACkH,GAAG,CAAC,MAAM,CAAC,CAAC,EAAEH,KAAK,CAAC;QACjE,CAAC,MAAM,CAEP;QAEApC,IAAI,CAACvD,WAAW,CAACgG,SAAS,CACxB,IAAIlI,IAAI,CAACqI,SAAS,CAACR,KAAK,EAAEU,MAAM,EAAEZ,OAAO,CAAC,EAC1C,YAAY;UACVlC,IAAI,CAAC+B,gBAAgB,CAAC1G,IAAI,CAACkH,GAAG,CAAC,MAAM,CAAC,CAAC;QACzC,CACF,CAAC;QAEDvC,IAAI,CAAC5C,IAAI,CAAC0F,MAAM,CAAC;QAEjB,IAAIzH,IAAI,CAACN,IAAI,CAAC+H,MAAM,EAAE;UAGpB9C,IAAI,CAAC2B,OAAO,CAACtG,IAAI,CAACkH,GAAG,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC;QACxC;QAEAvC,IAAI,CAAClB,IAAI,CAACuD,IAAI,CAAC;QAEfrC,IAAI,CAAC5C,IAAI,CAACgF,KAAK,CAAC;QAEhB;MAGF,KAAK,oBAAoB;QACvB,OAAOpC,IAAI,CAACgC,iBAAiB,CAAE3G,IAAI,CAASkH,GAAG,CAAC,YAAY,CAAC,CAAC;MAEhE,KAAK,gBAAgB;QACnBF,IAAI,GAAG,IAAI,CAAC1F,GAAG,CAAC,CAAC;QACjByF,KAAK,GAAG,IAAI,CAACzF,GAAG,CAAC,CAAC;QAElB,MAAMqG,aAAa,GAAGhD,IAAI,CAACL,WAAW,CAAC,CAAC;QACxCK,IAAI,CAACjC,UAAU,CACbiF,aAAa,EACblG,WAAC,CAAC+B,cAAc,CAACpE,IAAI,CAACwI,eAAe,CAAC,MAAM,CAAC,EAAE,CAC7CjD,IAAI,CAACgC,iBAAiB,CAAC3G,IAAI,CAACkH,GAAG,CAAC,OAAO,CAAC,CAAC,CAC1C,CACH,CAAC;QAEDvC,IAAI,CAAC5C,IAAI,CAACiF,IAAI,CAAC;QAEf,MAAMa,aAAa,GAAGlD,IAAI,CAACL,WAAW,CAAC,CAAC;QACxCK,IAAI,CAACf,MAAM,CACTnC,WAAC,CAACwB,gBAAgB,CAChBxB,WAAC,CAACqB,oBAAoB,CACpB,GAAG,EACH+E,aAAa,EACbpG,WAAC,CAAC+B,cAAc,CAAC/B,WAAC,CAACK,SAAS,CAAC6F,aAAa,CAAC,EAAE,EAAE,CACjD,CAAC,EACDlG,WAAC,CAAC0B,UAAU,CAAC,MAAM,CAAC,EACpB,KACF,CAAC,EACD4D,KACF,CAAC;QAEDpC,IAAI,CAACjC,UAAU,CACb1C,IAAI,CAACN,IAAI,CAACoI,IAAI,EACdrG,WAAC,CAACwB,gBAAgB,CAChBxB,WAAC,CAACK,SAAS,CAAC+F,aAAa,CAAC,EAC1BpG,WAAC,CAAC0B,UAAU,CAAC,OAAO,CAAC,EACrB,KACF,CACF,CAAC;QAEDwB,IAAI,CAACvD,WAAW,CAACgG,SAAS,CACxB,IAAIlI,IAAI,CAACqI,SAAS,CAACR,KAAK,EAAEC,IAAI,EAAEH,OAAO,CAAC,EACxC,YAAY;UACVlC,IAAI,CAAC+B,gBAAgB,CAAC1G,IAAI,CAACkH,GAAG,CAAC,MAAM,CAAC,CAAC;QACzC,CACF,CAAC;QAEDvC,IAAI,CAAClB,IAAI,CAACuD,IAAI,CAAC;QAEfrC,IAAI,CAAC5C,IAAI,CAACgF,KAAK,CAAC;QAEhB;MAEF,KAAK,gBAAgB;QACnBpC,IAAI,CAACoD,oBAAoB,CAAC;UACxBnB,IAAI,EAAE,OAAO;UACboB,MAAM,EAAErD,IAAI,CAACvD,WAAW,CAAC6G,WAAW,CAACjI,IAAI,CAACN,IAAI,CAAC4H,KAAK;QACtD,CAAC,CAAC;QAEF;MAEF,KAAK,mBAAmB;QACtB3C,IAAI,CAACoD,oBAAoB,CAAC;UACxBnB,IAAI,EAAE,UAAU;UAChBoB,MAAM,EAAErD,IAAI,CAACvD,WAAW,CAAC8G,cAAc,CAAClI,IAAI,CAACN,IAAI,CAAC4H,KAAK;QACzD,CAAC,CAAC;QAEF;MAEF,KAAK,iBAAiB;QAGpB,MAAMa,IAAI,GAAGxD,IAAI,CAACjC,UAAU,CAC1BiC,IAAI,CAACL,WAAW,CAAC,CAAC,EAClBK,IAAI,CAACgC,iBAAiB,CAAC3G,IAAI,CAACkH,GAAG,CAAC,cAAc,CAAC,CACjD,CAAC;QAEDH,KAAK,GAAG,IAAI,CAACzF,GAAG,CAAC,CAAC;QAClB,MAAM8G,UAAU,GAAG,IAAI,CAAC9G,GAAG,CAAC,CAAC;QAC7B,IAAI+G,SAAuB,GAAGD,UAAU;QACxC,MAAME,QAA4B,GAAG,EAAE;QAGvC,MAAM1D,KAAK,GAAG5E,IAAI,CAACN,IAAI,CAACkF,KAAK,IAAI,EAAE;QAEnC,KAAK,IAAIK,CAAC,GAAGL,KAAK,CAAC3C,MAAM,GAAG,CAAC,EAAEgD,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;UAC1C,MAAMsD,CAAC,GAAG3D,KAAK,CAACK,CAAC,CAAC;UAElB,IAAIsD,CAAC,CAAC1E,IAAI,EAAE;YACVwE,SAAS,GAAG5G,WAAC,CAAC+G,qBAAqB,CACjC/G,WAAC,CAACgH,gBAAgB,CAAC,KAAK,EAAEhH,WAAC,CAACK,SAAS,CAACqG,IAAI,CAAC,EAAEI,CAAC,CAAC1E,IAAI,CAAC,EACnDyE,QAAQ,CAACrD,CAAC,CAAC,GAAG,IAAI,CAAC3D,GAAG,CAAC,CAAC,EACzB+G,SACF,CAAC;UACH,CAAC,MAAM;YACLC,QAAQ,CAACrD,CAAC,CAAC,GAAGmD,UAAU;UAC1B;QACF;QAEA,MAAMM,YAAY,GAAG1I,IAAI,CAACkH,GAAG,CAAC,cAAc,CAAC;QAC7C9H,IAAI,CAACiB,mBAAmB,CAACqI,YAAY,EAAEL,SAAS,CAAC;QACjD1D,IAAI,CAAClB,IAAI,CAACkB,IAAI,CAACgC,iBAAiB,CAAC+B,YAAY,CAAC,CAAC;QAE/C/D,IAAI,CAACvD,WAAW,CAACgG,SAAS,CAAC,IAAIlI,IAAI,CAACyJ,WAAW,CAAC5B,KAAK,CAAC,EAAE,YAAY;UAClE/G,IAAI,CAACkH,GAAG,CAAC,OAAO,CAAC,CAACnC,OAAO,CAAC,UAAU6D,QAAQ,EAAE;YAC5C,MAAM3D,CAAC,GAAG2D,QAAQ,CAACC,GAAa;YAChClE,IAAI,CAAC5C,IAAI,CAACuG,QAAQ,CAACrD,CAAC,CAAC,CAAC;YAEtB2D,QAAQ,CAAC1B,GAAG,CAAC,YAAY,CAAC,CAACnC,OAAO,CAAC,UAAU/E,IAAI,EAAE;cACjD2E,IAAI,CAAC+B,gBAAgB,CAAC1G,IAAI,CAAC;YAC7B,CAAC,CAAC;UACJ,CAAC,CAAC;QACJ,CAAC,CAAC;QAEF2E,IAAI,CAAC5C,IAAI,CAACgF,KAAK,CAAC;QAChB,IAAIqB,UAAU,CAAClG,KAAK,KAAK5C,gBAAgB,EAAE;UACzCqF,IAAI,CAAC5C,IAAI,CAACqG,UAAU,CAAC;UACrBjG,OAAM,CAACC,WAAW,CAAC2E,KAAK,CAAC7E,KAAK,EAAEkG,UAAU,CAAClG,KAAK,CAAC;QACnD;QAEA;MAEF,KAAK,aAAa;QAChB,MAAM4G,OAAO,GAAG9I,IAAI,CAACN,IAAI,CAACqJ,SAAS,IAAI,IAAI,CAACzH,GAAG,CAAC,CAAC;QACjDyF,KAAK,GAAG,IAAI,CAACzF,GAAG,CAAC,CAAC;QAElBqD,IAAI,CAACX,SAAS,CACZW,IAAI,CAACgC,iBAAiB,CAAC3G,IAAI,CAACkH,GAAG,CAAC,MAAM,CAAC,CAAC,EACxC4B,OAAO,IAAI/B,KACb,CAAC;QAEDpC,IAAI,CAAC+B,gBAAgB,CAAC1G,IAAI,CAACkH,GAAG,CAAC,YAAY,CAAC,CAAC;QAE7C,IAAI4B,OAAO,EAAE;UACXnE,IAAI,CAAClB,IAAI,CAACsD,KAAK,CAAC;UAChBpC,IAAI,CAAC5C,IAAI,CAAC+G,OAAO,CAAC;UAClBnE,IAAI,CAAC+B,gBAAgB,CAAC1G,IAAI,CAACkH,GAAG,CAAC,WAAW,CAAC,CAAC;QAC9C;QAEAvC,IAAI,CAAC5C,IAAI,CAACgF,KAAK,CAAC;QAEhB;MAEF,KAAK,iBAAiB;QACpBpC,IAAI,CAACoD,oBAAoB,CAAC;UACxBnB,IAAI,EAAE,QAAQ;UACd1E,KAAK,EAAEyC,IAAI,CAACgC,iBAAiB,CAAC3G,IAAI,CAACkH,GAAG,CAAC,UAAU,CAAC;QACpD,CAAC,CAAC;QAEF;MAEF,KAAK,eAAe;QAClB,MAAM,IAAIvH,KAAK,CAAC,qDAAqD,CAAC;MAExE,KAAK,cAAc;QACjBoH,KAAK,GAAG,IAAI,CAACzF,GAAG,CAAC,CAAC;QAElB,MAAM0H,OAAO,GAAGhJ,IAAI,CAACN,IAAI,CAACsJ,OAAO;QAEjC,MAAMC,QAAQ,GAAGD,OAAO,IAAI,IAAI,CAAC1H,GAAG,CAAC,CAAC;QACtC,MAAM2E,UAAU,GACdgD,QAAQ,IAAI,IAAI/J,IAAI,CAACgK,UAAU,CAACD,QAAQ,EAAED,OAAO,CAACG,KAAY,CAAC;QAEjE,MAAMC,UAAU,GAAGpJ,IAAI,CAACN,IAAI,CAAC2J,SAAS,IAAI,IAAI,CAAC/H,GAAG,CAAC,CAAC;QACpD,MAAM6E,YAAY,GAChBiD,UAAU,IAAI,IAAIlK,IAAI,CAACoK,YAAY,CAACF,UAAU,EAAErC,KAAK,CAAC;QAExD,MAAMnB,QAAQ,GAAG,IAAI1G,IAAI,CAACqK,QAAQ,CAChC5E,IAAI,CAAC6E,qBAAqB,CAAC,CAAC,EAC5BvD,UAAU,EACVE,YACF,CAAC;QAEDxB,IAAI,CAACxD,UAAU,CAACsB,IAAI,CAACmD,QAAQ,CAAC;QAC9BjB,IAAI,CAAC8E,oBAAoB,CAAC7D,QAAQ,CAACE,QAAQ,CAAC;QAE5CnB,IAAI,CAACvD,WAAW,CAACgG,SAAS,CAACxB,QAAQ,EAAE,YAAY;UAC/CjB,IAAI,CAAC+B,gBAAgB,CAAC1G,IAAI,CAACkH,GAAG,CAAC,OAAO,CAAC,CAAC;UAExC,IAAI+B,QAAQ,EAAE;YACZ,IAAIG,UAAU,EAAE;cAIdzE,IAAI,CAAClB,IAAI,CAAC2F,UAAU,CAAC;YACvB,CAAC,MAAM;cAGLzE,IAAI,CAAClB,IAAI,CAACsD,KAAK,CAAC;YAClB;YAEApC,IAAI,CAAC8E,oBAAoB,CAAC9E,IAAI,CAAC5C,IAAI,CAACkH,QAAQ,CAAC,CAAC;YAE9C,MAAMS,QAAQ,GAAG1J,IAAI,CAACkH,GAAG,CAAC,cAAc,CAAC;YACzC,MAAMyC,SAAS,GAAGhF,IAAI,CAACL,WAAW,CAAC,CAAC;YACpCK,IAAI,CAACvB,qBAAqB,CAACwC,QAAQ,CAACE,QAAQ,EAAE6D,SAAS,CAAC;YAExDD,QAAQ,CAACE,QAAQ,CAAC9J,iBAAiB,EAAE;cACnCQ,YAAY,EAAEA,CAAA,KAAMmB,WAAC,CAACK,SAAS,CAAC6H,SAAS,CAAC;cAC1CxJ,cAAc,EAEZ6I,OAAO,CAACG,KAAK,CAACjJ;YAClB,CAAC,CAAC;YAEFyE,IAAI,CAACvD,WAAW,CAACgG,SAAS,CAACnB,UAAU,EAAE,YAAY;cACjDtB,IAAI,CAAC+B,gBAAgB,CAACgD,QAAQ,CAAC;YACjC,CAAC,CAAC;UACJ;UAEA,IAAIN,UAAU,EAAE;YACdzE,IAAI,CAAC8E,oBAAoB,CAAC9E,IAAI,CAAC5C,IAAI,CAACqH,UAAU,CAAC,CAAC;YAEhDzE,IAAI,CAACvD,WAAW,CAACgG,SAAS,CAACjB,YAAY,EAAE,YAAY;cACnDxB,IAAI,CAAC+B,gBAAgB,CAAC1G,IAAI,CAACkH,GAAG,CAAC,WAAW,CAAC,CAAC;YAC9C,CAAC,CAAC;YAEFvC,IAAI,CAACtC,IAAI,CACPZ,WAAC,CAAC4D,eAAe,CACf5D,WAAC,CAAC+B,cAAc,CAACmB,IAAI,CAAC5B,eAAe,CAAC,QAAQ,CAAC,EAAE,CAC/CoD,YAAY,CAACL,QAAQ,CACtB,CACH,CACF,CAAC;UACH;QACF,CAAC,CAAC;QAEFnB,IAAI,CAAC5C,IAAI,CAACgF,KAAK,CAAC;QAEhB;MAEF,KAAK,gBAAgB;QACnBpC,IAAI,CAACtC,IAAI,CACPZ,WAAC,CAACoI,cAAc,CAAClF,IAAI,CAACgC,iBAAiB,CAAC3G,IAAI,CAACkH,GAAG,CAAC,UAAU,CAAC,CAAC,CAC/D,CAAC;QAED;MAEF,KAAK,kBAAkB;QACrBvC,IAAI,CAACtC,IAAI,CAACsC,IAAI,CAACmF,YAAY,CAAC9J,IAAI,CAAC,CAAC;QAClC;MAEF;QACE,MAAM,IAAIL,KAAK,CACb,4BAA4B,GAAGC,IAAI,CAACC,SAAS,CAACmF,IAAI,CAAC4B,IAAI,CACzD,CAAC;IACL;EACF;EAEAmB,oBAAoBA,CAACgC,MAAwB,EAAE;IAC7C5H,OAAM,CAAC6H,cAAc,CACnBD,MAAM,CAACnD,IAAI,EACX,QAAQ,EACR,mCACF,CAAC;IAED,MAAMqD,UAA4C,GAAG,CACnDxI,WAAC,CAACyB,aAAa,CAAC6G,MAAM,CAACnD,IAAI,CAAC,CAC7B;IAED,IAAImD,MAAM,CAACnD,IAAI,KAAK,OAAO,IAAImD,MAAM,CAACnD,IAAI,KAAK,UAAU,EAAE;MACzDqD,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAChJ,YAAY,CAACiJ,GAAG,CAACH,MAAM,CAAC/B,MAAM,CAAC,GAChD+B,MAAM,CAAC/B,MAAM,GACbvG,WAAC,CAACK,SAAS,CAACiI,MAAM,CAAC/B,MAAM,CAAC;IAChC,CAAC,MAAM,IAAI+B,MAAM,CAACnD,IAAI,KAAK,QAAQ,IAAImD,MAAM,CAACnD,IAAI,KAAK,OAAO,EAAE;MAC9D,IAAImD,MAAM,CAAC7H,KAAK,EAAE;QAChB+H,UAAU,CAAC,CAAC,CAAC,GAAGxI,WAAC,CAACK,SAAS,CAACiI,MAAM,CAAC7H,KAAK,CAAC;MAC3C;IACF;IAEA,IAAI,CAACG,IAAI,CACPZ,WAAC,CAAC4D,eAAe,CACf5D,WAAC,CAAC+B,cAAc,CAAC,IAAI,CAACT,eAAe,CAAC,QAAQ,CAAC,EAAEkH,UAAU,CAC7D,CACF,CAAC;EACH;EAWAT,qBAAqBA,CAAA,EAAG;IACtB,OAAO/H,WAAC,CAACC,cAAc,CAAC,IAAI,CAACX,OAAO,CAACkB,MAAM,CAAC;EAC9C;EAYAwH,oBAAoBA,CAACnI,GAAqB,EAAE;IAC1C,IAAIA,GAAG,EAAE;MACP,IAAIA,GAAG,CAACY,KAAK,KAAK5C,gBAAgB,EAAE;QAGlCgC,GAAG,CAACY,KAAK,GAAG,IAAI,CAACnB,OAAO,CAACkB,MAAM;MACjC,CAAC,MAAM;QAELE,OAAM,CAACC,WAAW,CAACd,GAAG,CAACY,KAAK,EAAE,IAAI,CAACnB,OAAO,CAACkB,MAAM,CAAC;MACpD;IACF,CAAC,MAAM;MACLX,GAAG,GAAG,IAAI,CAACkI,qBAAqB,CAAC,CAAC;IACpC;IAKA,IAAI,CAAC9G,UAAU,CAAC,IAAI,CAACK,eAAe,CAAC,MAAM,CAAC,EAAEzB,GAAG,CAAC;EACpD;EAWA6I,iBAAiBA,CACfC,OAA2B,EAC3BC,SAAiC,EACjCC,kBAA2B,EAC3BC,iBAA2B,EAC3B;IACApI,OAAM,CAAC4D,EAAE,CACP,CAACwE,iBAAiB,IAAI,CAACH,OAAO,EAC9B,8DAA8D,GAC5D,sCACJ,CAAC;IAED,IAAII,MAAM,GAAG,IAAI,CAAC7D,iBAAiB,CAAC0D,SAAS,EAAEE,iBAAiB,CAAC;IAEjE,IAAIA,iBAAiB,EAAE,CAEvB,CAAC,MAAM,IAAIH,OAAO,IAAKE,kBAAkB,IAAI,CAAC7I,WAAC,CAACgJ,SAAS,CAACD,MAAM,CAAE,EAAE;MAYlEA,MAAM,GAAG,IAAI,CAAC9H,UAAU,CAAC0H,OAAO,IAAI,IAAI,CAAC9F,WAAW,CAAC,CAAC,EAAEkG,MAAM,CAAC;IACjE;IACA,OAAOA,MAAM;EACf;EAEA7D,iBAAiBA,CACf3G,IAA4B,EAC5BuG,YAAsB,EACR;IACd,MAAMmE,IAAI,GAAG1K,IAAI,CAACN,IAAI;IACtB,IAAI,CAACgL,IAAI,EAAE;MACT,OAAOA,IAAI;IACb;IAEA,MAAM/F,IAAI,GAAG,IAAI;IACjB,IAAI6F,MAAM;IACV,IAAIzD,KAAK;IAET,SAAS4D,MAAMA,CAACD,IAAkB,EAAE;MAClC,IAAInE,YAAY,EAAE;QAChB5B,IAAI,CAACtC,IAAI,CAACqI,IAAI,CAAC;MACjB;MACA,OAAOA,IAAI;IACb;IAIA,IAAI,CAACvL,IAAI,CAACgI,YAAY,CAACuD,IAAI,CAAC,EAAE;MAC5B,OAAOC,MAAM,CAACD,IAAI,CAAC;IACrB;IAMA,MAAMJ,kBAAkB,GAAGnL,IAAI,CAACgI,YAAY,CAACyD,YAAY,CAACF,IAAI,CAAC;IAM/D,QAAQ1K,IAAI,CAAC4G,IAAI;MACf,KAAK,kBAAkB;QACrB,OAAO+D,MAAM,CACXlJ,WAAC,CAACwB,gBAAgB,CAChB0B,IAAI,CAACgC,iBAAiB,CACpB3G,IAAI,CAACkH,GAAG,CAAC,QAAQ,CACnB,CAAC,EACDlH,IAAI,CAACN,IAAI,CAACsD,QAAQ,GACd2B,IAAI,CAACwF,iBAAiB,CACpB,IAAI,EACJnK,IAAI,CAACkH,GAAG,CAAC,UAAU,CAAC,EACpBoD,kBACF,CAAC,GACDtK,IAAI,CAACN,IAAI,CAACmL,QAAQ,EACtB7K,IAAI,CAACN,IAAI,CAACsD,QACZ,CACF,CAAC;MAEH,KAAK,gBAAgB;QACnB,MAAM8H,UAAU,GAAG9K,IAAI,CAACkH,GAAG,CAAC,QAAQ,CAAC;QACrC,MAAM6D,QAAQ,GAAG/K,IAAI,CAACkH,GAAG,CAAC,WAAW,CAAC;QAEtC,IAAI8D,SAAS;QACb,IAAIC,OAAO;QAEX,MAAMC,cAAc,GAAGH,QAAQ,CAACI,IAAI,CAACC,OAAO,IAC1CjM,IAAI,CAACgI,YAAY,CAACiE,OAAO,CAAC1L,IAAI,CAChC,CAAC;QAED,IAAI2L,cAAc,GAAG,IAAI;QAEzB,IAAI5J,WAAC,CAAC6J,kBAAkB,CAACR,UAAU,CAACpL,IAAI,CAAC,EAAE;UACzC,IAAIwL,cAAc,EAAE;YAOlB,MAAMK,SAAS,GAAG5G,IAAI,CAACwF,iBAAiB,CAGtCxF,IAAI,CAACL,WAAW,CAAC,CAAC,EAClBwG,UAAU,CAAC5D,GAAG,CAAC,QAAQ,CAAC,EACxBoD,kBACF,CAAC;YAED,MAAMkB,WAAW,GAAGV,UAAU,CAACpL,IAAI,CAACsD,QAAQ,GACxC2B,IAAI,CAACwF,iBAAiB,CACpB,IAAI,EACJW,UAAU,CAAC5D,GAAG,CAAC,UAAU,CAAC,EAC1BoD,kBACF,CAAC,GACDQ,UAAU,CAACpL,IAAI,CAACmL,QAAQ;YAE5BQ,cAAc,GAAGE,SAAS;YAE1BP,SAAS,GAAGvJ,WAAC,CAACwB,gBAAgB,CAC5BxB,WAAC,CAACwB,gBAAgB,CAChBxB,WAAC,CAACK,SAAS,CAACyJ,SAAS,CAAC,EACtBC,WAAW,EACXV,UAAU,CAACpL,IAAI,CAACsD,QAClB,CAAC,EACDvB,WAAC,CAAC0B,UAAU,CAAC,MAAM,CAAC,EACpB,KACF,CAAC;UACH,CAAC,MAAM;YACL6H,SAAS,GAAGrG,IAAI,CAACgC,iBAAiB,CAChCmE,UACF,CAAC;UACH;QACF,CAAC,MAAM;UACLE,SAAS,GAAGrG,IAAI,CAACwF,iBAAiB,CAChC,IAAI,EACJW,UAAU,EACVR,kBACF,CAAC;UAED,IAAI7I,WAAC,CAAC6J,kBAAkB,CAACN,SAAS,CAAC,EAAE;YASnCA,SAAS,GAAGvJ,WAAC,CAACgK,kBAAkB,CAAC,CAC/BhK,WAAC,CAACC,cAAc,CAAC,CAAC,CAAC,EACnBD,WAAC,CAACK,SAAS,CAACkJ,SAAS,CAAC,CACvB,CAAC;UACJ;QACF;QAEA,IAAIE,cAAc,EAAE;UAClBD,OAAO,GAAGF,QAAQ,CAACpF,GAAG,CAACyF,OAAO,IAC5BzG,IAAI,CAACwF,iBAAiB,CACpB,IAAI,EACJiB,OAAO,EACPd,kBACF,CACF,CAAC;UACD,IAAIe,cAAc,EAAEJ,OAAO,CAACS,OAAO,CAACL,cAAc,CAAC;UAEnDJ,OAAO,GAAGA,OAAO,CAACtF,GAAG,CAACgG,GAAG,IAAIlK,WAAC,CAACK,SAAS,CAAC6J,GAAG,CAAC,CAAC;QAChD,CAAC,MAAM;UACLV,OAAO,GAAGjL,IAAI,CAACN,IAAI,CAACkM,SAAS;QAC/B;QAEA,OAAOjB,MAAM,CAAClJ,WAAC,CAAC+B,cAAc,CAACwH,SAAS,EAAEC,OAAO,CAAC,CAAC;MAErD,KAAK,eAAe;QAClB,OAAON,MAAM,CACXlJ,WAAC,CAACoK,aAAa,CACblH,IAAI,CAACwF,iBAAiB,CACpB,IAAI,EACJnK,IAAI,CAACkH,GAAG,CAAC,QAAQ,CAAC,EAClBoD,kBACF,CAAC,EACDtK,IAAI,CAACkH,GAAG,CAAC,WAAW,CAAC,CAACvB,GAAG,CAAC,UAAUyF,OAAY,EAAE;UAChD,OAAOzG,IAAI,CAACwF,iBAAiB,CAAC,IAAI,EAAEiB,OAAO,EAAEd,kBAAkB,CAAC;QAClE,CAAC,CACH,CACF,CAAC;MAEH,KAAK,kBAAkB;QACrB,OAAOK,MAAM,CACXlJ,WAAC,CAACqK,gBAAgB,CAChB9L,IAAI,CAACkH,GAAG,CAAC,YAAY,CAAC,CAACvB,GAAG,CAAC,UAAUoG,QAAQ,EAAE;UAC7C,IAAIA,QAAQ,CAACC,gBAAgB,CAAC,CAAC,EAAE;YAC/B,OAAOvK,WAAC,CAACwK,cAAc,CACrBF,QAAQ,CAACrM,IAAI,CAACmJ,GAAG,EACjBlE,IAAI,CAACwF,iBAAiB,CACpB,IAAI,EAEJ4B,QAAQ,CAAC7E,GAAG,CAAC,OAAO,CAAC,EACrBoD,kBACF,CAAC,EACDyB,QAAQ,CAACrM,IAAI,CAACsD,QAChB,CAAC;UACH,CAAC,MAAM;YACL,OAAO+I,QAAQ,CAACrM,IAAI;UACtB;QACF,CAAC,CACH,CACF,CAAC;MAEH,KAAK,iBAAiB;QACpB,OAAOiL,MAAM,CACXlJ,WAAC,CAACiE,eAAe,CACf1F,IAAI,CAACkH,GAAG,CAAC,UAAU,CAAC,CAACvB,GAAG,CAAC,UAAUuG,QAAa,EAAE;UAChD,IAAI,CAACA,QAAQ,CAACxM,IAAI,EAAE;YAClB,OAAO,IAAI;UACb;UACA,IAAIwM,QAAQ,CAACC,eAAe,CAAC,CAAC,EAAE;YAC9B,OAAO1K,WAAC,CAAC2K,aAAa,CACpBzH,IAAI,CAACwF,iBAAiB,CACpB,IAAI,EACJ+B,QAAQ,CAAChF,GAAG,CAAC,UAAU,CAAC,EACxBoD,kBACF,CACF,CAAC;UACH,CAAC,MAAM;YACL,OAAO3F,IAAI,CAACwF,iBAAiB,CAC3B,IAAI,EACJ+B,QAAQ,EACR5B,kBACF,CAAC;UACH;QACF,CAAC,CACH,CACF,CAAC;MAEH,KAAK,oBAAoB;QACvB,MAAM+B,SAAS,GAAGrM,IAAI,CAACN,IAAI,CAAC4M,WAAW,CAACrK,MAAM,GAAG,CAAC;QAElDjC,IAAI,CAACkH,GAAG,CAAC,aAAa,CAAC,CAACnC,OAAO,CAAC,UAAUwH,QAAa,EAAE;UACvD,IAAIA,QAAQ,CAAC1D,GAAG,KAAKwD,SAAS,EAAE;YAC9B7B,MAAM,GAAG7F,IAAI,CAACgC,iBAAiB,CAAC4F,QAAQ,EAAEhG,YAAY,CAAC;UACzD,CAAC,MAAM;YACL5B,IAAI,CAACgC,iBAAiB,CAAC4F,QAAQ,EAAE,IAAI,CAAC;UACxC;QACF,CAAC,CAAC;QAEF,OAAO/B,MAAM;MAEf,KAAK,mBAAmB;QACtBzD,KAAK,GAAG,IAAI,CAACzF,GAAG,CAAC,CAAC;QAElB,IAAI,CAACiF,YAAY,EAAE;UACjBiE,MAAM,GAAG7F,IAAI,CAACL,WAAW,CAAC,CAAC;QAC7B;QAEA,MAAMwD,IAAI,GAAGnD,IAAI,CAACwF,iBAAiB,CACjCK,MAAM,EACNxK,IAAI,CAACkH,GAAG,CAAC,MAAM,CAAC,EAChBoD,kBACF,CAAC;QAED,IAAItK,IAAI,CAACN,IAAI,CAACyE,QAAQ,KAAK,IAAI,EAAE;UAC/BQ,IAAI,CAACX,SAAS,CAAC8D,IAAI,EAAEf,KAAK,CAAC;QAC7B,CAAC,MAAM;UACL5E,OAAM,CAACC,WAAW,CAACpC,IAAI,CAACN,IAAI,CAACyE,QAAQ,EAAE,IAAI,CAAC;UAC5CQ,IAAI,CAACf,MAAM,CAACkE,IAAI,EAAEf,KAAK,CAAC;QAC1B;QAEApC,IAAI,CAACwF,iBAAiB,CACpBK,MAAM,EACNxK,IAAI,CAACkH,GAAG,CAAC,OAAO,CAAC,EACjBoD,kBAAkB,EAClB/D,YACF,CAAC;QAED5B,IAAI,CAAC5C,IAAI,CAACgF,KAAK,CAAC;QAEhB,OAAOyD,MAAM;MAEf,KAAK,uBAAuB;QAC1B,MAAM1B,OAAO,GAAG,IAAI,CAACxH,GAAG,CAAC,CAAC;QAC1ByF,KAAK,GAAG,IAAI,CAACzF,GAAG,CAAC,CAAC;QAClB,MAAMuC,IAAI,GAAGc,IAAI,CAACgC,iBAAiB,CAAC3G,IAAI,CAACkH,GAAG,CAAC,MAAM,CAAC,CAAC;QAErDvC,IAAI,CAACX,SAAS,CAACH,IAAI,EAAEiF,OAAO,CAAC;QAE7B,IAAI,CAACvC,YAAY,EAAE;UACjBiE,MAAM,GAAG7F,IAAI,CAACL,WAAW,CAAC,CAAC;QAC7B;QAEAK,IAAI,CAACwF,iBAAiB,CACpBK,MAAM,EACNxK,IAAI,CAACkH,GAAG,CAAC,YAAY,CAAC,EACtBoD,kBAAkB,EAClB/D,YACF,CAAC;QACD5B,IAAI,CAAClB,IAAI,CAACsD,KAAK,CAAC;QAEhBpC,IAAI,CAAC5C,IAAI,CAAC+G,OAAO,CAAC;QAClBnE,IAAI,CAACwF,iBAAiB,CACpBK,MAAM,EACNxK,IAAI,CAACkH,GAAG,CAAC,WAAW,CAAC,EACrBoD,kBAAkB,EAClB/D,YACF,CAAC;QAED5B,IAAI,CAAC5C,IAAI,CAACgF,KAAK,CAAC;QAEhB,OAAOyD,MAAM;MAEf,KAAK,iBAAiB;QACpB,OAAOG,MAAM,CACXlJ,WAAC,CAAC4C,eAAe,CACfrE,IAAI,CAACN,IAAI,CAACyE,QAAQ,EAGlBQ,IAAI,CAACgC,iBAAiB,CAAC3G,IAAI,CAACkH,GAAG,CAAC,UAAU,CAAC,CAAC,EAC5C,CAAC,CAAClH,IAAI,CAACN,IAAI,CAAC8M,MACd,CACF,CAAC;MAEH,KAAK,kBAAkB;QACrB,OAAO7B,MAAM,CACXlJ,WAAC,CAACgH,gBAAgB,CAChBzI,IAAI,CAACN,IAAI,CAACyE,QAAQ,EAClBQ,IAAI,CAACwF,iBAAiB,CACpB,IAAI,EACJnK,IAAI,CAACkH,GAAG,CAAC,MAAM,CAAC,EAChBoD,kBACF,CAAC,EACD3F,IAAI,CAACwF,iBAAiB,CAAC,IAAI,EAAEnK,IAAI,CAACkH,GAAG,CAAC,OAAO,CAAC,EAAEoD,kBAAkB,CACpE,CACF,CAAC;MAEH,KAAK,sBAAsB;QACzB,IAAItK,IAAI,CAACN,IAAI,CAACyE,QAAQ,KAAK,GAAG,EAAE;UAI9B,OAAOwG,MAAM,CACXlJ,WAAC,CAACqB,oBAAoB,CACpB9C,IAAI,CAACN,IAAI,CAACyE,QAAQ,EAElBQ,IAAI,CAACgC,iBAAiB,CAAC3G,IAAI,CAACkH,GAAG,CAAC,MAAM,CAAC,CAAC,EACxCvC,IAAI,CAACgC,iBAAiB,CAAC3G,IAAI,CAACkH,GAAG,CAAC,OAAO,CAAC,CAC1C,CACF,CAAC;QACH;QAGA,MAAMvE,GAAG,GAAGgC,IAAI,CAACgC,iBAAiB,CAAC3G,IAAI,CAACkH,GAAG,CAAC,MAAM,CAAC,CAAC;QACpD,MAAMuF,IAAI,GAAG9H,IAAI,CAACjC,UAAU,CAACiC,IAAI,CAACL,WAAW,CAAC,CAAC,EAAE3B,GAAG,CAAC;QAcrD,OAAOgI,MAAM,CACXlJ,WAAC,CAACqB,oBAAoB,CACpB,GAAG,EAEHrB,WAAC,CAACK,SAAS,CAACa,GAAG,CAAC,EAChBlB,WAAC,CAACqB,oBAAoB,CACpB9C,IAAI,CAACN,IAAI,CAACyE,QAAQ,EAClB1C,WAAC,CAACK,SAAS,CAAC2K,IAAI,CAAC,EACjB9H,IAAI,CAACgC,iBAAiB,CAAC3G,IAAI,CAACkH,GAAG,CAAC,OAAO,CAAC,CAC1C,CACF,CACF,CAAC;MAEH,KAAK,kBAAkB;QACrB,OAAOyD,MAAM,CACXlJ,WAAC,CAACiL,gBAAgB,CAChB1M,IAAI,CAACN,IAAI,CAACyE,QAAQ,EAClBQ,IAAI,CAACgC,iBAAiB,CAAC3G,IAAI,CAACkH,GAAG,CAAC,UAAU,CAAC,CAAC,EAG5ClH,IAAI,CAACN,IAAI,CAAC8M,MACZ,CACF,CAAC;MAEH,KAAK,iBAAiB;QACpBzF,KAAK,GAAG,IAAI,CAACzF,GAAG,CAAC,CAAC;QAClB,MAAMqK,GAAG,GACP3L,IAAI,CAACN,IAAI,CAAC0E,QAAQ,IAAIO,IAAI,CAACgC,iBAAiB,CAAC3G,IAAI,CAACkH,GAAG,CAAC,UAAU,CAAC,CAAC;QAEpE,IAAIyE,GAAG,IAAI3L,IAAI,CAACN,IAAI,CAACiN,QAAQ,EAAE;UAC7B,MAAMnC,MAAM,GAAG7F,IAAI,CAACL,WAAW,CAAC,CAAC;UAEjC,MAAMsI,GAAG,GAAGnL,WAAC,CAAC4D,eAAe,CAC3B5D,WAAC,CAAC+B,cAAc,CAACmB,IAAI,CAAC5B,eAAe,CAAC,eAAe,CAAC,EAAE,CACtD4I,GAAG,EACHlK,WAAC,CAACyB,aAAa,CAAEsH,MAAM,CAACK,QAAQ,CAAkB3K,IAAI,CAAC,EACvD6G,KAAK,CACN,CACH,CAAC;UACD6F,GAAG,CAACtL,GAAG,GAAGoJ,IAAI,CAACpJ,GAAG;UAElBqD,IAAI,CAACtC,IAAI,CAACuK,GAAG,CAAC;UACdjI,IAAI,CAAC5C,IAAI,CAACgF,KAAK,CAAC;UAEhB,OAAOyD,MAAM;QACf;QAEA7F,IAAI,CAACjC,UAAU,CAACiC,IAAI,CAAC5B,eAAe,CAAC,MAAM,CAAC,EAAEgE,KAAK,CAAC;QAEpD,MAAM6F,GAAG,GAAGnL,WAAC,CAAC4D,eAAe,CAAC5D,WAAC,CAACK,SAAS,CAAC6J,GAAG,CAAC,IAAI,IAAI,CAAC;QAGvDiB,GAAG,CAACtL,GAAG,GAAGoJ,IAAI,CAACpJ,GAAG;QAClBqD,IAAI,CAACtC,IAAI,CAACuK,GAAG,CAAC;QACdjI,IAAI,CAAC5C,IAAI,CAACgF,KAAK,CAAC;QAEhB,OAAOpC,IAAI,CAAC5B,eAAe,CAAC,MAAM,CAAC;MAErC,KAAK,iBAAiB;QACpB,OAAO4H,MAAM,CAAChG,IAAI,CAACmF,YAAY,CAAC9J,IAAI,CAAC,CAAC;MAExC;QACE,MAAM,IAAIL,KAAK,CACb,6BAA6B,GAAGC,IAAI,CAACC,SAAS,CAAC6K,IAAI,CAAC9D,IAAI,CAC1D,CAAC;IACL;EACF;EAEAkD,YAAYA,CAAoB9J,IAAiB,EAAK;IACpD,MAAM6M,iBAAiB,GAAG,EAAE;IAE5B,IAAI7M,IAAI,CAACN,IAAI,CAACoN,UAAU,EAAE;MACxBD,iBAAiB,CAACpK,IAAI,CAACzC,IAAI,CAACkH,GAAG,CAAC,YAAY,CAAC,CAAC;IAChD;IAEAlH,IAAI,CAACkH,GAAG,CAAC,WAAW,CAAC,CAACnC,OAAO,CAAEgI,MAAW,IAAK;MAC7C,IAAIA,MAAM,CAACrN,IAAI,CAACsD,QAAQ,EAAE;QACxB6J,iBAAiB,CAACpK,IAAI,CAACsK,MAAM,CAAC7F,GAAG,CAAC,KAAK,CAAC,CAAC;MAC3C;IACF,CAAC,CAAC;IAEF,MAAMoD,kBAAkB,GAAGuC,iBAAiB,CAAC1B,IAAI,CAAC6B,KAAK,IACrD7N,IAAI,CAACgI,YAAY,CAAC6F,KAAK,CACzB,CAAC;IAED,KAAK,IAAI/H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4H,iBAAiB,CAAC5K,MAAM,EAAEgD,CAAC,EAAE,EAAE;MACjD,MAAM+H,KAAK,GAAGH,iBAAiB,CAAC5H,CAAC,CAAC;MAClC,MAAMgI,MAAM,GAAGhI,CAAC,KAAK4H,iBAAiB,CAAC5K,MAAM,GAAG,CAAC;MAEjD,IAAIgL,MAAM,EAAE;QACVD,KAAK,CAACE,WAAW,CAAC,IAAI,CAACvG,iBAAiB,CAACqG,KAAK,CAAC,CAAC;MAClD,CAAC,MAAM;QACLA,KAAK,CAACE,WAAW,CACf,IAAI,CAAC/C,iBAAiB,CAAC,IAAI,EAAE6C,KAAK,EAAE1C,kBAAkB,CACxD,CAAC;MACH;IACF;IAEA,OAAOtK,IAAI,CAACN,IAAI;EAClB;AACF;AAACyN,OAAA,CAAAxM,OAAA,GAAAA,OAAA", "ignoreList": []}