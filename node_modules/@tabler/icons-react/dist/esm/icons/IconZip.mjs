/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconZip = createReactComponent("outline", "zip", "IconZip", [["path", { "d": "M16 16v-8h2a2 2 0 1 1 0 4h-2", "key": "svg-0" }], ["path", { "d": "M12 8v8", "key": "svg-1" }], ["path", { "d": "M4 8h4l-4 8h4", "key": "svg-2" }]]);

export { IconZip as default };
//# sourceMappingURL=IconZip.mjs.map
