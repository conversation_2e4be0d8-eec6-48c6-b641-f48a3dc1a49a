/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconWaveSawTool = createReactComponent("outline", "wave-saw-tool", "IconWaveSawTool", [["path", { "d": "M3 12h5l4 8v-16l4 8h5", "key": "svg-0" }]]);

export { IconWaveSawTool as default };
//# sourceMappingURL=IconWaveSawTool.mjs.map
