/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconWorldMinus = createReactComponent("outline", "world-minus", "IconWorldMinus", [["path", { "d": "M20.483 15.006a9 9 0 1 0 -7.958 5.978", "key": "svg-0" }], ["path", { "d": "M3.6 9h16.8", "key": "svg-1" }], ["path", { "d": "M3.6 15h16.8", "key": "svg-2" }], ["path", { "d": "M11.5 3a17 17 0 0 0 0 18", "key": "svg-3" }], ["path", { "d": "M12.5 3a16.94 16.94 0 0 1 2.307 12", "key": "svg-4" }], ["path", { "d": "M16 19h6", "key": "svg-5" }]]);

export { IconWorldMinus as default };
//# sourceMappingURL=IconWorldMinus.mjs.map
