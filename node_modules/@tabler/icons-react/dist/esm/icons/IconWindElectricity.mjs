/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconWindElectricity = createReactComponent("outline", "wind-electricity", "IconWindElectricity", [["path", { "d": "M20 7l-3 5h4l-3 5", "key": "svg-0" }], ["path", { "d": "M3 16h4a2 2 0 1 1 0 4", "key": "svg-1" }], ["path", { "d": "M3 12h8a2 2 0 1 0 0 -4", "key": "svg-2" }], ["path", { "d": "M3 8h3a2 2 0 1 0 0 -4", "key": "svg-3" }]]);

export { IconWindElectricity as default };
//# sourceMappingURL=IconWindElectricity.mjs.map
