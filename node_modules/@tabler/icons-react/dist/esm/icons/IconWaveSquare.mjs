/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconWaveSquare = createReactComponent("outline", "wave-square", "IconWaveSquare", [["path", { "d": "M3 12h5v8h4v-16h4v8h5", "key": "svg-0" }]]);

export { IconWaveSquare as default };
//# sourceMappingURL=IconWaveSquare.mjs.map
