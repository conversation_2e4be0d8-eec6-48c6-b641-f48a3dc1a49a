/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconZoomCancel = createReactComponent("outline", "zoom-cancel", "IconZoomCancel", [["path", { "d": "M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0", "key": "svg-0" }], ["path", { "d": "M8 8l4 4", "key": "svg-1" }], ["path", { "d": "M12 8l-4 4", "key": "svg-2" }], ["path", { "d": "M21 21l-6 -6", "key": "svg-3" }]]);

export { IconZoomCancel as default };
//# sourceMappingURL=IconZoomCancel.mjs.map
