/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconWashDryShade = createReactComponent("outline", "wash-dry-shade", "IconWashDryShade", [["path", { "d": "M3 3m0 3a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3z", "key": "svg-0" }], ["path", { "d": "M3 11l8 -8", "key": "svg-1" }], ["path", { "d": "M3 17l14 -14", "key": "svg-2" }]]);

export { IconWashDryShade as default };
//# sourceMappingURL=IconWashDryShade.mjs.map
