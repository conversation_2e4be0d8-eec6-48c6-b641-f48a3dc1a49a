/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconXboxX = createReactComponent("outline", "xbox-x", "IconXboxX", [["path", { "d": "M12 21a9 9 0 0 0 9 -9a9 9 0 0 0 -9 -9a9 9 0 0 0 -9 9a9 9 0 0 0 9 9z", "key": "svg-0" }], ["path", { "d": "M9 8l6 8", "key": "svg-1" }], ["path", { "d": "M15 8l-6 8", "key": "svg-2" }]]);

export { IconXboxX as default };
//# sourceMappingURL=IconXboxX.mjs.map
