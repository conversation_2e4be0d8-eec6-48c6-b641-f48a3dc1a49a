/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconWoman = createReactComponent("outline", "woman", "IconWoman", [["path", { "d": "M10 16v5", "key": "svg-0" }], ["path", { "d": "M14 16v5", "key": "svg-1" }], ["path", { "d": "M8 16h8l-2 -7h-4z", "key": "svg-2" }], ["path", { "d": "M5 11c1.667 -1.333 3.333 -2 5 -2", "key": "svg-3" }], ["path", { "d": "M19 11c-1.667 -1.333 -3.333 -2 -5 -2", "key": "svg-4" }], ["path", { "d": "M12 4m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-5" }]]);

export { IconWoman as default };
//# sourceMappingURL=IconWoman.mjs.map
