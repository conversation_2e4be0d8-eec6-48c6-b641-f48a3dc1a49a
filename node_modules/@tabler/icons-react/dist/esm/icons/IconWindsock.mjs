/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconWindsock = createReactComponent("outline", "windsock", "IconWindsock", [["path", { "d": "M6 3v18", "key": "svg-0" }], ["path", { "d": "M6 11l12 -1v-4l-12 -1", "key": "svg-1" }], ["path", { "d": "M10 5.5v5", "key": "svg-2" }], ["path", { "d": "M14 6v4", "key": "svg-3" }], ["path", { "d": "M4 21h4", "key": "svg-4" }]]);

export { IconWindsock as default };
//# sourceMappingURL=IconWindsock.mjs.map
