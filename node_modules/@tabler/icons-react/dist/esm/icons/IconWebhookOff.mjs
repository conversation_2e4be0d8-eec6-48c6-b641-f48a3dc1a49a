/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconWebhookOff = createReactComponent("outline", "webhook-off", "IconWebhookOff", [["path", { "d": "M4.876 13.61a4 4 0 1 0 6.124 3.39h6", "key": "svg-0" }], ["path", { "d": "M15.066 20.502a4 4 0 0 0 4.763 -.675m1.171 -2.827a4 4 0 0 0 -4 -4", "key": "svg-1" }], ["path", { "d": "M16 8a4 4 0 0 0 -6.824 -2.833m-1.176 2.833c0 1.506 .77 2.818 2 3.5l-3 5.5", "key": "svg-2" }], ["path", { "d": "M3 3l18 18", "key": "svg-3" }]]);

export { IconWebhookOff as default };
//# sourceMappingURL=IconWebhookOff.mjs.map
