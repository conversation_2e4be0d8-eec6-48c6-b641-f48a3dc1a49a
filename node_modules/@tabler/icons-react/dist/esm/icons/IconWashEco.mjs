/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconWashEco = createReactComponent("outline", "wash-eco", "IconWashEco", [["path", { "d": "M3 6l1.721 10.329a2 2 0 0 0 1.973 1.671h5.306m8.162 -6.972l.838 -5.028", "key": "svg-0" }], ["path", { "d": "M3.486 8.965c.168 .02 .34 .033 .514 .035c.79 .009 1.539 -.178 2 -.5c.461 -.32 1.21 -.507 2 -.5c.79 -.007 1.539 .18 2 .5c.461 .322 1.21 .509 2 .5c.79 .009 1.539 -.178 2 -.5c.461 -.32 1.21 -.507 2 -.5c.79 -.007 1.539 .18 2 .5c.461 .322 1.21 .509 2 .5c.17 0 .339 -.014 .503 -.034", "key": "svg-1" }], ["path", { "d": "M16 22s0 -2 3 -4", "key": "svg-2" }], ["path", { "d": "M19 21a3 3 0 0 1 0 -6h3v3a3 3 0 0 1 -3 3z", "key": "svg-3" }]]);

export { IconWashEco as default };
//# sourceMappingURL=IconWashEco.mjs.map
