/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconWheelchair = createReactComponent("outline", "wheelchair", "IconWheelchair", [["path", { "d": "M8 16m-5 0a5 5 0 1 0 10 0a5 5 0 1 0 -10 0", "key": "svg-0" }], ["path", { "d": "M19 19m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-1" }], ["path", { "d": "M19 17a3 3 0 0 0 -3 -3h-3.4", "key": "svg-2" }], ["path", { "d": "M3 3h1a2 2 0 0 1 2 2v6", "key": "svg-3" }], ["path", { "d": "M6 8h11", "key": "svg-4" }], ["path", { "d": "M15 8v6", "key": "svg-5" }]]);

export { IconWheelchair as default };
//# sourceMappingURL=IconWheelchair.mjs.map
