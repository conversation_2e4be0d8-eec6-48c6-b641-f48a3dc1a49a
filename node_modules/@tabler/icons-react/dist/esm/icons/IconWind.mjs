/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconWind = createReactComponent("outline", "wind", "IconWind", [["path", { "d": "M5 8h8.5a2.5 2.5 0 1 0 -2.34 -3.24", "key": "svg-0" }], ["path", { "d": "M3 12h15.5a2.5 2.5 0 1 1 -2.34 3.24", "key": "svg-1" }], ["path", { "d": "M4 16h5.5a2.5 2.5 0 1 1 -2.34 3.24", "key": "svg-2" }]]);

export { IconWind as default };
//# sourceMappingURL=IconWind.mjs.map
