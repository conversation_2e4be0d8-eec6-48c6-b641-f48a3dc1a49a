/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconZodiacCancer = createReactComponent("outline", "zodiac-cancer", "IconZodiacCancer", [["path", { "d": "M6 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-0" }], ["path", { "d": "M18 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-1" }], ["path", { "d": "M3 12a10 6.5 0 0 1 14 -6.5", "key": "svg-2" }], ["path", { "d": "M21 12a10 6.5 0 0 1 -14 6.5", "key": "svg-3" }]]);

export { IconZodiacCancer as default };
//# sourceMappingURL=IconZodiacCancer.mjs.map
