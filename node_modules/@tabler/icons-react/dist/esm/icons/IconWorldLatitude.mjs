/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconWorldLatitude = createReactComponent("outline", "world-latitude", "IconWorldLatitude", [["path", { "d": "M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0", "key": "svg-0" }], ["path", { "d": "M4.6 7l14.8 0", "key": "svg-1" }], ["path", { "d": "M3 12l18 0", "key": "svg-2" }], ["path", { "d": "M4.6 17l14.8 0", "key": "svg-3" }]]);

export { IconWorldLatitude as default };
//# sourceMappingURL=IconWorldLatitude.mjs.map
