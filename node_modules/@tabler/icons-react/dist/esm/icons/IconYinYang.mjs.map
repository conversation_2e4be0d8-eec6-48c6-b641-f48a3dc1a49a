{"version": 3, "file": "IconYinYang.mjs", "sources": ["../../../src/icons/IconYinYang.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'yin-yang', 'IconYinYang', [[\"path\",{\"d\":\"M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M12 3a4.5 4.5 0 0 0 0 9a4.5 4.5 0 0 1 0 9\",\"key\":\"svg-1\"}],[\"circle\",{\"cx\":\"12\",\"cy\":\"7.5\",\"r\":\".5\",\"fill\":\"currentColor\",\"key\":\"svg-2\"}],[\"circle\",{\"cx\":\"12\",\"cy\":\"16.5\",\"r\":\".5\",\"fill\":\"currentColor\",\"key\":\"svg-3\"}]]);"], "names": [], "mappings": ";;;;;;;;;AACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,oBAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,aAAe,CAAA,CAAA,CAAC,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAC,CAAI,CAAA,CAAA,CAAA,CAAA,4CAAA,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAE,CAAA,CAAA,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAC,CAAI,CAAA,CAAA,CAAA,CAAA,2CAAA,CAA4C,CAAA,CAAA,CAAA,CAAA,CAAA,GAAM,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAE,CAAA,CAAA,CAAC,QAAS,CAAA,CAAA,CAAA,CAAC,KAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,eAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAE,CAAA,CAAC,QAAA,CAAS,CAAA,CAAA,CAAC,IAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAO,CAAA,CAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,MAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAC,CAAC,CAAA;;"}