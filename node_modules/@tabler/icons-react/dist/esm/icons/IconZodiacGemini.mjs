/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconZodiacGemini = createReactComponent("outline", "zodiac-gemini", "IconZodiacGemini", [["path", { "d": "M3 3a21 21 0 0 0 18 0", "key": "svg-0" }], ["path", { "d": "M3 21a21 21 0 0 1 18 0", "key": "svg-1" }], ["path", { "d": "M7 4.5l0 15", "key": "svg-2" }], ["path", { "d": "M17 4.5l0 15", "key": "svg-3" }]]);

export { IconZodiacGemini as default };
//# sourceMappingURL=IconZodiacGemini.mjs.map
