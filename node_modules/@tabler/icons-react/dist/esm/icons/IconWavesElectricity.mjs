/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconWavesElectricity = createReactComponent("outline", "waves-electricity", "IconWavesElectricity", [["path", { "d": "M3 12c.576 -.643 1.512 -1.017 2.5 -1c.988 -.017 1.924 .357 2.5 1c.576 .643 1.512 1.017 2.5 1c.988 .017 1.924 -.357 2.5 -1", "key": "svg-0" }], ["path", { "d": "M3 16c.576 -.643 1.512 -1.017 2.5 -1c.988 -.017 1.924 .357 2.5 1c.576 .643 1.512 1.017 2.5 1c.988 .017 1.924 -.357 2.5 -1", "key": "svg-1" }], ["path", { "d": "M3 8c.576 -.643 1.512 -1.017 2.5 -1c.988 -.017 1.924 .357 2.5 1c.576 .643 1.512 1.017 2.5 1c.988 .017 1.924 -.357 2.5 -1", "key": "svg-2" }], ["path", { "d": "M20 7l-3 5h4l-3 5", "key": "svg-3" }]]);

export { IconWavesElectricity as default };
//# sourceMappingURL=IconWavesElectricity.mjs.map
