/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconWashDrycleanOff = createReactComponent("outline", "wash-dryclean-off", "IconWashDrycleanOff", [["path", { "d": "M20.048 16.033a9 9 0 0 0 -12.094 -12.075m-2.321 1.682a9 9 0 0 0 12.733 12.723", "key": "svg-0" }], ["path", { "d": "M3 3l18 18", "key": "svg-1" }]]);

export { IconWashDrycleanOff as default };
//# sourceMappingURL=IconWashDrycleanOff.mjs.map
