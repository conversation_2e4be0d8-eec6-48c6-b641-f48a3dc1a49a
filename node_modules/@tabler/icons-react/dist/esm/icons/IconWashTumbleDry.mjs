/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconWashTumbleDry = createReactComponent("outline", "wash-tumble-dry", "IconWashTumbleDry", [["path", { "d": "M3 3m0 3a3 3 0 0 1 3 -3h12a3 3 0 0 1 3 3v12a3 3 0 0 1 -3 3h-12a3 3 0 0 1 -3 -3z", "key": "svg-0" }], ["path", { "d": "M12 12m-6 0a6 6 0 1 0 12 0a6 6 0 1 0 -12 0", "key": "svg-1" }]]);

export { IconWashTumbleDry as default };
//# sourceMappingURL=IconWashTumbleDry.mjs.map
