/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconWorldBolt = createReactComponent("outline", "world-bolt", "IconWorldBolt", [["path", { "d": "M20.985 12.52a9 9 0 1 0 -7.52 8.36", "key": "svg-0" }], ["path", { "d": "M3.6 9h16.8", "key": "svg-1" }], ["path", { "d": "M3.6 15h10.9", "key": "svg-2" }], ["path", { "d": "M11.5 3a17 17 0 0 0 0 18", "key": "svg-3" }], ["path", { "d": "M12.5 3c2.313 3.706 3.07 7.856 2.27 12", "key": "svg-4" }], ["path", { "d": "M19 16l-2 3h4l-2 3", "key": "svg-5" }]]);

export { IconWorldBolt as default };
//# sourceMappingURL=IconWorldBolt.mjs.map
