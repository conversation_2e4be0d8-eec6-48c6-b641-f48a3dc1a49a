/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconXboxB = createReactComponent("outline", "xbox-b", "IconXboxB", [["path", { "d": "M12 21a9 9 0 0 0 9 -9a9 9 0 0 0 -9 -9a9 9 0 0 0 -9 9a9 9 0 0 0 9 9z", "key": "svg-0" }], ["path", { "d": "M13 12a2 2 0 1 1 0 4h-3v-4", "key": "svg-1" }], ["path", { "d": "M13 12h-3", "key": "svg-2" }], ["path", { "d": "M13 12a2 2 0 1 0 0 -4h-3v4", "key": "svg-3" }]]);

export { IconXboxB as default };
//# sourceMappingURL=IconXboxB.mjs.map
