/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconZodiacCapricorn = createReactComponent("outline", "zodiac-capricorn", "IconZodiacCapricorn", [["path", { "d": "M4 4a3 3 0 0 1 3 3v9", "key": "svg-0" }], ["path", { "d": "M7 7a3 3 0 0 1 6 0v11a3 3 0 0 1 -3 3", "key": "svg-1" }], ["path", { "d": "M16 17m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0", "key": "svg-2" }]]);

export { IconZodiacCapricorn as default };
//# sourceMappingURL=IconZodiacCapricorn.mjs.map
