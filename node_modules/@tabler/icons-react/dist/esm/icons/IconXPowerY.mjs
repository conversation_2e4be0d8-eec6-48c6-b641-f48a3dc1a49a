/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconXPowerY = createReactComponent("outline", "x-power-y", "IconXPowerY", [["path", { "d": "M15 3l3 5.063", "key": "svg-0" }], ["path", { "d": "M5 12l6 6", "key": "svg-1" }], ["path", { "d": "M5 18l6 -6", "key": "svg-2" }], ["path", { "d": "M21 3l-4.8 9", "key": "svg-3" }]]);

export { IconXPowerY as default };
//# sourceMappingURL=IconXPowerY.mjs.map
