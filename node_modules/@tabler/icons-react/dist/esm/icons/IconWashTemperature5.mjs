/**
 * @license @tabler/icons-react v3.31.0 - MIT
 *
 * This source code is licensed under the MIT license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createReactComponent from '../createReactComponent.mjs';

var IconWashTemperature5 = createReactComponent("outline", "wash-temperature-5", "IconWashTemperature5", [["path", { "d": "M10 15h.01", "key": "svg-0" }], ["path", { "d": "M3 6l1.721 10.329a2 2 0 0 0 1.973 1.671h10.612a2 2 0 0 0 1.973 -1.671l1.721 -10.329", "key": "svg-1" }], ["path", { "d": "M14 15h.01", "key": "svg-2" }], ["path", { "d": "M15 12h.01", "key": "svg-3" }], ["path", { "d": "M12 12h.01", "key": "svg-4" }], ["path", { "d": "M9 12h.01", "key": "svg-5" }], ["path", { "d": "M3.486 8.965c.168 .02 .34 .033 .514 .035c.79 .009 1.539 -.178 2 -.5c.461 -.32 1.21 -.507 2 -.5c.79 -.007 1.539 .18 2 .5c.461 .322 1.21 .509 2 .5c.79 .009 1.539 -.178 2 -.5c.461 -.32 1.21 -.507 2 -.5c.79 -.007 1.539 .18 2 .5c.461 .322 1.21 .509 2 .5c.17 0 .339 -.014 .503 -.034", "key": "svg-6" }]]);

export { IconWashTemperature5 as default };
//# sourceMappingURL=IconWashTemperature5.mjs.map
