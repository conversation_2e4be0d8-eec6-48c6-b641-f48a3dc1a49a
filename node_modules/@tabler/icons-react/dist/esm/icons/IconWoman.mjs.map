{"version": 3, "file": "IconWoman.mjs", "sources": ["../../../src/icons/IconWoman.ts"], "sourcesContent": ["import createReactComponent from '../createReactComponent';\nexport default createReactComponent('outline', 'woman', 'IconWoman', [[\"path\",{\"d\":\"M10 16v5\",\"key\":\"svg-0\"}],[\"path\",{\"d\":\"M14 16v5\",\"key\":\"svg-1\"}],[\"path\",{\"d\":\"M8 16h8l-2 -7h-4z\",\"key\":\"svg-2\"}],[\"path\",{\"d\":\"M5 11c1.667 -1.333 3.333 -2 5 -2\",\"key\":\"svg-3\"}],[\"path\",{\"d\":\"M19 11c-1.667 -1.333 -3.333 -2 -5 -2\",\"key\":\"svg-4\"}],[\"path\",{\"d\":\"M12 4m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0\",\"key\":\"svg-5\"}]]);"], "names": [], "mappings": ";;;;;;;;;AACA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA,CAAC,CAAC,MAAA,CAAO,CAAA,CAAA,CAAC,GAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,OAAO,CAAC,CAAA,CAAE,EAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAC,CAAA,CAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,KAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAA,CAAA,CAAE,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAC,CAAA,CAAA,CAAA,CAAA,CAAI,mBAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAM,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAE,CAAA,CAAA,CAAC,MAAO,CAAA,CAAA,CAAA,CAAC,GAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAmC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,EAAE,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAO,CAAC,CAAA,CAAA,CAAA,CAAA,EAAI,CAAuC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,EAAC,CAAE,CAAA,CAAA,CAAC,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAC,CAAI,CAAA,CAAA,CAAA,CAAA,yCAAA,CAA0C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAC,CAAC,CAAA;;"}