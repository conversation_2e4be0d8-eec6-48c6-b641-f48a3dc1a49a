export interface ButtonClasses {
    /** Styles applied to the root element. */
    root: string;
    /** Styles applied to the root element if `variant="text"`. */
    text: string;
    /** Styles applied to the root element if `variant="text"` and `color="inherit"`.
     * @deprecated Combine the [.MuiButton-text](/material-ui/api/button/#button-classes-text) and [.MuiButton-colorInherit](/material-ui/api/button/#button-classes-colorInherit) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    textInherit: string;
    /** Styles applied to the root element if `variant="text"` and `color="primary"`.
     * @deprecated Combine the [.MuiButton-text](/material-ui/api/button/#button-classes-text) and [.MuiButton-colorPrimary](/material-ui/api/button/#button-classes-colorPrimary) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    textPrimary: string;
    /** Styles applied to the root element if `variant="text"` and `color="secondary"`.
     * @deprecated Combine the [.MuiButton-text](/material-ui/api/button/#button-classes-text) and [.MuiButton-colorSecondary](/material-ui/api/button/#button-classes-colorSecondary) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    textSecondary: string;
    /** Styles applied to the root element if `variant="text"` and `color="success"`.
     * @deprecated Combine the [.MuiButton-text](/material-ui/api/button/#button-classes-text) and [.MuiButton-colorSuccess](/material-ui/api/button/#button-classes-colorSuccess) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    textSuccess: string;
    /** Styles applied to the root element if `variant="text"` and `color="error"`.
     * @deprecated Combine the [.MuiButton-text](/material-ui/api/button/#button-classes-text) and [.MuiButton-colorError](/material-ui/api/button/#button-classes-colorError) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    textError: string;
    /** Styles applied to the root element if `variant="text"` and `color="info"`.
     * @deprecated Combine the [.MuiButton-text](/material-ui/api/button/#button-classes-text) and [.MuiButton-colorInfo](/material-ui/api/button/#button-classes-colorInfo) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    textInfo: string;
    /** Styles applied to the root element if `variant="text"` and `color="warning"`.
     * @deprecated Combine the [.MuiButton-text](/material-ui/api/button/#button-classes-text) and [.MuiButton-colorWarning](/material-ui/api/button/#button-classes-colorWarning) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    textWarning: string;
    /** Styles applied to the root element if `variant="outlined"`. */
    outlined: string;
    /** Styles applied to the root element if `variant="outlined"` and `color="inherit"`.
     * @deprecated Combine the [.MuiButton-outlined](/material-ui/api/button/#button-classes-outlined) and [.MuiButton-colorInherit](/material-ui/api/button/#button-classes-colorInherit) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    outlinedInherit: string;
    /** Styles applied to the root element if `variant="outlined"` and `color="primary"`.
     * @deprecated Combine the [.MuiButton-outlined](/material-ui/api/button/#button-classes-outlined) and [.MuiButton-colorPrimary](/material-ui/api/button/#button-classes-colorPrimary) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    outlinedPrimary: string;
    /** Styles applied to the root element if `variant="outlined"` and `color="secondary"`.
     * @deprecated Combine the [.MuiButton-outlined](/material-ui/api/button/#button-classes-outlined) and [.MuiButton-colorSecondary](/material-ui/api/button/#button-classes-colorSecondary) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    outlinedSecondary: string;
    /** Styles applied to the root element if `variant="outlined"` and `color="success"`.
     * @deprecated Combine the [.MuiButton-outlined](/material-ui/api/button/#button-classes-outlined) and [.MuiButton-colorSuccess](/material-ui/api/button/#button-classes-colorSuccess) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    outlinedSuccess: string;
    /** Styles applied to the root element if `variant="outlined"` and `color="error"`.
     * @deprecated Combine the [.MuiButton-outlined](/material-ui/api/button/#button-classes-outlined) and [.MuiButton-colorError](/material-ui/api/button/#button-classes-colorError) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    outlinedError: string;
    /** Styles applied to the root element if `variant="outlined"` and `color="info"`.
     * @deprecated Combine the [.MuiButton-outlined](/material-ui/api/button/#button-classes-outlined) and [.MuiButton-colorInfo](/material-ui/api/button/#button-classes-colorInfo) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    outlinedInfo: string;
    /** Styles applied to the root element if `variant="outlined"` and `color="warning"`.
     * @deprecated Combine the [.MuiButton-outlined](/material-ui/api/button/#button-classes-outlined) and [.MuiButton-colorWarning](/material-ui/api/button/#button-classes-colorWarning) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    outlinedWarning: string;
    /** Styles applied to the root element if `variant="contained"`. */
    contained: string;
    /** Styles applied to the root element if `variant="contained"` and `color="inherit"`.
     * @deprecated Combine the [.MuiButton-contained](/material-ui/api/button/#button-classes-contained) and [.MuiButton-colorInherit](/material-ui/api/button/#button-classes-colorInherit) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    containedInherit: string;
    /** Styles applied to the root element if `variant="contained"` and `color="primary"`.
     * @deprecated Combine the [.MuiButton-contained](/material-ui/api/button/#button-classes-contained) and [.MuiButton-colorPrimary](/material-ui/api/button/#button-classes-colorPrimary) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    containedPrimary: string;
    /** Styles applied to the root element if `variant="contained"` and `color="secondary"`.
     * @deprecated Combine the [.MuiButton-contained](/material-ui/api/button/#button-classes-contained) and [.MuiButton-colorSecondary](/material-ui/api/button/#button-classes-colorSecondary) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    containedSecondary: string;
    /** Styles applied to the root element if `variant="contained"` and `color="success"`.
     * @deprecated Combine the [.MuiButton-contained](/material-ui/api/button/#button-classes-contained) and [.MuiButton-colorSuccess](/material-ui/api/button/#button-classes-colorSuccess) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    containedSuccess: string;
    /** Styles applied to the root element if `variant="contained"` and `color="info"`.
     * @deprecated Combine the [.MuiButton-contained](/material-ui/api/button/#button-classes-contained) and [.MuiButton-colorInfo](/material-ui/api/button/#button-classes-colorInfo) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    containedInfo: string;
    /** Styles applied to the root element if `variant="contained"` and `color="error"`.
     * @deprecated Combine the [.MuiButton-contained](/material-ui/api/button/#button-classes-contained) and [.MuiButton-colorError](/material-ui/api/button/#button-classes-colorError) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    containedError: string;
    /** Styles applied to the root element if `variant="contained"` and `color="warning"`.
     * @deprecated Combine the [.MuiButton-contained](/material-ui/api/button/#button-classes-contained) and [.MuiButton-colorWarning](/material-ui/api/button/#button-classes-colorWarning) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    containedWarning: string;
    /** Styles applied to the root element if `disableElevation={true}`. */
    disableElevation: string;
    /** State class applied to the ButtonBase root element if the button is keyboard focused. */
    focusVisible: string;
    /** State class applied to the root element if `disabled={true}`. */
    disabled: string;
    /** Styles applied to the root element if `color="inherit"`. */
    colorInherit: string;
    /** Styles applied to the root element if `size="small"` and `variant="text"`.
     * @deprecated Combine the [.MuiButton-sizeSmall](/material-ui/api/button/#button-classes-sizeSmall) and [.MuiButton-text](/material-ui/api/button/#button-classes-text) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    textSizeSmall: string;
    /** Styles applied to the root element if `size="medium"` and `variant="text"`.
     * @deprecated Combine the [.MuiButton-sizeMedium](/material-ui/api/button/#button-classes-sizeMedium) and [.MuiButton-text](/material-ui/api/button/#button-classes-text) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    textSizeMedium: string;
    /** Styles applied to the root element if `size="large"` and `variant="text"`.
     * @deprecated Combine the [.MuiButton-sizeLarge](/material-ui/api/button/#button-classes-sizeLarge) and [.MuiButton-text](/material-ui/api/button/#button-classes-text) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    textSizeLarge: string;
    /** Styles applied to the root element if `size="small"` and `variant="outlined"`.
     * @deprecated Combine the [.MuiButton-sizeSmall](/material-ui/api/button/#button-classes-sizeSmall) and [.MuiButton-outlined](/material-ui/api/button/#button-classes-outlined) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    outlinedSizeSmall: string;
    /** Styles applied to the root element if `size="medium"` and `variant="outlined"`.
     * @deprecated Combine the [.MuiButton-sizeMedium](/material-ui/api/button/#button-classes-sizeMedium) and [.MuiButton-outlined](/material-ui/api/button/#button-classes-outlined) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    outlinedSizeMedium: string;
    /** Styles applied to the root element if `size="large"` and `variant="outlined"`.
     * @deprecated Combine the [.MuiButton-sizeLarge](/material-ui/api/button/#button-classes-sizeLarge) and [.MuiButton-outlined](/material-ui/api/button/#button-classes-outlined) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    outlinedSizeLarge: string;
    /** Styles applied to the root element if `size="small"` and `variant="contained"`.
     * @deprecated Combine the [.MuiButton-sizeSmall](/material-ui/api/button/#button-classes-sizeSmall) and [.MuiButton-contained](/material-ui/api/button/#button-classes-contained) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    containedSizeSmall: string;
    /** Styles applied to the root element if `size="medium"` and `variant="contained"`.
     * @deprecated Combine the [.MuiButton-sizeMedium](/material-ui/api/button/#button-classes-sizeMedium) and [.MuiButton-contained](/material-ui/api/button/#button-classes-contained) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    containedSizeMedium: string;
    /** Styles applied to the root element if `size="large"` and `variant="contained"`.
     * @deprecated Combine the [.MuiButton-sizeLarge](/material-ui/api/button/#button-classes-sizeLarge) and [.MuiButton-contained](/material-ui/api/button/#button-classes-contained) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    containedSizeLarge: string;
    /** Styles applied to the root element if `size="small"`. */
    sizeSmall: string;
    /** Styles applied to the root element if `size="medium"`. */
    sizeMedium: string;
    /** Styles applied to the root element if `size="large"`. */
    sizeLarge: string;
    /** Styles applied to the root element if `fullWidth={true}`. */
    fullWidth: string;
    /** Styles applied to the icon element if supplied */
    icon: string;
    /** Styles applied to the startIcon element if supplied. */
    startIcon: string;
    /** Styles applied to the endIcon element if supplied. */
    endIcon: string;
    /** Styles applied to the icon element if supplied and `size="small"`.
     * @deprecated Combine the [.MuiButton-icon](/material-ui/api/button/#button-classes-icon) and [.MuiButtonSizeSmall](/material-ui/api/button/#button-classes-sizeSmall) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    iconSizeSmall: string;
    /** Styles applied to the icon element if supplied and `size="medium"`.
     * @deprecated Combine the [.MuiButton-icon](/material-ui/api/button/#button-classes-icon) and [.MuiButtonSizeMedium](/material-ui/api/button/#button-classes-sizeMedium) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    iconSizeMedium: string;
    /** Styles applied to the icon element if supplied and `size="large"`.
     * @deprecated Combine the [.MuiButton-icon](/material-ui/api/button/#button-classes-icon) and [.MuiButtonSizeLarge](/material-ui/api/button/#button-classes-sizeLarge) classes instead. [How to migrate](/material-ui/migration/migrating-from-deprecated-apis/)
     */
    iconSizeLarge: string;
    /** Styles applied to the root element if `color="primary"`. */
    colorPrimary: string;
    /** Styles applied to the root element if `color="secondary"`. */
    colorSecondary: string;
    /** Styles applied to the root element if `color="success"`. */
    colorSuccess: string;
    /** Styles applied to the root element if `color="error"`. */
    colorError: string;
    /** Styles applied to the root element if `color="info"`. */
    colorInfo: string;
    /** Styles applied to the root element if `color="warning"`. */
    colorWarning: string;
}
export type ButtonClassKey = keyof ButtonClasses;
export declare function getButtonUtilityClass(slot: string): string;
declare const buttonClasses: ButtonClasses;
export default buttonClasses;
