{"name": "teacher-pro", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/cache": "^11.14.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.17.1", "@mui/material": "^5.14.18", "@tabler/icons-react": "^3.31.0", "axios": "^1.6.2", "framer-motion": "^10.18.0", "html2canvas": "^1.4.1", "i18next": "^23.7.6", "jspdf": "^2.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-i18next": "^13.5.0", "react-router-dom": "^6.20.0", "styled-components": "^6.1.1", "stylis-plugin-rtl": "^2.1.1"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.2.0", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^5.0.0", "vite-plugin-pwa": "^0.17.0"}}