<?xml version="1.0" encoding="UTF-8"?>
<svg width="800px" height="500px" viewBox="0 0 800 500" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#2563EB" offset="0%"></stop>
            <stop stop-color="#3B82F6" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#F3F4F6" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-3">
            <stop stop-color="#60A5FA" offset="0%"></stop>
            <stop stop-color="#3B82F6" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Hero-Illustration" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect id="Background" fill="#F8FAFC" x="0" y="0" width="800" height="500"></rect>
        
        <!-- Decorative Elements -->
        <circle id="Circle-1" fill="#DBEAFE" cx="100" cy="100" r="50"></circle>
        <circle id="Circle-2" fill="#BFDBFE" cx="700" cy="400" r="70"></circle>
        <circle id="Circle-3" fill="#EFF6FF" cx="200" cy="450" r="30"></circle>
        <circle id="Circle-4" fill="#EFF6FF" cx="650" cy="150" r="40"></circle>
        
        <!-- Main Elements -->
        <g id="Document-Group" transform="translate(250, 50)">
            <!-- Document Background -->
            <rect id="Document" fill="url(#linearGradient-2)" x="0" y="0" width="300" height="400" rx="10"></rect>
            
            <!-- Document Header -->
            <rect id="Header" fill="url(#linearGradient-1)" x="0" y="0" width="300" height="60" rx="10 10 0 0"></rect>
            <circle id="Logo" fill="#FFFFFF" cx="30" cy="30" r="15"></circle>
            <rect id="Title-1" fill="#FFFFFF" x="60" y="20" width="120" height="10" rx="5"></rect>
            <rect id="Title-2" fill="#FFFFFF" opacity="0.7" x="60" y="40" width="80" height="6" rx="3"></rect>
            
            <!-- Document Content -->
            <rect id="Section-1" fill="#E2E8F0" x="20" y="80" width="260" height="30" rx="5"></rect>
            <rect id="Section-2" fill="#E2E8F0" x="20" y="130" width="260" height="60" rx="5"></rect>
            <rect id="Section-3" fill="#E2E8F0" x="20" y="210" width="260" height="40" rx="5"></rect>
            <rect id="Section-4" fill="#E2E8F0" x="20" y="270" width="260" height="80" rx="5"></rect>
            
            <!-- Document Footer -->
            <rect id="Footer" fill="#F1F5F9" x="20" y="370" width="260" height="20" rx="5"></rect>
        </g>
        
        <!-- AI Elements -->
        <g id="AI-Elements" transform="translate(150, 150)">
            <circle id="AI-Circle" fill="url(#linearGradient-3)" cx="50" cy="50" r="50"></circle>
            <path d="M50,25 L65,50 L50,75 L35,50 Z" id="AI-Diamond" fill="#FFFFFF"></path>
            <circle id="AI-Dot-1" fill="#FFFFFF" cx="30" cy="40" r="5"></circle>
            <circle id="AI-Dot-2" fill="#FFFFFF" cx="70" cy="40" r="5"></circle>
            
            <!-- Connection Lines -->
            <path d="M100,50 C150,50 150,200 200,200" id="Connection-1" stroke="#3B82F6" stroke-width="3" stroke-dasharray="5,5"></path>
            <path d="M100,50 C150,50 150,100 200,100" id="Connection-2" stroke="#3B82F6" stroke-width="3" stroke-dasharray="5,5"></path>
            <path d="M100,50 C150,50 150,300 200,300" id="Connection-3" stroke="#3B82F6" stroke-width="3" stroke-dasharray="5,5"></path>
        </g>
        
        <!-- Arabic Text Elements (Represented as Shapes) -->
        <g id="Arabic-Text" transform="translate(500, 200)">
            <rect id="Text-Line-1" fill="#1E40AF" x="0" y="0" width="150" height="10" rx="5"></rect>
            <rect id="Text-Line-2" fill="#1E40AF" opacity="0.8" x="0" y="20" width="200" height="10" rx="5"></rect>
            <rect id="Text-Line-3" fill="#1E40AF" opacity="0.6" x="0" y="40" width="180" height="10" rx="5"></rect>
            <rect id="Text-Line-4" fill="#1E40AF" opacity="0.4" x="0" y="60" width="220" height="10" rx="5"></rect>
        </g>
    </g>
</svg>
