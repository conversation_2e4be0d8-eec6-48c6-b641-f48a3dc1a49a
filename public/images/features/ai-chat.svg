<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="200px" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#3B82F6" offset="0%"></stop>
            <stop stop-color="#2563EB" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="AI-Chat-Feature" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <circle id="Background" fill="#EFF6FF" cx="100" cy="100" r="100"></circle>
        
        <!-- Chat Bubbles -->
        <g id="Chat-Bubbles" transform="translate(30, 40)">
            <!-- AI Bubble -->
            <rect id="AI-Bubble" fill="url(#linearGradient-1)" x="0" y="0" width="100" height="50" rx="15"></rect>
            <circle id="AI-Avatar" fill="#FFFFFF" cx="15" cy="15" r="10"></circle>
            <rect id="AI-Text-1" fill="#FFFFFF" x="35" y="10" width="50" height="6" rx="3"></rect>
            <rect id="AI-Text-2" fill="#FFFFFF" opacity="0.7" x="35" y="22" width="40" height="6" rx="3"></rect>
            <rect id="AI-Text-3" fill="#FFFFFF" opacity="0.5" x="35" y="34" width="30" height="6" rx="3"></rect>
            
            <!-- User Bubble -->
            <rect id="User-Bubble" fill="#FFFFFF" stroke="#E2E8F0" stroke-width="2" x="40" y="70" width="100" height="50" rx="15"></rect>
            <circle id="User-Avatar" fill="#3B82F6" cx="125" cy="85" r="10"></circle>
            <rect id="User-Text-1" fill="#1E293B" x="55" y="80" width="50" height="6" rx="3"></rect>
            <rect id="User-Text-2" fill="#1E293B" opacity="0.7" x="55" y="92" width="40" height="6" rx="3"></rect>
            <rect id="User-Text-3" fill="#1E293B" opacity="0.5" x="55" y="104" width="30" height="6" rx="3"></rect>
        </g>
        
        <!-- Decorative Elements -->
        <circle id="Dot-1" fill="#93C5FD" cx="40" cy="160" r="5"></circle>
        <circle id="Dot-2" fill="#93C5FD" cx="55" cy="160" r="5"></circle>
        <circle id="Dot-3" fill="#93C5FD" cx="70" cy="160" r="5"></circle>
    </g>
</svg>
