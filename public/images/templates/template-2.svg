<?xml version="1.0" encoding="UTF-8"?>
<svg width="300px" height="200px" viewBox="0 0 300 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#0EA5E9" offset="0%"></stop>
            <stop stop-color="#0284C7" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Teacher-Portfolio-Template" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect id="Background" fill="url(#linearGradient-1)" x="0" y="0" width="300" height="200" rx="10"></rect>
        
        <!-- Template Content -->
        <g id="Content" transform="translate(30, 30)">
            <rect id="Paper" fill="#FFFFFF" x="0" y="0" width="240" height="140" rx="5"></rect>
            
            <!-- Profile Section -->
            <circle id="Profile-Photo" fill="#F1F5F9" stroke="#0EA5E9" stroke-width="2" cx="40" cy="40" r="25"></circle>
            <rect id="Name" fill="#1E293B" x="80" y="30" width="120" height="10" rx="2"></rect>
            <rect id="Title" fill="#64748B" x="80" y="50" width="80" height="5" rx="2"></rect>
            
            <!-- Content Sections -->
            <rect id="Section-1" fill="#F0F9FF" x="10" y="80" width="220" height="15" rx="3"></rect>
            <rect id="Section-2" fill="#F0F9FF" x="10" y="100" width="220" height="15" rx="3"></rect>
            <rect id="Section-3" fill="#F0F9FF" x="10" y="120" width="220" height="15" rx="3"></rect>
        </g>
        
        <!-- Decorative Elements -->
        <circle id="Circle-1" fill="#FFFFFF" opacity="0.1" cx="250" cy="40" r="20"></circle>
        <circle id="Circle-2" fill="#FFFFFF" opacity="0.1" cx="270" cy="150" r="15"></circle>
        <circle id="Circle-3" fill="#FFFFFF" opacity="0.1" cx="50" cy="180" r="10"></circle>
        
        <!-- Arabic Label -->
        <text id="Template-Name" font-family="Arial" font-size="16" font-weight="bold" fill="#FFFFFF" text-anchor="middle">
            <tspan x="150" y="180">ملف إنجاز معلم</tspan>
        </text>
    </g>
</svg>
