<?xml version="1.0" encoding="UTF-8"?>
<svg width="300px" height="200px" viewBox="0 0 300 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#10B981" offset="0%"></stop>
            <stop stop-color="#059669" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Lesson-Plan-Template" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect id="Background" fill="url(#linearGradient-1)" x="0" y="0" width="300" height="200" rx="10"></rect>
        
        <!-- Template Content -->
        <g id="Content" transform="translate(30, 30)">
            <rect id="Paper" fill="#FFFFFF" x="0" y="0" width="240" height="140" rx="5"></rect>
            
            <!-- Header -->
            <rect id="Header" fill="#ECFDF5" x="10" y="10" width="220" height="25" rx="3"></rect>
            <rect id="Title" fill="#1E293B" x="20" y="20" width="100" height="10" rx="2"></rect>
            <rect id="Date" fill="#64748B" x="150" y="20" width="70" height="5" rx="2"></rect>
            
            <!-- Content Sections -->
            <rect id="Section-1-Header" fill="#10B981" x="10" y="45" width="220" height="15" rx="3"></rect>
            <rect id="Section-1-Text" fill="#F1F5F9" x="10" y="65" width="220" height="10" rx="2"></rect>
            
            <rect id="Section-2-Header" fill="#10B981" x="10" y="85" width="220" height="15" rx="3"></rect>
            <rect id="Section-2-Text" fill="#F1F5F9" x="10" y="105" width="220" height="10" rx="2"></rect>
            <rect id="Section-2-Text-2" fill="#F1F5F9" x="10" y="120" width="220" height="10" rx="2"></rect>
        </g>
        
        <!-- Decorative Elements -->
        <circle id="Circle-1" fill="#FFFFFF" opacity="0.1" cx="250" cy="40" r="20"></circle>
        <circle id="Circle-2" fill="#FFFFFF" opacity="0.1" cx="270" cy="150" r="15"></circle>
        <circle id="Circle-3" fill="#FFFFFF" opacity="0.1" cx="50" cy="180" r="10"></circle>
        
        <!-- Arabic Label -->
        <text id="Template-Name" font-family="Arial" font-size="16" font-weight="bold" fill="#FFFFFF" text-anchor="middle">
            <tspan x="150" y="180">خطة درس</tspan>
        </text>
    </g>
</svg>
