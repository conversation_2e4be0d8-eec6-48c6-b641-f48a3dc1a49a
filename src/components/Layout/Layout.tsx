import { useState } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Header from './Header';
import Sidebar from './Sidebar';

const Main = styled('main', { shouldForwardProp: (prop) => prop !== 'open' })<{
  open?: boolean;
}>(({ theme, open }) => ({
  flexGrow: 1,
  padding: theme.spacing(3),
  transition: theme.transitions.create(['margin', 'padding'], {
    easing: theme.transitions.easing.easeInOut,
    duration: theme.transitions.duration.standard,
  }),
  marginRight: `-260px`,
  overflowY: 'auto',
  height: '100vh',
  backgroundColor: theme.palette.background.default,
  borderRadius: theme.shape.borderRadius,
  boxShadow: 'none',
  ...(open && {
    transition: theme.transitions.create(['margin', 'padding'], {
      easing: theme.transitions.easing.easeInOut,
      duration: theme.transitions.duration.standard,
    }),
    marginRight: 0,
  }),
}));

const Layout = () => {
  const [open, setOpen] = useState(true);
  const location = useLocation();

  // تحديد ما إذا كان يجب عرض القائمة الجانبية أم لا
  const shouldShowSidebar = () => {
    // لا نعرض القائمة الجانبية في الصفحة الرئيسية
    return location.pathname !== '/';
  };

  const handleDrawerToggle = () => {
    setOpen(!open);
  };

  return (
    <Box sx={{ display: 'flex', height: '100vh', overflow: 'hidden' }}>
      {/* تم إزالة مكون Header */}
      {shouldShowSidebar() && <Sidebar open={open} onDrawerToggle={handleDrawerToggle} />}
      <Main open={shouldShowSidebar() ? open : false}>
        <Box
          component="div"
          sx={{
            height: '100%',
            overflowY: 'auto',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'flex-start',
            alignItems: 'stretch',
          }}
        >
          <Container
            maxWidth="lg"
            sx={{
              py: 4,
              px: { xs: 2, sm: 3, md: 4 },
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: '100px',
                background: 'linear-gradient(180deg, rgba(255,255,255,0.8) 0%, rgba(255,255,255,0) 100%)',
                opacity: 0.5,
                pointerEvents: 'none',
                zIndex: -1,
              }
            }}
          >
            <Outlet />
          </Container>
        </Box>
      </Main>
    </Box>
  );
};

export default Layout;
