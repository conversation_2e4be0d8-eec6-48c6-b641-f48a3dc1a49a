import { useTranslation } from 'react-i18next';
import { Link, useLocation } from 'react-router-dom';
import { styled, alpha } from '@mui/material/styles';
import Box from '@mui/material/Box';
import Drawer from '@mui/material/Drawer';
import List from '@mui/material/List';
import Divider from '@mui/material/Divider';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import Tooltip from '@mui/material/Tooltip';
import Paper from '@mui/material/Paper';
import Button from '@mui/material/Button';

// أيقونات عصرية
import HomeRoundedIcon from '@mui/icons-material/HomeRounded';
import ChatRoundedIcon from '@mui/icons-material/ChatRounded';
import AutoAwesomeRoundedIcon from '@mui/icons-material/AutoAwesomeRounded';
import DescriptionRoundedIcon from '@mui/icons-material/DescriptionRounded';
import PersonRoundedIcon from '@mui/icons-material/PersonRounded';
import AddRoundedIcon from '@mui/icons-material/AddRounded';
import StarRoundedIcon from '@mui/icons-material/StarRounded';
import HistoryRoundedIcon from '@mui/icons-material/HistoryRounded';
import SettingsRoundedIcon from '@mui/icons-material/SettingsRounded';
import HelpOutlineRoundedIcon from '@mui/icons-material/HelpOutlineRounded';

const drawerWidth = 260;

const StyledDrawer = styled(Drawer)(({ theme }) => ({
  width: drawerWidth,
  flexShrink: 0,
  '& .MuiDrawer-paper': {
    width: drawerWidth,
    boxSizing: 'border-box',
    top: '0',
    height: '100%',
    borderLeft: 'none',
    backgroundColor: theme.palette.background.default,
    padding: theme.spacing(2),
    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
    transition: 'box-shadow 0.3s ease-in-out',
    '&:hover': {
      boxShadow: '0 6px 25px rgba(0, 0, 0, 0.12)',
    },
  },
}));

const StyledListItemButton = styled(ListItemButton)(({ theme }) => ({
  borderRadius: 12,
  marginBottom: 4,
  padding: '10px 16px',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  boxShadow: 'none',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: 'transparent',
    borderRadius: 12,
    opacity: 0,
    transition: 'opacity 0.3s ease',
  },
  '&.Mui-selected': {
    backgroundColor: alpha(theme.palette.primary.main, 0.1),
    boxShadow: `0 4px 10px ${alpha(theme.palette.primary.main, 0.15)}`,
    transform: 'translateY(-1px)',
    '&:hover': {
      backgroundColor: alpha(theme.palette.primary.main, 0.15),
    },
    '& .MuiListItemIcon-root': {
      color: theme.palette.primary.main,
      transform: 'scale(1.1)',
      transition: 'transform 0.3s ease',
    },
    '& .MuiListItemText-primary': {
      color: theme.palette.primary.main,
      fontWeight: 600,
    },
    '&::before': {
      opacity: 0.05,
      background: `linear-gradient(135deg, ${theme.palette.primary.light} 0%, ${theme.palette.primary.main} 100%)`,
    },
  },
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.05),
    transform: 'translateY(-1px)',
    boxShadow: `0 4px 8px ${alpha(theme.palette.primary.main, 0.1)}`,
    '& .MuiListItemIcon-root': {
      transform: 'scale(1.05)',
    },
  },
}));

interface SidebarProps {
  open: boolean;
  onDrawerToggle: () => void;
}

const Sidebar = ({ open, onDrawerToggle }: SidebarProps) => {
  const { t } = useTranslation();
  const location = useLocation();

  const mainMenuItems = [
    { text: t('nav.home'), icon: <HomeRoundedIcon />, path: '/' },
    { text: t('nav.chat'), icon: <ChatRoundedIcon />, path: '/chat' },
    { text: t('nav.templates'), icon: <DescriptionRoundedIcon />, path: '/templates' },
  ];

  const secondaryMenuItems = [
    { text: t('nav.profile'), icon: <PersonRoundedIcon />, path: '/profile' },
    { text: 'الإعدادات', icon: <SettingsRoundedIcon />, path: '/settings' },
    { text: 'المساعدة', icon: <HelpOutlineRoundedIcon />, path: '/help' },
  ];

  const drawer = (
    <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
      {/* زر إنشاء مستند جديد */}
      <Box sx={{ mb: 3 }}>
        <Button
          variant="contained"
          fullWidth
          startIcon={<AddRoundedIcon />}
          size="large"
          component={Link}
          to="/chat"
          sx={{
            py: 1.5,
            borderRadius: 3,
            background: 'linear-gradient(135deg, #2563eb 0%, #3b82f6 100%)',
            boxShadow: '0 8px 16px rgba(37, 99, 235, 0.25)',
            transition: 'all 0.3s ease',
            position: 'relative',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)',
              opacity: 0,
              transition: 'opacity 0.3s ease',
            },
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: '0 12px 20px rgba(37, 99, 235, 0.3)',
              '&::before': {
                opacity: 1,
              },
            },
            '&:active': {
              transform: 'translateY(0)',
              boxShadow: '0 5px 10px rgba(37, 99, 235, 0.2)',
            },
          }}
        >
          إنشاء مستند جديد
        </Button>
      </Box>

      {/* القائمة الرئيسية */}
      <Typography
        variant="subtitle2"
        color="text.secondary"
        sx={{ px: 2, mb: 1, fontWeight: 500 }}
      >
        القائمة الرئيسية
      </Typography>
      <List sx={{ p: 0 }}>
        {mainMenuItems.map((item) => (
          <ListItem key={item.text} disablePadding sx={{ mb: 0.5 }}>
            <StyledListItemButton
              component={Link}
              to={item.path}
              selected={location.pathname === item.path}
            >
              <ListItemIcon sx={{ minWidth: 40 }}>{item.icon}</ListItemIcon>
              <ListItemText
                primary={item.text}
                primaryTypographyProps={{ fontWeight: 500 }}
              />
            </StyledListItemButton>
          </ListItem>
        ))}
      </List>

      <Divider sx={{ my: 3 }} />

      {/* المستندات الأخيرة */}
      <Typography
        variant="subtitle2"
        color="text.secondary"
        sx={{ px: 2, mb: 1, fontWeight: 500 }}
      >
        المستندات الأخيرة
      </Typography>
      <List sx={{ p: 0 }}>
        <ListItem disablePadding sx={{ mb: 0.5 }}>
          <StyledListItemButton>
            <ListItemIcon sx={{ minWidth: 40 }}>
              <HistoryRoundedIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText
              primary="تقرير نشاط يوم المعلم"
              primaryTypographyProps={{
                fontWeight: 500,
                fontSize: '0.9rem',
                noWrap: true,
              }}
            />
          </StyledListItemButton>
        </ListItem>
        <ListItem disablePadding sx={{ mb: 0.5 }}>
          <StyledListItemButton>
            <ListItemIcon sx={{ minWidth: 40 }}>
              <HistoryRoundedIcon fontSize="small" />
            </ListItemIcon>
            <ListItemText
              primary="خطة درس مهارات القراءة"
              primaryTypographyProps={{
                fontWeight: 500,
                fontSize: '0.9rem',
                noWrap: true,
              }}
            />
          </StyledListItemButton>
        </ListItem>
      </List>

      <Divider sx={{ my: 3 }} />

      {/* القوالب المفضلة */}
      <Typography
        variant="subtitle2"
        color="text.secondary"
        sx={{ px: 2, mb: 1, fontWeight: 500 }}
      >
        القوالب المفضلة
      </Typography>
      <List sx={{ p: 0 }}>
        <ListItem disablePadding sx={{ mb: 0.5 }}>
          <StyledListItemButton>
            <ListItemIcon sx={{ minWidth: 40 }}>
              <StarRoundedIcon fontSize="small" color="warning" />
            </ListItemIcon>
            <ListItemText
              primary="تقرير نشاط طلابي"
              primaryTypographyProps={{
                fontWeight: 500,
                fontSize: '0.9rem',
                noWrap: true,
              }}
            />
          </StyledListItemButton>
        </ListItem>
        <ListItem disablePadding sx={{ mb: 0.5 }}>
          <StyledListItemButton>
            <ListItemIcon sx={{ minWidth: 40 }}>
              <StarRoundedIcon fontSize="small" color="warning" />
            </ListItemIcon>
            <ListItemText
              primary="ملف إنجاز معلم"
              primaryTypographyProps={{
                fontWeight: 500,
                fontSize: '0.9rem',
                noWrap: true,
              }}
            />
          </StyledListItemButton>
        </ListItem>
      </List>

      <Box sx={{ flexGrow: 1 }} />

      {/* الإعدادات والمساعدة */}
      <List sx={{ p: 0 }}>
        {secondaryMenuItems.map((item) => (
          <ListItem key={item.text} disablePadding sx={{ mb: 0.5 }}>
            <StyledListItemButton
              component={Link}
              to={item.path}
              selected={location.pathname === item.path}
            >
              <ListItemIcon sx={{ minWidth: 40 }}>{item.icon}</ListItemIcon>
              <ListItemText
                primary={item.text}
                primaryTypographyProps={{ fontWeight: 500 }}
              />
            </StyledListItemButton>
          </ListItem>
        ))}
      </List>

      {/* بطاقة الترقية */}
      <Paper
        elevation={0}
        sx={{
          mt: 3,
          p: 2,
          borderRadius: 3,
          background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
          color: 'white',
          position: 'relative',
          overflow: 'hidden',
          boxShadow: '0 10px 20px rgba(37, 99, 235, 0.2)',
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'translateY(-3px)',
            boxShadow: '0 15px 30px rgba(37, 99, 235, 0.25)',
          },
        }}
      >
        <Box
          sx={{
            position: 'absolute',
            top: -15,
            right: -15,
            width: 80,
            height: 80,
            borderRadius: '50%',
            bgcolor: 'rgba(255,255,255,0.1)',
            animation: 'pulse 3s infinite ease-in-out',
            '@keyframes pulse': {
              '0%': { transform: 'scale(1)', opacity: 0.5 },
              '50%': { transform: 'scale(1.05)', opacity: 0.8 },
              '100%': { transform: 'scale(1)', opacity: 0.5 },
            },
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            bottom: -20,
            left: -20,
            width: 100,
            height: 100,
            borderRadius: '50%',
            bgcolor: 'rgba(255,255,255,0.05)',
            animation: 'float 4s infinite ease-in-out',
            '@keyframes float': {
              '0%': { transform: 'translateY(0)' },
              '50%': { transform: 'translateY(-5px)' },
              '100%': { transform: 'translateY(0)' },
            },
          }}
        />
        <AutoAwesomeRoundedIcon
          sx={{
            mb: 1,
            animation: 'glow 2s infinite alternate',
            '@keyframes glow': {
              '0%': { opacity: 0.7, transform: 'scale(1)' },
              '100%': { opacity: 1, transform: 'scale(1.1)' },
            },
          }}
        />
        <Typography variant="subtitle2" fontWeight={600} sx={{ mb: 0.5 }}>
          ترقية إلى النسخة المميزة
        </Typography>
        <Typography variant="caption" sx={{ display: 'block', mb: 1.5, opacity: 0.9 }}>
          احصل على ميزات حصرية ومستندات غير محدودة
        </Typography>
        <Button
          variant="contained"
          size="small"
          fullWidth
          sx={{
            bgcolor: 'white',
            color: 'primary.main',
            fontWeight: 600,
            boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
            transition: 'all 0.3s ease',
            '&:hover': {
              bgcolor: 'rgba(255,255,255,0.95)',
              transform: 'translateY(-1px)',
              boxShadow: '0 6px 12px rgba(0, 0, 0, 0.15)',
            },
            '&:active': {
              transform: 'translateY(0)',
            },
          }}
        >
          ترقية الآن
        </Button>
      </Paper>
    </Box>
  );

  return (
    <Box component="nav" sx={{ width: { md: drawerWidth }, flexShrink: { md: 0 } }}>
      {/* Mobile drawer */}
      <Drawer
        variant="temporary"
        open={open}
        onClose={onDrawerToggle}
        ModalProps={{
          keepMounted: true, // Better open performance on mobile.
        }}
        sx={{
          display: { xs: 'block', md: 'none' },
          '& .MuiDrawer-paper': {
            width: drawerWidth,
            boxSizing: 'border-box',
            padding: 2,
          },
        }}
      >
        {drawer}
      </Drawer>

      {/* Desktop drawer */}
      <StyledDrawer
        variant="persistent"
        sx={{
          display: { xs: 'none', md: 'block' },
        }}
        open={open}
      >
        {drawer}
      </StyledDrawer>
    </Box>
  );
};

export default Sidebar;
