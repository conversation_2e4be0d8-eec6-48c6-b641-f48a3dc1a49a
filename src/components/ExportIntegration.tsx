import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogT<PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Paper,
  Divider,
  IconButton,
  Tooltip,
  FormControlLabel,
  Checkbox
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import SchoolIcon from '@mui/icons-material/School';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { motion } from 'framer-motion';
import { integrationService, DocumentData } from '../services/integration';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`integration-tabpanel-${index}`}
      aria-labelledby={`integration-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface ExportIntegrationProps {
  open: boolean;
  onClose: () => void;
  documentTitle: string;
  documentType: string;
  documentContent: string;
  attachments?: File[];
}

const ExportIntegration: React.FC<ExportIntegrationProps> = ({
  open,
  onClose,
  documentTitle,
  documentType,
  documentContent,
  attachments
}) => {
  const [activeTab, setActiveTab] = useState(0);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // بيانات تسجيل الدخول لمنصة مدرستي
  const [madrasatiUsername, setMadrasatiUsername] = useState('');
  const [madrasatiPassword, setMadrasatiPassword] = useState('');
  const [madrasatiRemember, setMadrasatiRemember] = useState(false);
  
  // بيانات تسجيل الدخول لنظام نور
  const [noorUsername, setNoorUsername] = useState('');
  const [noorPassword, setNoorPassword] = useState('');
  const [noorRemember, setNoorRemember] = useState(false);

  // معالجة تغيير التبويب
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    // إعادة تعيين حالة النجاح والخطأ عند تغيير التبويب
    setSuccess(false);
    setError(null);
  };

  // تصدير المستند إلى منصة مدرستي
  const handleExportToMadrasati = async () => {
    if (!madrasatiUsername || !madrasatiPassword) {
      setError('يرجى إدخال اسم المستخدم وكلمة المرور');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // تسجيل الدخول إلى منصة مدرستي
      const loginResponse = await integrationService.loginToMadrasati(madrasatiUsername, madrasatiPassword);
      
      if (!loginResponse.success) {
        throw new Error(loginResponse.message || 'فشل تسجيل الدخول');
      }

      // إعداد بيانات المستند
      const documentData: DocumentData = {
        title: documentTitle,
        content: documentContent,
        type: documentType,
        attachments: attachments,
        metadata: {
          exportDate: new Date().toISOString(),
          source: 'تطبيق معلم برو'
        }
      };

      // تحميل المستند
      const uploadResponse = await integrationService.uploadToMadrasati(documentData);
      
      if (!uploadResponse.success) {
        throw new Error(uploadResponse.message || 'فشل تحميل المستند');
      }

      setSuccess(true);
      
      // تسجيل الخروج إذا لم يتم تحديد "تذكرني"
      if (!madrasatiRemember) {
        integrationService.logoutFromMadrasati();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'حدث خطأ غير متوقع');
      console.error('خطأ في التصدير إلى منصة مدرستي:', err);
    } finally {
      setLoading(false);
    }
  };

  // تصدير المستند إلى نظام نور
  const handleExportToNoor = async () => {
    if (!noorUsername || !noorPassword) {
      setError('يرجى إدخال اسم المستخدم وكلمة المرور');
      return;
    }

    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // تسجيل الدخول إلى نظام نور
      const loginResponse = await integrationService.loginToNoor(noorUsername, noorPassword);
      
      if (!loginResponse.success) {
        throw new Error(loginResponse.message || 'فشل تسجيل الدخول');
      }

      // إعداد بيانات المستند
      const documentData: DocumentData = {
        title: documentTitle,
        content: documentContent,
        type: documentType,
        attachments: attachments,
        metadata: {
          exportDate: new Date().toISOString(),
          source: 'تطبيق معلم برو'
        }
      };

      // تحميل المستند
      const uploadResponse = await integrationService.uploadToNoor(documentData);
      
      if (!uploadResponse.success) {
        throw new Error(uploadResponse.message || 'فشل تحميل المستند');
      }

      setSuccess(true);
      
      // تسجيل الخروج إذا لم يتم تحديد "تذكرني"
      if (!noorRemember) {
        integrationService.logoutFromNoor();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'حدث خطأ غير متوقع');
      console.error('خطأ في التصدير إلى نظام نور:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={loading ? undefined : onClose}
      maxWidth="sm"
      fullWidth
      PaperComponent={motion.div}
      PaperProps={{
        initial: { opacity: 0, y: 20 },
        animate: { opacity: 1, y: 0 },
        transition: { duration: 0.3 },
        style: { borderRadius: 12, overflow: 'hidden' }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Typography variant="h6" component="div" fontWeight="bold">
            تصدير المستند
          </Typography>
          <IconButton
            edge="end"
            color="inherit"
            onClick={onClose}
            disabled={loading}
            aria-label="close"
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      
      <Divider />
      
      <DialogContent sx={{ pt: 2 }}>
        <Typography variant="body2" color="text.secondary" paragraph>
          يمكنك تصدير المستند "{documentTitle}" إلى منصة مدرستي أو نظام نور. يرجى اختيار المنصة وإدخال بيانات الدخول الخاصة بك.
        </Typography>
        
        <Paper elevation={0} sx={{ bgcolor: 'rgba(0, 0, 0, 0.02)', p: 1, borderRadius: 2, mb: 3 }}>
          <Box display="flex" alignItems="center">
            <InfoOutlinedIcon color="info" sx={{ mr: 1 }} />
            <Typography variant="caption" color="text.secondary">
              لن يتم تخزين بيانات الدخول الخاصة بك إلا إذا قمت بتحديد خيار "تذكرني".
            </Typography>
          </Box>
        </Paper>
        
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{
            mb: 2,
            '& .MuiTab-root': {
              py: 1.5,
              transition: 'all 0.2s',
              fontWeight: 500,
              borderRadius: '8px 8px 0 0',
            },
          }}
        >
          <Tab 
            icon={<SchoolIcon />} 
            label="منصة مدرستي" 
            iconPosition="start"
            disabled={loading}
          />
          <Tab 
            icon={<MenuBookIcon />} 
            label="نظام نور" 
            iconPosition="start"
            disabled={loading}
          />
        </Tabs>
        
        {/* محتوى تبويب منصة مدرستي */}
        <TabPanel value={activeTab} index={0}>
          <Box component={motion.div} initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3 }}>
            <TextField
              label="اسم المستخدم في منصة مدرستي"
              variant="outlined"
              fullWidth
              margin="normal"
              value={madrasatiUsername}
              onChange={(e) => setMadrasatiUsername(e.target.value)}
              disabled={loading}
              required
            />
            <TextField
              label="كلمة المرور"
              type="password"
              variant="outlined"
              fullWidth
              margin="normal"
              value={madrasatiPassword}
              onChange={(e) => setMadrasatiPassword(e.target.value)}
              disabled={loading}
              required
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={madrasatiRemember}
                  onChange={(e) => setMadrasatiRemember(e.target.checked)}
                  disabled={loading}
                  color="primary"
                />
              }
              label="تذكرني"
            />
          </Box>
        </TabPanel>
        
        {/* محتوى تبويب نظام نور */}
        <TabPanel value={activeTab} index={1}>
          <Box component={motion.div} initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3 }}>
            <TextField
              label="اسم المستخدم في نظام نور"
              variant="outlined"
              fullWidth
              margin="normal"
              value={noorUsername}
              onChange={(e) => setNoorUsername(e.target.value)}
              disabled={loading}
              required
            />
            <TextField
              label="كلمة المرور"
              type="password"
              variant="outlined"
              fullWidth
              margin="normal"
              value={noorPassword}
              onChange={(e) => setNoorPassword(e.target.value)}
              disabled={loading}
              required
            />
            <FormControlLabel
              control={
                <Checkbox
                  checked={noorRemember}
                  onChange={(e) => setNoorRemember(e.target.checked)}
                  disabled={loading}
                  color="primary"
                />
              }
              label="تذكرني"
            />
          </Box>
        </TabPanel>
        
        {/* رسائل النجاح والخطأ */}
        {success && (
          <Alert 
            severity="success" 
            sx={{ mt: 2 }}
            component={motion.div}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            تم تصدير المستند بنجاح إلى {activeTab === 0 ? 'منصة مدرستي' : 'نظام نور'}!
          </Alert>
        )}
        
        {error && (
          <Alert 
            severity="error" 
            sx={{ mt: 2 }}
            component={motion.div}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {error}
          </Alert>
        )}
      </DialogContent>
      
      <DialogActions sx={{ px: 3, pb: 3, pt: 1 }}>
        <Button 
          onClick={onClose} 
          color="inherit" 
          disabled={loading}
          sx={{ borderRadius: 2 }}
        >
          إلغاء
        </Button>
        <Tooltip title={`تصدير إلى ${activeTab === 0 ? 'منصة مدرستي' : 'نظام نور'}`}>
          <span>
            <Button
              onClick={activeTab === 0 ? handleExportToMadrasati : handleExportToNoor}
              variant="contained"
              color="primary"
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <CloudUploadIcon />}
              sx={{ 
                borderRadius: 2,
                px: 3,
                py: 1,
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
              }}
            >
              {loading ? 'جاري التصدير...' : 'تصدير'}
            </Button>
          </span>
        </Tooltip>
      </DialogActions>
    </Dialog>
  );
};

export default ExportIntegration;