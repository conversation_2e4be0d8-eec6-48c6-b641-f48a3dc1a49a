import React, { useState } from 'react';
import { Box, Paper, Typography, Grid, Button } from '@mui/material';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import PeopleIcon from '@mui/icons-material/People';
import PersonIcon from '@mui/icons-material/Person';
import SupervisorAccountIcon from '@mui/icons-material/SupervisorAccount';
import DescriptionIcon from '@mui/icons-material/Description';
import AddPhotoAlternateIcon from '@mui/icons-material/AddPhotoAlternate';
import GpsFixedIcon from '@mui/icons-material/GpsFixed';

// استيراد المكونات من الملفات المناسبة
import {
  SupervisoryVisitHeader
} from './templates/SupervisoryVisit';

import {
  StudentActivityHeader,
  TeacherAchievementHeader,
  LessonPlanHeader,
  ClassAchievementHeader,
  ProfessionalDevelopmentHeader,
  InfoSection1,
  InfoSection2,
  ContentSection1,
  ContentSection2,
  QualificationSection,
  ExperienceSection,
  AchievementSection,
  TrainingSection,
  DocumentationSection1,
  DocumentationSection2
} from '../pages/Editor';

// تعريف أنواع النماذج المختلفة
interface TemplateModel {
  id: number;
  name: string;
  imageCount: number;
  color: string;
  previewImage: string;
}

interface TemplateContentProps {
  model: TemplateModel;
  activeTab: number;
}

// مكون عرض محتوى القالب بناءً على النموذج المختار
const TemplateContent = ({ model, activeTab }: TemplateContentProps) => {
  const [date, setDate] = useState('');
  const [participantsCount, setParticipantsCount] = useState('');
  const [executor, setExecutor] = useState('');
  const [supervisor, setSupervisor] = useState('');
  const [goal, setGoal] = useState('');
  const [description, setDescription] = useState('');
  
  // استخراج اللون الرئيسي من التدرج اللوني
  const mainColor = model.color.split(' ')[0].replace('linear-gradient(135deg,', '').trim();
  
  // تحديد نوع القالب بناءً على معرف النموذج
  const getTemplateType = (modelId: number) => {
    switch (modelId) {
      case 1: return 'student-activity';
      case 2: return 'teacher-achievement';
      case 3: return 'lesson-plan';
      case 4: return 'supervisory-visit';
      case 5: return 'class-achievement';
      case 6: return 'professional-development';
      default: return 'student-activity';
    }
  };
  
  // إنشاء كائن القالب المحدد
  const selectedTemplate = {
    id: model.id,
    title: `نموذج ${model.id}`,
    type: getTemplateType(model.id)
  };
  
  const renderTemplateContent = () => {
    if (!selectedTemplate) return null;

    // تقديم المحتوى المناسب حسب نوع القالب
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          {selectedTemplate.title}
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          قم بتخصيص هذا القالب حسب احتياجاتك
        </Typography>

        {/* محتوى مخصص لكل نوع من القوالب */}
        {selectedTemplate.type === 'student-activity' && (
          <>
            <StudentActivityHeader />
            <InfoSection1 />
            <ContentSection1 />
            <DocumentationSection1 />
          </>
        )}

        {selectedTemplate.type === 'teacher-achievement' && (
          <>
            <TeacherAchievementHeader />
            <ContentSection2 />
            <QualificationSection />
            <ExperienceSection />
            <AchievementSection />
            <TrainingSection />
            <DocumentationSection2 />
          </>
        )}

        {selectedTemplate.type === 'lesson-plan' && (
          <>
            <LessonPlanHeader />
            <ContentSection1 />
            <DocumentationSection1 />
          </>
        )}

        {selectedTemplate.type === 'supervisory-visit' && (
          <>
            <SupervisoryVisitHeader />
            <ContentSection1 />
            <DocumentationSection1 />
          </>
        )}

        {selectedTemplate.type === 'class-achievement' && (
          <>
            <ClassAchievementHeader />
            <ContentSection1 />
            <DocumentationSection1 />
          </>
        )}

        {selectedTemplate.type === 'professional-development' && (
          <>
            <ProfessionalDevelopmentHeader />
            <ContentSection2 />
            <TrainingSection />
            <DocumentationSection2 />
          </>
        )}
      </Box>
    );
  };
  
  // تحديد عدد الصور بناءً على النموذج المختار
  const renderImagePlaceholders = () => {
    const placeholders = [];
    
    for (let i = 0; i < model.imageCount; i++) {
      placeholders.push(
        <Box key={i} sx={{ mb: 2 }}>
          <Paper
            elevation={0}
            sx={{
              height: 150,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              border: '2px dashed rgba(0,0,0,0.1)',
              borderRadius: 2,
              p: 2,
              cursor: 'pointer',
              transition: 'all 0.2s',
              '&:hover': {
                borderColor: mainColor,
                bgcolor: 'rgba(0,0,0,0.02)'
              }
            }}
          >
            <AddPhotoAlternateIcon sx={{ fontSize: 40, color: 'text.secondary', mb: 1 }} />
            <Typography variant="body2" color="text.secondary">
              انقر لإضافة صورة {i + 1}
            </Typography>
          </Paper>
        </Box>
      );
    }
    
    return placeholders;
  };
  
  // تصميم القالب الجديد المطابق للصورة الثانية
  const renderNewTemplateDesign = () => {
    return (
      <Box sx={{ p: 3 }}>
        <Paper
          elevation={0}
          sx={{
            borderRadius: 3,
            border: '1px solid rgba(0,0,0,0.1)',
            overflow: 'hidden',
            bgcolor: 'white'
          }}
        >
          {/* العنوان الرئيسي */}
          <Box sx={{
            width: '100%',
            p: 2,
            background: model.color,
            color: 'white',
            textAlign: 'center',
            fontWeight: 'bold',
            fontSize: '1.2rem'
          }}>
            {model.name}
          </Box>
          
          {/* المحتوى الرئيسي */}
          <Box sx={{ p: 3 }}>
            <Grid container spacing={3}>
              {/* الجانب الأيسر - مساحة الصورة */}
              <Grid item xs={12} md={5}>
                <Box sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'center'
                }}>
                  <Paper
                    elevation={0}
                    sx={{
                      height: 250,
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      border: '2px dashed rgba(0,0,0,0.1)',
                      borderRadius: 2,
                      p: 2,
                      cursor: 'pointer',
                      transition: 'all 0.2s',
                      '&:hover': {
                        borderColor: mainColor,
                        bgcolor: 'rgba(0,0,0,0.02)'
                      }
                    }}
                  >
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      الصورة
                    </Typography>
                    <AddPhotoAlternateIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 1 }} />
                  </Paper>
                </Box>
              </Grid>
              
              {/* الجانب الأيمن - الحقول */}
              <Grid item xs={12} md={7}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  {/* اليوم والتاريخ */}
                  <Box sx={{
                    p: 2,
                    borderRadius: 2,
                    bgcolor: mainColor,
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <CalendarTodayIcon sx={{ mr: 1 }} />
                    <Typography>اليوم والتاريخ</Typography>
                  </Box>
                  
                  {/* عدد المشاركين */}
                  <Box sx={{
                    p: 2,
                    borderRadius: 2,
                    bgcolor: mainColor,
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <PeopleIcon sx={{ mr: 1 }} />
                    <Typography>عدد المشاركين</Typography>
                  </Box>
                  
                  {/* المنفذ */}
                  <Box sx={{
                    p: 2,
                    borderRadius: 2,
                    bgcolor: mainColor,
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <PersonIcon sx={{ mr: 1 }} />
                    <Typography>المنفذ</Typography>
                  </Box>
                  
                  {/* مشرف التنفيذ */}
                  <Box sx={{
                    p: 2,
                    borderRadius: 2,
                    bgcolor: mainColor,
                    color: 'white',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <SupervisorAccountIcon sx={{ mr: 1 }} />
                    <Typography>مشرف التنفيذ</Typography>
                  </Box>
                </Box>
              </Grid>
            </Grid>
            
            {/* الهدف والوصف */}
            <Box sx={{ mt: 3 }}>
              <Box sx={{
                p: 2,
                borderRadius: 2,
                bgcolor: mainColor,
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mb: 2
              }}>
                <GpsFixedIcon sx={{ mr: 1 }} />
                <Typography>الهدف</Typography>
              </Box>
              
              <Box sx={{
                p: 2,
                borderRadius: 2,
                bgcolor: mainColor,
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <DescriptionIcon sx={{ mr: 1 }} />
                <Typography>الوصف</Typography>
              </Box>
            </Box>
          </Box>
        </Paper>
      </Box>
    );
  };
  
  // عرض التصميم المناسب حسب نوع القالب
  return (
    <Box>
      {renderTemplateContent()}
      {renderNewTemplateDesign()}
    </Box>
  );
};

export default TemplateContent;