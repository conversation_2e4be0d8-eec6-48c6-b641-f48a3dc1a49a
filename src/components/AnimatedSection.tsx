import { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { Box } from '@mui/material';
import { fadeInUp, staggerContainer } from '../styles/animations';

interface AnimatedSectionProps {
  children: ReactNode;
  delay?: number;
  className?: string;
  id?: string;
}

// مكون لإضافة تأثيرات حركية للأقسام
const AnimatedSection = ({ children, delay = 0, className, id }: AnimatedSectionProps) => {
  return (
    <Box
      component={motion.div}
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-100px' }}
      variants={staggerContainer}
      transition={{ delay, staggerChildren: 0.2 }}
      className={className}
      id={id}
    >
      <Box
        component={motion.div}
        variants={fadeInUp}
      >
        {children}
      </Box>
    </Box>
  );
};

export default AnimatedSection;