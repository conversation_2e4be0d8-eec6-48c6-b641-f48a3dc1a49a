import React from 'react';
import {
  Grid,
  Paper,
  Typography,
  TextField,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Button,
  IconButton,
  LinearProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import BarChartIcon from '@mui/icons-material/BarChart';
import FlagIcon from '@mui/icons-material/Flag';
import EmojiEventsIcon from '@mui/icons-material/EmojiEvents';
import ErrorIcon from '@mui/icons-material/Error';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import AttachFileIcon from '@mui/icons-material/AttachFile';

// مكون ترويسة تقرير الإنجاز الفصلي
export const ClassAchievementHeader = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3, bgcolor: 'rgba(37, 99, 235, 0.02)' }}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          معلومات أساسية
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="العام الدراسي" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="الفصل الدراسي" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="المدرسة" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="الصف" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="المادة" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="اسم المعلم" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
        </Grid>
      </Paper>
    </Grid>
  </Grid>
);

// مكون الأهداف المخططة
export const PlannedGoalsSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <FlagIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="subtitle2" color="text.secondary">
            الأهداف المخططة
          </Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        
        {[1, 2, 3].map((goal) => (
          <Box key={goal} sx={{ mb: 3, p: 2, bgcolor: 'rgba(0,0,0,0.02)', borderRadius: 1 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="subtitle2">الهدف {goal}</Typography>
              <IconButton size="small" color="error">
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Box>
            <TextField
              fullWidth
              label="وصف الهدف"
              variant="outlined"
              sx={{ mb: 2 }}
            />
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel>نوع الهدف</InputLabel>
                  <Select
                    label="نوع الهدف"
                    defaultValue="academic"
                  >
                    <MenuItem value="academic">أكاديمي</MenuItem>
                    <MenuItem value="behavioral">سلوكي</MenuItem>
                    <MenuItem value="skill">مهاري</MenuItem>
                    <MenuItem value="other">أخرى</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel>مستوى الأولوية</InputLabel>
                  <Select
                    label="مستوى الأولوية"
                    defaultValue="medium"
                  >
                    <MenuItem value="high">عالية</MenuItem>
                    <MenuItem value="medium">متوسطة</MenuItem>
                    <MenuItem value="low">منخفضة</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        ))}
        
        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          sx={{ mt: 2 }}
        >
          إضافة هدف جديد
        </Button>
      </Paper>
    </Grid>
  </Grid>
);

// مكون الإنجازات المحققة
export const AchievementsSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <EmojiEventsIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="subtitle2" color="text.secondary">
            الإنجازات المحققة
          </Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        
        <List>
          {[
            'تحسن مستوى الطلاب في مهارات القراءة بنسبة 15%',
            'تنفيذ 5 أنشطة تعليمية تفاعلية',
            'مشاركة الطلاب في مسابقة على مستوى المنطقة التعليمية',
            'تطبيق استراتيجيات تعليمية حديثة في الفصل'
          ].map((achievement, index) => (
            <ListItem key={index} sx={{ py: 1 }}>
              <ListItemIcon>
                <CheckCircleIcon color="success" />
              </ListItemIcon>
              <ListItemText primary={achievement} />
            </ListItem>
          ))}
        </List>
        
        <TextField
          fullWidth
          label="إنجازات أخرى"
          variant="outlined"
          multiline
          rows={3}
          sx={{ mt: 2 }}
        />
        
        <Box sx={{ mt: 3 }}>
          <Typography variant="body2" gutterBottom>
            نسبة تحقق الأهداف المخططة:
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{ width: '100%', mr: 1 }}>
              <LinearProgress variant="determinate" value={75} sx={{ height: 10, borderRadius: 5 }} />
            </Box>
            <Box sx={{ minWidth: 35 }}>
              <Typography variant="body2" color="text.secondary">75%</Typography>
            </Box>
          </Box>
        </Box>
      </Paper>
    </Grid>
  </Grid>
);

// مكون التحديات
export const ChallengesSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <ErrorIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="subtitle2" color="text.secondary">
            التحديات
          </Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        
        <TextField
          fullWidth
          label="التحديات التي واجهت تنفيذ الخطة"
          variant="outlined"
          multiline
          rows={4}
          sx={{ mb: 3 }}
        />
        
        <TextField
          fullWidth
          label="الحلول المقترحة للتغلب على التحديات"
          variant="outlined"
          multiline
          rows={4}
        />
      </Paper>
    </Grid>
  </Grid>
);

// مكون الإحصائيات والمؤشرات
export const StatisticsSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <BarChartIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="subtitle2" color="text.secondary">
            إحصائيات ومؤشرات
          </Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="عدد الطلاب الكلي"
              type="number"
              variant="outlined"
              sx={{ mb: 2 }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="نسبة النجاح"
              type="number"
              InputProps={{ endAdornment: '%' }}
              variant="outlined"
              sx={{ mb: 2 }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="متوسط درجات الطلاب"
              type="number"
              variant="outlined"
              sx={{ mb: 2 }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="عدد الطلاب المتفوقين"
              type="number"
              variant="outlined"
              sx={{ mb: 2 }}
            />
          </Grid>
        </Grid>
        
        <TextField
          fullWidth
          label="ملاحظات حول الإحصائيات"
          variant="outlined"
          multiline
          rows={3}
          sx={{ mt: 2 }}
        />
      </Paper>
    </Grid>
  </Grid>
);

// مكون الخطط المستقبلية
export const FuturePlansSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <TrendingUpIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="subtitle2" color="text.secondary">
            الخطط المستقبلية
          </Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        
        <TextField
          fullWidth
          label="الخطط المستقبلية للفصل القادم"
          variant="outlined"
          multiline
          rows={4}
          sx={{ mb: 3 }}
        />
        
        <TextField
          fullWidth
          label="مقترحات التطوير"
          variant="outlined"
          multiline
          rows={4}
        />
      </Paper>
    </Grid>
  </Grid>
);

// مكون المرفقات والوثائق
export const AttachmentsSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <AttachFileIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="subtitle2" color="text.secondary">
            المرفقات والوثائق
          </Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        
        <Box
          sx={{
            p: 3,
            border: '2px dashed rgba(0,0,0,0.1)',
            borderRadius: 2,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            mb: 3,
            cursor: 'pointer',
            '&:hover': {
              borderColor: 'primary.main',
              bgcolor: 'rgba(0,0,0,0.02)'
            }
          }}
        >
          <AttachFileIcon sx={{ fontSize: 40, color: 'text.secondary', mb: 1 }} />
          <Typography variant="body2" color="text.secondary">
            اضغط هنا لإرفاق ملفات أو اسحب الملفات وأفلتها هنا
          </Typography>
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
            الحد الأقصى لحجم الملف: 10 ميجابايت
          </Typography>
        </Box>
        
        <TextField
          fullWidth
          label="ملاحظات حول المرفقات"
          variant="outlined"
          multiline
          rows={2}
        />
      </Paper>
    </Grid>
  </Grid>
);
