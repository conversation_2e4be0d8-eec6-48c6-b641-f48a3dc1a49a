import React from 'react';
import {
  Grid,
  Paper,
  Typography,
  TextField,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Button,
  IconButton,
  <PERSON>ing,
  Chip,
  Stack
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';
import AssessmentIcon from '@mui/icons-material/Assessment';
import TimelineIcon from '@mui/icons-material/Timeline';
import FlagIcon from '@mui/icons-material/Flag';
import BuildIcon from '@mui/icons-material/Build';
import InventoryIcon from '@mui/icons-material/Inventory';
import DateRangeIcon from '@mui/icons-material/DateRange';
import GradingIcon from '@mui/icons-material/Grading';

// مكون ترويسة خطة التطوير المهني
export const ProfessionalDevelopmentHeader = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3, bgcolor: 'rgba(37, 99, 235, 0.02)' }}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          المعلومات الشخصية
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="الاسم" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="التخصص" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="المسمى الوظيفي" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="المدرسة" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="سنوات الخبرة" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="العام الدراسي" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
        </Grid>
      </Paper>
    </Grid>
  </Grid>
);

// مكون التقييم الذاتي
export const SelfAssessmentSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <AssessmentIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="subtitle2" color="text.secondary">
            التقييم الذاتي
          </Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        
        <Typography variant="body2" gutterBottom>
          قيّم مهاراتك الحالية في المجالات التالية:
        </Typography>
        
        <Box sx={{ my: 2 }}>
          <Typography component="legend" variant="body2" gutterBottom>
            مهارات التدريس
          </Typography>
          <Rating name="teaching-skills" defaultValue={3} precision={0.5} />
        </Box>
        
        <Box sx={{ my: 2 }}>
          <Typography component="legend" variant="body2" gutterBottom>
            استخدام التكنولوجيا
          </Typography>
          <Rating name="tech-skills" defaultValue={3} precision={0.5} />
        </Box>
        
        <Box sx={{ my: 2 }}>
          <Typography component="legend" variant="body2" gutterBottom>
            إدارة الصف
          </Typography>
          <Rating name="class-management" defaultValue={3} precision={0.5} />
        </Box>
        
        <Box sx={{ my: 2 }}>
          <Typography component="legend" variant="body2" gutterBottom>
            التقييم والقياس
          </Typography>
          <Rating name="assessment-skills" defaultValue={3} precision={0.5} />
        </Box>
        
        <TextField
          fullWidth
          label="ملاحظات إضافية حول التقييم الذاتي"
          variant="outlined"
          multiline
          rows={4}
          sx={{ mt: 3 }}
        />
      </Paper>
    </Grid>
  </Grid>
);

// مكون الأهداف التطويرية
export const DevelopmentGoalsSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <FlagIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="subtitle2" color="text.secondary">
            الأهداف التطويرية
          </Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        
        {[1, 2, 3].map((goal) => (
          <Box key={goal} sx={{ mb: 3, p: 2, bgcolor: 'rgba(0,0,0,0.02)', borderRadius: 1 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="subtitle2">الهدف {goal}</Typography>
              <IconButton size="small" color="error">
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Box>
            <TextField
              fullWidth
              label="وصف الهدف"
              variant="outlined"
              sx={{ mb: 2 }}
            />
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel>مستوى الأولوية</InputLabel>
                  <Select
                    label="مستوى الأولوية"
                    defaultValue="medium"
                  >
                    <MenuItem value="high">عالية</MenuItem>
                    <MenuItem value="medium">متوسطة</MenuItem>
                    <MenuItem value="low">منخفضة</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth variant="outlined">
                  <InputLabel>المجال</InputLabel>
                  <Select
                    label="المجال"
                    defaultValue="teaching"
                  >
                    <MenuItem value="teaching">مهارات التدريس</MenuItem>
                    <MenuItem value="technology">التكنولوجيا</MenuItem>
                    <MenuItem value="management">الإدارة الصفية</MenuItem>
                    <MenuItem value="assessment">التقييم</MenuItem>
                    <MenuItem value="other">أخرى</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        ))}
        
        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          sx={{ mt: 2 }}
        >
          إضافة هدف جديد
        </Button>
      </Paper>
    </Grid>
  </Grid>
);

// مكون خطة العمل
export const ActionPlanSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <BuildIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="subtitle2" color="text.secondary">
            خطة العمل
          </Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        
        {[1, 2].map((action) => (
          <Box key={action} sx={{ mb: 3, p: 2, bgcolor: 'rgba(0,0,0,0.02)', borderRadius: 1 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="subtitle2">الإجراء {action}</Typography>
              <IconButton size="small" color="error">
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Box>
            <TextField
              fullWidth
              label="وصف الإجراء"
              variant="outlined"
              sx={{ mb: 2 }}
            />
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="تاريخ البدء"
                  type="date"
                  InputLabelProps={{ shrink: true }}
                  variant="outlined"
                  sx={{ mb: 2 }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="تاريخ الانتهاء"
                  type="date"
                  InputLabelProps={{ shrink: true }}
                  variant="outlined"
                  sx={{ mb: 2 }}
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="مؤشرات النجاح"
                  variant="outlined"
                  multiline
                  rows={2}
                />
              </Grid>
            </Grid>
          </Box>
        ))}
        
        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          sx={{ mt: 2 }}
        >
          إضافة إجراء جديد
        </Button>
      </Paper>
    </Grid>
  </Grid>
);

// مكون الموارد المطلوبة
export const ResourcesSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <InventoryIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="subtitle2" color="text.secondary">
            الموارد المطلوبة
          </Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        
        <Stack direction="row" spacing={1} sx={{ mb: 2 }}>
          <Chip label="دورات تدريبية" color="primary" variant="outlined" />
          <Chip label="كتب ومراجع" color="primary" variant="outlined" />
          <Chip label="برامج تعليمية" color="primary" variant="outlined" />
          <Chip label="منصات إلكترونية" color="primary" variant="outlined" />
        </Stack>
        
        <TextField
          fullWidth
          label="تفاصيل الموارد المطلوبة"
          variant="outlined"
          multiline
          rows={4}
          sx={{ mb: 3 }}
        />
        
        <TextField
          fullWidth
          label="الميزانية التقديرية (إن وجدت)"
          variant="outlined"
        />
      </Paper>
    </Grid>
  </Grid>
);

// مكون الجدول الزمني
export const TimelineSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <DateRangeIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="subtitle2" color="text.secondary">
            الجدول الزمني
          </Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        
        <Box sx={{ mb: 3 }}>
          <Typography variant="body2" gutterBottom>
            الفترة الزمنية للخطة:
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="تاريخ بداية الخطة"
                type="date"
                InputLabelProps={{ shrink: true }}
                variant="outlined"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="تاريخ نهاية الخطة"
                type="date"
                InputLabelProps={{ shrink: true }}
                variant="outlined"
              />
            </Grid>
          </Grid>
        </Box>
        
        <TextField
          fullWidth
          label="ملاحظات حول الجدول الزمني"
          variant="outlined"
          multiline
          rows={3}
        />
      </Paper>
    </Grid>
  </Grid>
);

// مكون آلية التقييم والمتابعة
export const EvaluationSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <GradingIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="subtitle2" color="text.secondary">
            آلية التقييم والمتابعة
          </Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        
        <TextField
          fullWidth
          label="طرق قياس مدى تحقق الأهداف"
          variant="outlined"
          multiline
          rows={3}
          sx={{ mb: 3 }}
        />
        
        <TextField
          fullWidth
          label="آلية المتابعة الدورية"
          variant="outlined"
          multiline
          rows={3}
          sx={{ mb: 3 }}
        />
        
        <TextField
          fullWidth
          label="الجهات المشاركة في التقييم"
          variant="outlined"
        />
      </Paper>
    </Grid>
  </Grid>
);
