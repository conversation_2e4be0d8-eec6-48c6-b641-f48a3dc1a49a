import React from 'react';
import {
  Grid,
  Paper,
  Typography,
  TextField,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  Rating,
  Chip,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import SchoolIcon from '@mui/icons-material/School';
import AssessmentIcon from '@mui/icons-material/Assessment';
import ThumbUpIcon from '@mui/icons-material/ThumbUp';
import BuildIcon from '@mui/icons-material/Build';
import RecommendIcon from '@mui/icons-material/Recommend';

// مكون ترويسة تقرير الزيارة الإشرافية
export const SupervisoryVisitHeader = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3, bgcolor: 'rgba(37, 99, 235, 0.02)' }}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          معلومات الزيارة
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="تاريخ الزيارة"
              type="date"
              InputLabelProps={{ shrink: true }}
              variant="outlined"
              sx={{ mb: 2 }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="اسم المشرف" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="المدرسة" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="المنطقة التعليمية" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
              <InputLabel>نوع الزيارة</InputLabel>
              <Select
                label="نوع الزيارة"
                defaultValue="regular"
              >
                <MenuItem value="regular">زيارة دورية</MenuItem>
                <MenuItem value="followup">زيارة متابعة</MenuItem>
                <MenuItem value="special">زيارة خاصة</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="رقم الزيارة" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
        </Grid>
      </Paper>
    </Grid>
  </Grid>
);

// مكون بيانات المعلم
export const TeacherInfoSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <PersonIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="subtitle2" color="text.secondary">
            بيانات المعلم
          </Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="اسم المعلم" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="التخصص" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="المؤهل العلمي" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="سنوات الخبرة" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="الرقم الوظيفي" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
              <InputLabel>المرحلة التعليمية</InputLabel>
              <Select
                label="المرحلة التعليمية"
                defaultValue="middle"
              >
                <MenuItem value="elementary">ابتدائي</MenuItem>
                <MenuItem value="middle">متوسط</MenuItem>
                <MenuItem value="high">ثانوي</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>
    </Grid>
  </Grid>
);

// مكون معلومات الدرس
export const LessonInfoSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <SchoolIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="subtitle2" color="text.secondary">
            معلومات الدرس
          </Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="المادة" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="الصف" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="عنوان الدرس" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="الحصة" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="عدد الطلاب" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="زمن الحصة" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="الأهداف التعليمية"
              variant="outlined"
              multiline
              rows={3}
              sx={{ mb: 2 }}
            />
          </Grid>
        </Grid>
      </Paper>
    </Grid>
  </Grid>
);

// مكون تقييم الأداء
export const PerformanceEvaluationSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <AssessmentIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="subtitle2" color="text.secondary">
            تقييم الأداء
          </Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>عناصر التقييم</TableCell>
                <TableCell align="center">ممتاز</TableCell>
                <TableCell align="center">جيد جداً</TableCell>
                <TableCell align="center">جيد</TableCell>
                <TableCell align="center">مقبول</TableCell>
                <TableCell align="center">ضعيف</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {[
                'التخطيط للدرس',
                'إدارة الصف',
                'استراتيجيات التدريس',
                'استخدام التقنية',
                'التفاعل مع الطلاب',
                'أساليب التقويم',
                'تحقيق أهداف الدرس'
              ].map((item, index) => (
                <TableRow key={index}>
                  <TableCell component="th" scope="row">
                    {item}
                  </TableCell>
                  {[5, 4, 3, 2, 1].map((value) => (
                    <TableCell key={value} align="center">
                      <input
                        type="radio"
                        name={`rating-${index}`}
                        value={value}
                        defaultChecked={value === 4 && index % 2 === 0}
                        defaultChecked={value === 5 && index % 2 === 1}
                      />
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        
        <Box sx={{ mt: 3 }}>
          <Typography variant="body2" gutterBottom>
            التقييم العام:
          </Typography>
          <Rating name="overall-rating" defaultValue={4} precision={0.5} size="large" />
        </Box>
        
        <TextField
          fullWidth
          label="ملاحظات عامة حول الأداء"
          variant="outlined"
          multiline
          rows={3}
          sx={{ mt: 3 }}
        />
      </Paper>
    </Grid>
  </Grid>
);

// مكون نقاط القوة
export const StrengthsSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <ThumbUpIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="subtitle2" color="text.secondary">
            نقاط القوة
          </Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        
        <Stack direction="row" spacing={1} flexWrap="wrap" sx={{ mb: 2 }}>
          <Chip label="التخطيط الجيد للدرس" color="success" sx={{ m: 0.5 }} />
          <Chip label="التمكن من المادة العلمية" color="success" sx={{ m: 0.5 }} />
          <Chip label="التفاعل الإيجابي مع الطلاب" color="success" sx={{ m: 0.5 }} />
          <Chip label="استخدام التقنية بفعالية" color="success" sx={{ m: 0.5 }} />
          <Chip label="تنويع أساليب التدريس" color="success" sx={{ m: 0.5 }} />
        </Stack>
        
        <TextField
          fullWidth
          label="نقاط قوة أخرى"
          variant="outlined"
          multiline
          rows={3}
        />
      </Paper>
    </Grid>
  </Grid>
);

// مكون مجالات التحسين
export const ImprovementsSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <BuildIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="subtitle2" color="text.secondary">
            مجالات التحسين
          </Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        
        <Stack direction="row" spacing={1} flexWrap="wrap" sx={{ mb: 2 }}>
          <Chip label="تنويع أساليب التقويم" color="warning" sx={{ m: 0.5 }} />
          <Chip label="مراعاة الفروق الفردية" color="warning" sx={{ m: 0.5 }} />
          <Chip label="إدارة وقت الحصة" color="warning" sx={{ m: 0.5 }} />
          <Chip label="تفعيل مشاركة جميع الطلاب" color="warning" sx={{ m: 0.5 }} />
        </Stack>
        
        <TextField
          fullWidth
          label="مجالات تحسين أخرى"
          variant="outlined"
          multiline
          rows={3}
        />
      </Paper>
    </Grid>
  </Grid>
);

// مكون التوصيات
export const RecommendationsSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <RecommendIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="subtitle2" color="text.secondary">
            التوصيات
          </Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        
        <TextField
          fullWidth
          label="التوصيات والمقترحات"
          variant="outlined"
          multiline
          rows={4}
          sx={{ mb: 3 }}
        />
        
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="تاريخ الزيارة القادمة"
              type="date"
              InputLabelProps={{ shrink: true }}
              variant="outlined"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <FormControl fullWidth variant="outlined">
              <InputLabel>الحاجة لزيارة متابعة</InputLabel>
              <Select
                label="الحاجة لزيارة متابعة"
                defaultValue="yes"
              >
                <MenuItem value="yes">نعم</MenuItem>
                <MenuItem value="no">لا</MenuItem>
              </Select>
            </FormControl>
          </Grid>
        </Grid>
        
        <Box sx={{ mt: 3, p: 2, bgcolor: 'rgba(0,0,0,0.02)', borderRadius: 1 }}>
          <Typography variant="subtitle2" gutterBottom>
            توقيعات:
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={4}>
              <TextField fullWidth label="المعلم" variant="outlined" />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField fullWidth label="المشرف التربوي" variant="outlined" />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField fullWidth label="قائد المدرسة" variant="outlined" />
            </Grid>
          </Grid>
        </Box>
      </Paper>
    </Grid>
  </Grid>
);
