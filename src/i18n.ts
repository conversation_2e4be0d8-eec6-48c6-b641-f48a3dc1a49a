import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

// the translations
const resources = {
  ar: {
    translation: {
      // Common
      'app.name': 'معلم برو',
      'app.tagline': 'أداة ذكية لإنشاء محتوى تعليمي وإداري احترافي',
      
      // Navigation
      'nav.home': 'الرئيسية',
      'nav.chat': 'المحادثة الذكية',
      'nav.templates': 'القوالب',
      'nav.profile': 'الملف الشخصي',
      'nav.login': 'تسجيل الدخول',
      'nav.register': 'إنشاء حساب',
      'nav.logout': 'تسجيل الخروج',
      
      // Home Page
      'home.hero.title': 'أنشئ محتوى تعليمي احترافي بسرعة وسهولة',
      'home.hero.subtitle': 'استخدم الذكاء الاصطناعي لإنشاء تقارير، ملفات إنجاز، ومستندات تعليمية بجودة عالية',
      'home.cta.start': 'ابدأ الآن',
      'home.cta.learn': 'تعرف على المزيد',
      
      // Chat Page
      'chat.placeholder': 'اكتب عنوان المستند المطلوب...',
      'chat.send': 'إرسال',
      'chat.suggestions': 'اقتراحات',
      'chat.suggestion.report': 'تقرير نشاط طلابي',
      'chat.suggestion.portfolio': 'ملف إنجاز معلم',
      'chat.suggestion.lesson': 'خطة درس',
      
      // Templates Page
      'templates.title': 'قوالب احترافية',
      'templates.filter.all': 'جميع القوالب',
      'templates.filter.reports': 'تقارير',
      'templates.filter.portfolios': 'ملفات إنجاز',
      'templates.filter.lessons': 'خطط دروس',
      'templates.filter.admin': 'مستندات إدارية',
      'templates.use': 'استخدام القالب',
      
      // Editor Page
      'editor.save': 'حفظ',
      'editor.export': 'تصدير',
      'editor.export.pdf': 'تصدير كـ PDF',
      'editor.export.image': 'تصدير كصورة',
      'editor.share': 'مشاركة',
      
      // Auth Pages
      'auth.email': 'البريد الإلكتروني',
      'auth.password': 'كلمة المرور',
      'auth.login': 'تسجيل الدخول',
      'auth.register': 'إنشاء حساب',
      'auth.name': 'الاسم',
      'auth.school': 'المدرسة / الجهة',
      'auth.forgot': 'نسيت كلمة المرور؟',
      'auth.no.account': 'ليس لديك حساب؟',
      'auth.has.account': 'لديك حساب بالفعل؟',
      
      // Profile Page
      'profile.title': 'الملف الشخصي',
      'profile.subscription': 'الاشتراك',
      'profile.documents': 'المستندات المحفوظة',
      'profile.templates': 'القوالب المخصصة',
      'profile.settings': 'الإعدادات',
      
      // Errors
      'error.404': 'الصفحة غير موجودة',
      'error.back': 'العودة للرئيسية'
    }
  }
};

i18n
  .use(initReactI18next) // passes i18n down to react-i18next
  .init({
    resources,
    lng: 'ar', // default language
    interpolation: {
      escapeValue: false // react already safes from xss
    }
  });

export default i18n;
