import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Paper from '@mui/material/Paper';

const NotFoundPage = () => {
  const { t } = useTranslation();

  return (
    <Container component="main" maxWidth="sm">
      <Paper
        sx={{
          mt: 8,
          p: 4,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          textAlign: 'center',
        }}
      >
        <Typography variant="h1" component="h1" gutterBottom>
          404
        </Typography>
        <Typography variant="h4" component="h2" gutterBottom>
          {t('error.404')}
        </Typography>
        <Typography variant="body1" paragraph>
          الصفحة التي تبحث عنها غير موجودة أو تم نقلها أو حذفها.
        </Typography>
        <Box sx={{ mt: 4 }}>
          <Button
            component={Link}
            to="/"
            variant="contained"
            size="large"
          >
            {t('error.back')}
          </Button>
        </Box>
      </Paper>
    </Container>
  );
};

export default NotFoundPage;
