import { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import SendIcon from '@mui/icons-material/Send';
import Chip from '@mui/material/Chip';
import Avatar from '@mui/material/Avatar';
import Grid from '@mui/material/Grid';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import PersonIcon from '@mui/icons-material/Person';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import LightbulbIcon from '@mui/icons-material/Lightbulb';
import { fadeIn, fadeInUp, messageWave, chatBubble, suggestionChip } from '../styles/animations';

interface Message {
  id: number;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
}

const ChatPage = () => {
  const { t } = useTranslation();
  const [input, setInput] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      text: '🌟 مرحباً بك في معلم برو! 🌟\n\nأنا مساعدك الذكي المتخصص في إنشاء المحتوى التعليمي والإداري. يمكنني مساعدتك في:\n\n📋 إنشاء تقارير الأنشطة الطلابية\n📁 إعداد ملفات الإنجاز للمعلمين\n📚 تصميم خطط الدروس التفاعلية\n📝 إعداد الاختبارات والتقييمات\n📄 كتابة المستندات الإدارية\n✉️ صياغة الخطابات الرسمية\n\nما نوع المستند الذي تحتاج إلى إنشائه اليوم؟ 😊',
      sender: 'ai',
      timestamp: new Date(),
    },
  ]);

  // تمرير تلقائي إلى آخر رسالة
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // تمرير إلى أعلى الصفحة عند تحميلها
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // ردود ذكية بناءً على المدخلات
  const generateAIResponse = (userInput: string): string => {
    const input = userInput.toLowerCase();

    // ردود خاصة بالتقارير
    if (input.includes('تقرير') || input.includes('نشاط') || input.includes('طلابي')) {
      return 'ممتاز! سأساعدك في إنشاء تقرير نشاط طلابي احترافي. يمكنني إنشاء تقرير يتضمن:\n\n• تفاصيل النشاط والأهداف\n• المشاركين والمستفيدين\n• النتائج والتوصيات\n• الصور والمرفقات\n\nما هو موضوع النشاط الذي تريد كتابة تقرير عنه؟';
    }

    // ردود خاصة بملفات الإنجاز
    if (input.includes('ملف') || input.includes('إنجاز') || input.includes('معلم')) {
      return 'رائع! سأساعدك في إعداد ملف إنجاز معلم شامل ومنظم. يمكنني إنشاء ملف يحتوي على:\n\n• السيرة الذاتية والمؤهلات\n• الخبرات التعليمية والتدريبية\n• الإنجازات والجوائز\n• نماذج من الأعمال والمشاريع\n• شهادات التقدير والتطوير المهني\n\nما هي المعلومات الأساسية التي تريد تضمينها؟';
    }

    // ردود خاصة بخطط الدروس
    if (input.includes('خطة') || input.includes('درس') || input.includes('تحضير')) {
      return 'ممتاز! سأساعدك في إعداد خطة درس تفاعلية ومتكاملة. يمكنني إنشاء خطة تشمل:\n\n• الأهداف التعليمية والمهارات المستهدفة\n• المقدمة والتمهيد\n• الأنشطة والاستراتيجيات التعليمية\n• التقويم والواجبات\n• الموارد والوسائل التعليمية\n\nما هي المادة والصف الذي تريد إعداد خطة درس له؟';
    }

    // ردود خاصة بالمستندات الإدارية
    if (input.includes('إداري') || input.includes('مستند') || input.includes('رسمي')) {
      return 'بالطبع! يمكنني مساعدتك في إنشاء مستندات إدارية احترافية مثل:\n\n• الخطابات الرسمية\n• التقارير الإدارية\n• المذكرات والتعاميم\n• خطط العمل والمشاريع\n• التقييمات والمتابعات\n\nما نوع المستند الإداري الذي تحتاجه؟';
    }

    // ردود خاصة بالاختبارات والتقييم
    if (input.includes('اختبار') || input.includes('تقييم') || input.includes('أسئلة')) {
      return 'ممتاز! سأساعدك في إعداد اختبارات وأدوات تقييم متنوعة:\n\n• أسئلة اختيار من متعدد\n• أسئلة مقالية ومفتوحة\n• أسئلة صح وخطأ\n• أنشطة تقييم تفاعلية\n• نماذج تقييم الأداء\n\nما هي المادة والمستوى التعليمي للاختبار؟';
    }

    // ردود عامة للاستفسارات
    if (input.includes('كيف') || input.includes('ماذا') || input.includes('أين')) {
      return 'أنا هنا لمساعدتك! يمكنني إنشاء مجموعة واسعة من المستندات التعليمية والإدارية:\n\n📋 تقارير الأنشطة الطلابية\n📁 ملفات الإنجاز للمعلمين\n📚 خطط الدروس التفاعلية\n📄 المستندات الإدارية\n📝 الاختبارات والتقييمات\n\nما نوع المستند الذي تحتاج إلى إنشائه؟';
    }

    // رد افتراضي
    return 'شكراً لك! فهمت طلبك وسأساعدك في إنشاء مستند احترافي يلبي احتياجاتك. \n\nيمكنني إنشاء مستندات متنوعة مثل التقارير وملفات الإنجاز وخطط الدروس. هل يمكنك توضيح المزيد من التفاصيل حول ما تحتاجه بالضبط؟\n\nأو يمكنك اختيار أحد الاقتراحات أدناه للبدء السريع.';
  };

  const handleSendMessage = () => {
    if (input.trim() === '') return;

    // Add user message
    const userMessage: Message = {
      id: messages.length + 1,
      text: input,
      sender: 'user',
      timestamp: new Date(),
    };

    const currentInput = input;
    setMessages([...messages, userMessage]);
    setInput('');

    // إظهار مؤشر الكتابة
    setIsTyping(true);

    // Generate intelligent AI response
    setTimeout(() => {
      setIsTyping(false);
      const aiResponse = generateAIResponse(currentInput);
      const aiMessage: Message = {
        id: messages.length + 2,
        text: aiResponse,
        sender: 'ai',
        timestamp: new Date(),
      };
      setMessages(prevMessages => [...prevMessages, aiMessage]);
    }, Math.random() * 1000 + 1000); // رد عشوائي بين 1-2 ثانية
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const suggestions = [
    { id: 1, text: 'تقرير نشاط طلابي', icon: '📋' },
    { id: 2, text: 'ملف إنجاز معلم', icon: '📁' },
    { id: 3, text: 'خطة درس تفاعلية', icon: '📚' },
    { id: 4, text: 'اختبار تقييمي', icon: '📝' },
    { id: 5, text: 'مستند إداري', icon: '📄' },
    { id: 6, text: 'خطاب رسمي', icon: '✉️' },
  ];

  const handleSuggestionClick = (suggestion: string) => {
    setInput(suggestion);
  };

  return (
    <Container maxWidth="lg">
      {/* زر الرجوع */}
      <Box sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
        <Button
          component={Link}
          to="/"
          startIcon={<ArrowBackIcon />}
          sx={{
            color: 'text.secondary',
            '&:hover': {
              bgcolor: 'rgba(0,0,0,0.04)',
              color: 'primary.main',
            },
            transition: 'all 0.2s',
            fontWeight: 500,
          }}
        >
          العودة إلى الرئيسية
        </Button>
      </Box>

      <motion.div
        initial="hidden"
        animate="visible"
        variants={fadeInUp}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Avatar
            sx={{
              bgcolor: 'primary.main',
              mr: 2,
              width: 48,
              height: 48,
              boxShadow: '0 4px 12px rgba(37, 99, 235, 0.2)'
            }}
          >
            <AutoAwesomeIcon sx={{ color: 'white' }} />
          </Avatar>
          <Typography
            variant="h4"
            component="h1"
            gutterBottom
            fontWeight="bold"
            sx={{
              mt: 1,
              background: 'linear-gradient(90deg, #2563eb, #0ea5e9)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
            }}
          >
            {t('nav.chat')}
          </Typography>
        </Box>
      </motion.div>

      <Paper
        elevation={0}
        sx={{
          height: 'calc(100vh - 220px)',
          display: 'flex',
          flexDirection: 'column',
          borderRadius: 4,
          overflow: 'hidden',
          boxShadow: '0 8px 32px rgba(0,0,0,0.08)',
          border: '1px solid rgba(0,0,0,0.05)',
          mb: 4,
          background: 'linear-gradient(135deg, #f9fafc 0%, #f1f5f9 100%)',
          position: 'relative',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: 'radial-gradient(circle at 25px 25px, rgba(37, 99, 235, 0.03) 2%, transparent 0%), radial-gradient(circle at 75px 75px, rgba(14, 165, 233, 0.03) 2%, transparent 0%)',
            backgroundSize: '100px 100px',
            pointerEvents: 'none',
            zIndex: 0
          }
        }}
      >
        {/* Chat Messages */}
        <Box sx={{ flexGrow: 1, overflow: 'auto', p: 3, position: 'relative', zIndex: 1 }}>
          {messages.map((message, index) => (
            <motion.div
              key={message.id}
              initial="hidden"
              animate="visible"
              variants={messageWave}
              custom={index}
            >
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: message.sender === 'user' ? 'flex-start' : 'flex-start',
                  mb: 3,
                }}
              >
                <Avatar
                  sx={{
                    bgcolor: message.sender === 'user' ? 'primary.main' : 'secondary.main',
                    mr: message.sender === 'user' ? 1 : 0,
                    ml: message.sender === 'ai' ? 1 : 0,
                    boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
                    width: 40,
                    height: 40,
                  }}
                >
                  {message.sender === 'user' ? <PersonIcon /> : <SmartToyIcon />}
                </Avatar>
                <motion.div
                  initial="initial"
                  animate="animate"
                  whileHover="hover"
                  variants={chatBubble}
                  style={{ maxWidth: '75%' }}
                >
                  <Paper
                    elevation={0}
                    sx={{
                      p: 2,
                      ml: message.sender === 'user' ? 1 : 0,
                      mr: message.sender === 'ai' ? 1 : 0,
                      backgroundColor: message.sender === 'user' ? 'primary.main' : 'white',
                      color: message.sender === 'user' ? 'white' : 'text.primary',
                      borderRadius: 3,
                      boxShadow: message.sender === 'user'
                        ? '0 4px 15px rgba(37, 99, 235, 0.2)'
                        : '0 4px 15px rgba(0,0,0,0.05)',
                      border: message.sender === 'ai' ? '1px solid rgba(0,0,0,0.05)' : 'none',
                      position: 'relative',
                      '&::after': message.sender === 'user' ? {
                        content: '""',
                        position: 'absolute',
                        right: '-8px',
                        top: '14px',
                        transform: 'rotate(45deg)',
                        width: '16px',
                        height: '16px',
                        backgroundColor: 'primary.main',
                        zIndex: -1
                      } : {},
                      '&::before': message.sender === 'ai' ? {
                        content: '""',
                        position: 'absolute',
                        left: '-8px',
                        top: '14px',
                        transform: 'rotate(45deg)',
                        width: '16px',
                        height: '16px',
                        backgroundColor: 'white',
                        border: '1px solid rgba(0,0,0,0.05)',
                        borderRight: 'none',
                        borderTop: 'none',
                        zIndex: -1
                      } : {}
                    }}
                  >
                    <Typography variant="body1" sx={{ lineHeight: 1.6 }}>{message.text}</Typography>
                    <Typography
                      variant="caption"
                      color={message.sender === 'user' ? 'rgba(255,255,255,0.8)' : 'text.secondary'}
                      sx={{ display: 'block', mt: 1, textAlign: 'right' }}
                    >
                      {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </Typography>
                  </Paper>
                </motion.div>
              </Box>
            </motion.div>
          ))}

          {/* مؤشر الكتابة */}
          {isTyping && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'secondary.main', ml: 1, boxShadow: '0 2px 8px rgba(0,0,0,0.1)', width: 40, height: 40 }}>
                  <SmartToyIcon />
                </Avatar>
                <Paper
                  elevation={0}
                  sx={{
                    p: 2,
                    mr: 1,
                    backgroundColor: 'white',
                    borderRadius: 3,
                    boxShadow: '0 4px 15px rgba(0,0,0,0.05)',
                    border: '1px solid rgba(0,0,0,0.05)'
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box sx={{
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      bgcolor: 'secondary.main',
                      mx: 0.5,
                      animation: 'pulse 1.5s infinite'
                    }} />
                    <Box sx={{
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      bgcolor: 'secondary.main',
                      mx: 0.5,
                      animation: 'pulse 1.5s infinite',
                      animationDelay: '0.2s'
                    }} />
                    <Box sx={{
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      bgcolor: 'secondary.main',
                      mx: 0.5,
                      animation: 'pulse 1.5s infinite',
                      animationDelay: '0.4s'
                    }} />
                  </Box>
                </Paper>
              </Box>
            </motion.div>
          )}

          {/* مرجع للتمرير التلقائي إلى آخر رسالة */}
          <div ref={messagesEndRef} />
        </Box>

        {/* Suggestions */}
        {messages.length === 1 && (
          <motion.div
            initial="hidden"
            animate="visible"
            transition={{ duration: 0.5, delay: 0.3, staggerChildren: 0.1 }}
            variants={fadeIn}
          >
            <Box sx={{
              p: 3,
              borderTop: '1px solid rgba(0,0,0,0.05)',
              bgcolor: 'rgba(255,255,255,0.8)',
              backdropFilter: 'blur(10px)'
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <LightbulbIcon sx={{ color: 'warning.main', mr: 1 }} />
                <Typography variant="subtitle1" fontWeight="medium">
                  {t('chat.suggestions')}:
                </Typography>
              </Box>
              <Grid container spacing={2}>
                {suggestions.map((suggestion, index) => (
                  <Grid item xs={12} sm={6} md={4} key={suggestion.id}>
                    <motion.div
                      custom={index}
                      variants={suggestionChip}
                      whileHover="hover"
                    >
                      <Paper
                        elevation={0}
                        onClick={() => handleSuggestionClick(suggestion.text)}
                        sx={{
                          p: 2,
                          borderRadius: 3,
                          cursor: 'pointer',
                          border: '1px solid rgba(37, 99, 235, 0.1)',
                          bgcolor: 'white',
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            boxShadow: '0 8px 20px rgba(37, 99, 235, 0.15)',
                            transform: 'translateY(-4px)',
                            borderColor: 'primary.main'
                          }
                        }}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography
                            variant="h6"
                            sx={{
                              fontSize: '1.5rem',
                              mr: 1.5,
                              filter: 'grayscale(0.2)'
                            }}
                          >
                            {suggestion.icon}
                          </Typography>
                          <Typography
                            variant="body1"
                            fontWeight="medium"
                            color="text.primary"
                            sx={{ flexGrow: 1 }}
                          >
                            {suggestion.text}
                          </Typography>
                        </Box>
                      </Paper>
                    </motion.div>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </motion.div>
        )}

        {/* Quick Actions */}
        {messages.length > 1 && (
          <Box sx={{
            px: 3,
            py: 2,
            borderTop: '1px solid rgba(0,0,0,0.05)',
            bgcolor: 'rgba(248, 250, 252, 0.8)',
            backdropFilter: 'blur(10px)'
          }}>
            <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1.5 }}>
              إجراءات سريعة:
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Chip
                label="📋 إنشاء تقرير"
                onClick={() => setInput('أريد إنشاء تقرير نشاط طلابي')}
                clickable
                size="small"
                sx={{
                  bgcolor: 'white',
                  border: '1px solid rgba(37, 99, 235, 0.2)',
                  '&:hover': { bgcolor: 'rgba(37, 99, 235, 0.05)' }
                }}
              />
              <Chip
                label="📁 ملف إنجاز"
                onClick={() => setInput('أريد إعداد ملف إنجاز معلم')}
                clickable
                size="small"
                sx={{
                  bgcolor: 'white',
                  border: '1px solid rgba(37, 99, 235, 0.2)',
                  '&:hover': { bgcolor: 'rgba(37, 99, 235, 0.05)' }
                }}
              />
              <Chip
                label="📚 خطة درس"
                onClick={() => setInput('أريد إعداد خطة درس')}
                clickable
                size="small"
                sx={{
                  bgcolor: 'white',
                  border: '1px solid rgba(37, 99, 235, 0.2)',
                  '&:hover': { bgcolor: 'rgba(37, 99, 235, 0.05)' }
                }}
              />
              <Chip
                label="📝 اختبار"
                onClick={() => setInput('أريد إعداد اختبار تقييمي')}
                clickable
                size="small"
                sx={{
                  bgcolor: 'white',
                  border: '1px solid rgba(37, 99, 235, 0.2)',
                  '&:hover': { bgcolor: 'rgba(37, 99, 235, 0.05)' }
                }}
              />
            </Box>
          </Box>
        )}

        {/* Input Area */}
        <Box sx={{
          p: 3,
          borderTop: '1px solid rgba(0,0,0,0.05)',
          bgcolor: 'white',
          position: 'relative',
          zIndex: 2,
          boxShadow: '0 -4px 20px rgba(0,0,0,0.03)'
        }}>
          <Grid container spacing={2} alignItems="flex-end">
            <Grid item xs>
              <TextField
                fullWidth
                placeholder="اكتب رسالتك هنا... (مثال: أريد إنشاء تقرير نشاط طلابي)"
                variant="outlined"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyPress={handleKeyPress}
                multiline
                maxRows={4}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    backgroundColor: '#f8fafc',
                    minHeight: '56px',
                    '&.Mui-focused': {
                      boxShadow: '0 0 0 3px rgba(37, 99, 235, 0.1)'
                    },
                    '&:hover fieldset': {
                      borderColor: 'primary.light',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: 'primary.main',
                      borderWidth: 2
                    }
                  }
                }}
              />
            </Grid>
            <Grid item>
              <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }}>
                <Button
                  variant="contained"
                  color="primary"
                  endIcon={<SendIcon />}
                  onClick={handleSendMessage}
                  sx={{
                    height: '56px',
                    borderRadius: 3,
                    px: 3,
                    py: 1.5,
                    boxShadow: '0 4px 14px rgba(37, 99, 235, 0.3)',
                    background: 'linear-gradient(90deg, #2563eb, #0ea5e9)',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                      boxShadow: '0 6px 20px rgba(37, 99, 235, 0.35)',
                      background: 'linear-gradient(90deg, #1d4ed8, #0284c7)'
                    },
                    '&:disabled': {
                      background: 'rgba(0,0,0,0.12)',
                      color: 'rgba(0,0,0,0.26)'
                    },
                    transition: 'transform 0.2s, box-shadow 0.2s'
                  }}
                  disabled={input.trim() === ''}
                >
                  إرسال
                </Button>
              </motion.div>
            </Grid>
          </Grid>
        </Box>
      </Paper>
    </Container>
  );
};

export default ChatPage;
