import { useState, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import SendIcon from '@mui/icons-material/Send';
import Chip from '@mui/material/Chip';
import Avatar from '@mui/material/Avatar';
import Grid from '@mui/material/Grid';
import SmartToyIcon from '@mui/icons-material/SmartToy';
import PersonIcon from '@mui/icons-material/Person';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';

interface Message {
  id: number;
  text: string;
  sender: 'user' | 'ai';
  timestamp: Date;
}

const ChatPage = () => {
  const { t } = useTranslation();
  const [input, setInput] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [isTyping, setIsTyping] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    {
      id: 1,
      text: 'مرحباً! أنا مساعدك الذكي. كيف يمكنني مساعدتك اليوم؟ يمكنني إنشاء تقارير أنشطة، ملفات إنجاز، أو أي مستندات تعليمية أخرى.',
      sender: 'ai',
      timestamp: new Date(),
    },
  ]);

  // تمرير تلقائي إلى آخر رسالة
  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // تمرير إلى أعلى الصفحة عند تحميلها
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = () => {
    if (input.trim() === '') return;

    // Add user message
    const userMessage: Message = {
      id: messages.length + 1,
      text: input,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages([...messages, userMessage]);
    setInput('');

    // إظهار مؤشر الكتابة
    setIsTyping(true);

    // Simulate AI response
    setTimeout(() => {
      setIsTyping(false);
      const aiMessage: Message = {
        id: messages.length + 2,
        text: 'شكراً لتواصلك معي. يمكنني مساعدتك في إنشاء هذا المستند. هل ترغب في استخدام أحد القوالب الجاهزة أم تفضل إنشاء مستند مخصص؟',
        sender: 'ai',
        timestamp: new Date(),
      };
      setMessages(prevMessages => [...prevMessages, aiMessage]);
    }, 1500);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const suggestions = [
    { id: 1, text: t('chat.suggestion.report') },
    { id: 2, text: t('chat.suggestion.portfolio') },
    { id: 3, text: t('chat.suggestion.lesson') },
  ];

  const handleSuggestionClick = (suggestion: string) => {
    setInput(suggestion);
  };

  return (
    <Container maxWidth="lg">
      {/* زر الرجوع */}
      <Box sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
        <Button
          component={Link}
          to="/"
          startIcon={<ArrowBackIcon />}
          sx={{
            color: 'text.secondary',
            '&:hover': {
              bgcolor: 'rgba(0,0,0,0.04)',
              color: 'primary.main',
            },
            transition: 'all 0.2s',
            fontWeight: 500,
          }}
        >
          العودة إلى الرئيسية
        </Button>
      </Box>

      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <AutoAwesomeIcon sx={{ mr: 1, color: 'primary.main' }} />
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold" sx={{ mt: 1 }}>
          {t('nav.chat')}
        </Typography>
      </Box>

      <Paper
        elevation={3}
        sx={{
          height: 'calc(100vh - 200px)',
          display: 'flex',
          flexDirection: 'column',
          borderRadius: 3,
          overflow: 'hidden',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          border: '1px solid rgba(0,0,0,0.05)',
          mb: 4
        }}
      >
        {/* Chat Messages */}
        <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2, bgcolor: '#f9fafc' }}>
          {messages.map((message) => (
            <Box
              key={message.id}
              sx={{
                display: 'flex',
                justifyContent: message.sender === 'user' ? 'flex-start' : 'flex-start',
                mb: 2,
              }}
            >
              <Avatar
                sx={{
                  bgcolor: message.sender === 'user' ? 'primary.main' : 'secondary.main',
                  mr: message.sender === 'user' ? 1 : 0,
                  ml: message.sender === 'ai' ? 1 : 0,
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
                }}
              >
                {message.sender === 'user' ? <PersonIcon /> : <SmartToyIcon />}
              </Avatar>
              <Paper
                variant="outlined"
                sx={{
                  p: 2,
                  ml: message.sender === 'user' ? 1 : 0,
                  mr: message.sender === 'ai' ? 1 : 0,
                  backgroundColor: message.sender === 'user' ? 'primary.light' : 'white',
                  maxWidth: '75%',
                  borderRadius: 2,
                  boxShadow: '0 2px 10px rgba(0,0,0,0.05)',
                  border: message.sender === 'ai' ? '1px solid rgba(0,0,0,0.08)' : 'none'
                }}
              >
                <Typography variant="body1">{message.text}</Typography>
                <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
                  {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                </Typography>
              </Paper>
            </Box>
          ))}

          {/* مؤشر الكتابة */}
          {isTyping && (
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Avatar sx={{ bgcolor: 'secondary.main', ml: 1, boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}>
                <SmartToyIcon />
              </Avatar>
              <Paper
                variant="outlined"
                sx={{
                  p: 2,
                  mr: 1,
                  backgroundColor: 'white',
                  borderRadius: 2,
                  boxShadow: '0 2px 10px rgba(0,0,0,0.05)',
                  border: '1px solid rgba(0,0,0,0.08)'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    bgcolor: 'secondary.main',
                    mx: 0.5,
                    animation: 'pulse 1.5s infinite'
                  }} />
                  <Box sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    bgcolor: 'secondary.main',
                    mx: 0.5,
                    animation: 'pulse 1.5s infinite',
                    animationDelay: '0.2s'
                  }} />
                  <Box sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    bgcolor: 'secondary.main',
                    mx: 0.5,
                    animation: 'pulse 1.5s infinite',
                    animationDelay: '0.4s'
                  }} />
                </Box>
              </Paper>
            </Box>
          )}

          {/* مرجع للتمرير التلقائي إلى آخر رسالة */}
          <div ref={messagesEndRef} />
        </Box>

        {/* Suggestions */}
        {messages.length === 1 && (
          <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider', bgcolor: 'white' }}>
            <Typography variant="subtitle2" gutterBottom fontWeight="medium">
              {t('chat.suggestions')}:
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {suggestions.map((suggestion) => (
                <Chip
                  key={suggestion.id}
                  label={suggestion.text}
                  onClick={() => handleSuggestionClick(suggestion.text)}
                  clickable
                  sx={{
                    borderRadius: '16px',
                    py: 0.5,
                    fontWeight: 500,
                    boxShadow: '0 2px 5px rgba(0,0,0,0.08)',
                    '&:hover': {
                      boxShadow: '0 3px 8px rgba(0,0,0,0.12)',
                      transform: 'scale(1.05)'
                    },
                    transition: 'transform 0.2s, box-shadow 0.2s'
                  }}
                  color="primary"
                  variant="outlined"
                />
              ))}
            </Box>
          </Box>
        )}

        {/* Input Area */}
        <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider', bgcolor: 'white' }}>
          <Grid container spacing={1}>
            <Grid item xs>
              <TextField
                fullWidth
                placeholder={t('chat.placeholder')}
                variant="outlined"
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyPress={handleKeyPress}
                multiline
                maxRows={4}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&.Mui-focused fieldset': {
                      borderColor: 'primary.main',
                      borderWidth: 2
                    }
                  }
                }}
              />
            </Grid>
            <Grid item>
              <Button
                variant="contained"
                color="primary"
                endIcon={<SendIcon />}
                onClick={handleSendMessage}
                sx={{
                  height: '100%',
                  borderRadius: 2,
                  px: 3,
                  boxShadow: '0 4px 10px rgba(37, 99, 235, 0.2)',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: '0 6px 15px rgba(37, 99, 235, 0.25)',
                  },
                  transition: 'transform 0.2s, box-shadow 0.2s'
                }}
                disabled={input.trim() === ''}
              >
                {t('chat.send')}
              </Button>
            </Grid>
          </Grid>
        </Box>
      </Paper>
    </Container>
  );
};

export default ChatPage;
