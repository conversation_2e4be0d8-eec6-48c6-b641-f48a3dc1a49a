import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Link, useNavigate, useParams } from 'react-router-dom';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardMedia from '@mui/material/CardMedia';
import CardActions from '@mui/material/CardActions';
import Button from '@mui/material/Button';
import Paper from '@mui/material/Paper';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import AddPhotoAlternateIcon from '@mui/icons-material/AddPhotoAlternate';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import PeopleIcon from '@mui/icons-material/People';
import GpsFixedIcon from '@mui/icons-material/GpsFixed';
import DescriptionIcon from '@mui/icons-material/Description';
import PersonIcon from '@mui/icons-material/Person';
import SupervisorAccountIcon from '@mui/icons-material/SupervisorAccount';
import ImageIcon from '@mui/icons-material/Image';

// تعريف أنواع النماذج المختلفة
interface TemplateModel {
  id: number;
  name: string;
  imageCount: number;
  color: string;
  previewImage: string;
}

// تعريف القالب
interface Template {
  id: number;
  title: string;
  description: string;
  models: TemplateModel[];
}

// بيانات النماذج المختلفة للقوالب
const templateModels: TemplateModel[] = [
  {
    id: 1,
    name: 'نموذج 1',
    imageCount: 1,
    color: 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)',
    previewImage: '/images/templates/template-1.svg'
  },
  {
    id: 2,
    name: 'نموذج 2',
    imageCount: 2,
    color: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
    previewImage: '/images/templates/template-2.svg'
  },
  {
    id: 3,
    name: 'نموذج 3',
    imageCount: 3,
    color: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
    previewImage: '/images/templates/template-3.svg'
  },
  {
    id: 4,
    name: 'نموذج 4',
    imageCount: 4,
    color: 'linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%)',
    previewImage: '/images/templates/template-1.svg'
  },
  {
    id: 5,
    name: 'نموذج 5',
    imageCount: 5,
    color: 'linear-gradient(135deg, #ec4899 0%, #be185d 100%)',
    previewImage: '/images/templates/template-2.svg'
  },
  {
    id: 6,
    name: 'نموذج 6',
    imageCount: 6,
    color: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',
    previewImage: '/images/templates/template-3.svg'
  },
];

// تعريف نماذج مخصصة لكل قالب
const studentActivityModels = templateModels.map(model => ({
  ...model,
  imageCount: model.id // عدد الصور يتطابق مع رقم النموذج
}));

const teacherPortfolioModels = templateModels.map(model => ({
  ...model,
  imageCount: model.id + 1 // عدد الصور مختلف لملف إنجاز المعلم
}));

const lessonPlanModels = templateModels.map(model => ({
  ...model,
  imageCount: Math.min(model.id, 4) // عدد الصور محدود لخطط الدروس
}));

// بيانات القوالب
const templates: Template[] = [
  {
    id: 1,
    title: 'تقرير نشاط طلابي',
    description: 'قالب احترافي لتوثيق الأنشطة الطلابية بتنسيق جذاب',
    models: studentActivityModels
  },
  {
    id: 2,
    title: 'ملف إنجاز معلم',
    description: 'قالب شامل لتوثيق إنجازات المعلم خلال العام الدراسي',
    models: teacherPortfolioModels
  },
  {
    id: 3,
    title: 'خطة درس مهارات القيادة',
    description: 'قالب منظم لإعداد خطط الدروس اليومية والأسبوعية',
    models: lessonPlanModels
  },
];

// مكون عرض نموذج القالب
const TemplateModelCard = ({ model, onSelect }: { model: TemplateModel, onSelect: () => void }) => {
  // استخراج اللون الرئيسي من التدرج اللوني
  const mainColor = model.color.split(' ')[0].replace('linear-gradient(135deg,', '').trim();

  return (
    <Card
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        transition: 'transform 0.3s, box-shadow 0.3s',
        '&:hover': {
          transform: 'translateY(-8px)',
          boxShadow: '0 15px 25px rgba(0,0,0,0.15)',
        },
        cursor: 'pointer',
        overflow: 'hidden',
        borderRadius: 4,
        border: '1px solid rgba(0,0,0,0.05)',
      }}
      onClick={onSelect}
    >
      <Box
        sx={{
          background: model.color,
          color: 'white',
          textAlign: 'center',
          p: 2.5,
          fontWeight: 'bold',
          fontSize: '1.3rem',
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)',
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            background: 'linear-gradient(to right, rgba(255,255,255,0.1), transparent)',
            transform: 'translateX(-100%)',
            transition: 'transform 0.5s ease',
          },
          '&:hover::before': {
            transform: 'translateX(100%)',
          }
        }}
      >
        {model.name}
      </Box>

      {/* عرض مثال للنموذج بالتصميم الجديد */}
      <Box sx={{ p: 3, bgcolor: 'white' }}>
        <Grid container spacing={2}>
          {/* الجانب الأيسر - مساحة الصورة */}
          <Grid item xs={5}>
            <Box sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center'
            }}>
              <Paper
                elevation={0}
                sx={{
                  height: 160,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: '2px dashed rgba(0,0,0,0.1)',
                  borderRadius: 2,
                  p: 1.5,
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    borderColor: mainColor,
                    bgcolor: 'rgba(0,0,0,0.02)'
                  }
                }}
              >
                <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.8rem', mb: 1.5, fontWeight: 500 }}>
                  الصورة
                </Typography>
                <AddPhotoAlternateIcon sx={{ fontSize: 36, color: 'text.secondary', opacity: 0.7 }} />
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, fontSize: '0.7rem', textAlign: 'center' }}>
                  {model.imageCount} {model.imageCount === 1 ? 'صورة' : 'صور'}
                </Typography>
              </Paper>
            </Box>
          </Grid>

          {/* الجانب الأيمن - الحقول */}
          <Grid item xs={7}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
              {/* اليوم والتاريخ */}
              <Box sx={{
                p: 1,
                borderRadius: 1,
                bgcolor: mainColor,
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '0.7rem',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
              }}>
                <CalendarTodayIcon sx={{ mr: 0.5, fontSize: '0.9rem' }} />
                <Typography variant="caption">اليوم والتاريخ</Typography>
              </Box>

              {/* عدد المشاركين */}
              <Box sx={{
                p: 1,
                borderRadius: 1,
                bgcolor: mainColor,
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '0.7rem',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
              }}>
                <PeopleIcon sx={{ mr: 0.5, fontSize: '0.9rem' }} />
                <Typography variant="caption">عدد المشاركين</Typography>
              </Box>

              {/* المنفذ */}
              <Box sx={{
                p: 1,
                borderRadius: 1,
                bgcolor: mainColor,
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '0.7rem',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
              }}>
                <PersonIcon sx={{ mr: 0.5, fontSize: '0.9rem' }} />
                <Typography variant="caption">المنفذ</Typography>
              </Box>

              {/* مشرف التنفيذ */}
              <Box sx={{
                p: 1,
                borderRadius: 1,
                bgcolor: mainColor,
                color: 'white',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '0.7rem',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
              }}>
                <SupervisorAccountIcon sx={{ mr: 0.5, fontSize: '0.9rem' }} />
                <Typography variant="caption">مشرف التنفيذ</Typography>
              </Box>
            </Box>
          </Grid>
        </Grid>

        {/* الهدف والوصف */}
        <Box sx={{ mt: 2 }}>
          <Box sx={{
            p: 1,
            borderRadius: 1,
            bgcolor: mainColor,
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            mb: 1,
            fontSize: '0.7rem',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}>
            <GpsFixedIcon sx={{ mr: 0.5, fontSize: '0.9rem' }} />
            <Typography variant="caption">الهدف</Typography>
          </Box>

          <Box sx={{
            p: 1,
            borderRadius: 1,
            bgcolor: mainColor,
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '0.7rem',
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
          }}>
            <DescriptionIcon sx={{ mr: 0.5, fontSize: '0.9rem' }} />
            <Typography variant="caption">الوصف</Typography>
          </Box>
        </Box>
      </Box>

      <Box sx={{
        p: 2,
        textAlign: 'center',
        color: 'white',
        borderTop: '1px solid rgba(0,0,0,0.05)',
        fontSize: '0.9rem',
        fontWeight: 600,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 1,
        background: model.color,
        borderBottomLeftRadius: 10,
        borderBottomRightRadius: 10
      }}>
        <Button 
          variant="contained" 
          size="small" 
          sx={{ 
            bgcolor: 'rgba(255,255,255,0.2)', 
            color: 'white',
            '&:hover': {
              bgcolor: 'rgba(255,255,255,0.3)'
            },
            fontSize: '0.8rem',
            fontWeight: 600,
            px: 2
          }}
        >
          اختيار النموذج
        </Button>
      </Box>
    </Card>
  );
};

const TemplateSelectorPage = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [template, setTemplate] = useState<Template | null>(null);

  useEffect(() => {
    // العثور على القالب المحدد
    const selectedTemplate = templates.find(t => t.id === Number(id));
    if (selectedTemplate) {
      setTemplate(selectedTemplate);
    }
  }, [id]);

  const handleModelSelect = (modelId: number) => {
    navigate(`/editor/${id}?model=${modelId}`);
  };

  if (!template) {
    return (
      <Container maxWidth="lg">
        <Typography variant="h5" align="center" sx={{ mt: 8 }}>
          جاري تحميل القالب...
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
        <Button
          component={Link}
          to="/templates"
          startIcon={<ArrowBackIcon />}
          sx={{
            color: 'text.secondary',
            '&:hover': {
              bgcolor: 'rgba(0,0,0,0.04)',
              color: 'primary.main',
            },
            transition: 'all 0.2s',
            fontWeight: 500,
          }}
        >
          العودة إلى القوالب
        </Button>
      </Box>

      <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
        {template.title}
      </Typography>

      <Typography variant="subtitle1" color="text.secondary" paragraph>
        {template.description}
      </Typography>

      <Typography variant="h5" gutterBottom sx={{ mt: 4, mb: 3, fontWeight: 'bold' }}>
        اختر القالب المناسب
      </Typography>

      <Grid container spacing={3}>
        {template.models.map((model) => (
          <Grid item xs={12} sm={6} md={4} key={model.id}>
            <TemplateModelCard model={model} onSelect={() => handleModelSelect(model.id)} />
          </Grid>
        ))}
      </Grid>
    </Container>
  );
};

export default TemplateSelectorPage;