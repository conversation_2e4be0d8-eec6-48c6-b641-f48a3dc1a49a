import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardMedia from '@mui/material/CardMedia';
import Paper from '@mui/material/Paper';
import Divider from '@mui/material/Divider';
import Avatar from '@mui/material/Avatar';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import ChatIcon from '@mui/icons-material/Chat';
import DescriptionIcon from '@mui/icons-material/Description';
import ShareIcon from '@mui/icons-material/Share';
import SchoolIcon from '@mui/icons-material/School';
import GradingIcon from '@mui/icons-material/Grading';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import SpeedIcon from '@mui/icons-material/Speed';
import SecurityIcon from '@mui/icons-material/Security';
import EmojiObjectsIcon from '@mui/icons-material/EmojiObjects';
import FormatQuoteIcon from '@mui/icons-material/FormatQuote';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { motion } from 'framer-motion';
import { useEffect, useRef } from 'react';
import AnimatedSection from '../components/AnimatedSection';
import { fadeInUp, fadeInRight, scaleUp, fadeIn, staggerContainer } from '../styles/animations';

const HomePage = () => {
  const { t } = useTranslation();
  const featuresRef = useRef<HTMLDivElement>(null);
  const templatesRef = useRef<HTMLDivElement>(null);

  // تمرير إلى أعلى الصفحة عند تحميلها
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // التمرير إلى القسم المحدد
  const scrollToSection = (ref: React.RefObject<HTMLDivElement>) => {
    if (ref.current) {
      ref.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const features = [
    {
      icon: <ChatIcon fontSize="large" color="primary" />,
      image: '/images/features/ai-chat.svg',
      title: 'المحادثة الذكية',
      description: 'تحدث مع الذكاء الاصطناعي بلغة طبيعية لإنشاء المحتوى التعليمي المناسب لاحتياجاتك'
    },
    {
      icon: <DescriptionIcon fontSize="large" color="primary" />,
      image: '/images/features/templates.svg',
      title: 'قوالب احترافية',
      description: 'اختر من مكتبة متنوعة من القوالب الاحترافية المصممة خصيصاً للمعلمين'
    },
    {
      icon: <ShareIcon fontSize="large" color="primary" />,
      image: '/images/features/export.svg',
      title: 'تصدير ومشاركة',
      description: 'صدّر مستنداتك بصيغ متعددة وشاركها بسهولة مع زملائك'
    }
  ];

  const benefits = [
    {
      icon: <SpeedIcon fontSize="large" sx={{ color: '#3b82f6' }} />,
      title: 'توفير الوقت والجهد',
      description: 'إنشاء المستندات التعليمية في دقائق بدلاً من ساعات'
    },
    {
      icon: <AutoAwesomeIcon fontSize="large" sx={{ color: '#f59e0b' }} />,
      title: 'محتوى احترافي',
      description: 'تصميمات عالية الجودة تعكس احترافية المعلم'
    },
    {
      icon: <EmojiObjectsIcon fontSize="large" sx={{ color: '#10b981' }} />,
      title: 'أفكار إبداعية',
      description: 'اقتراحات ذكية لتطوير المحتوى التعليمي'
    },
    {
      icon: <SecurityIcon fontSize="large" sx={{ color: '#8b5cf6' }} />,
      title: 'خصوصية وأمان',
      description: 'حماية كاملة لبياناتك ومستنداتك التعليمية'
    }
  ];

  const testimonials = [
    {
      name: 'أحمد محمد',
      role: 'معلم لغة عربية',
      avatar: '/images/avatars/teacher1.jpg',
      content: 'ساعدني تطبيق معلم برو في إعداد خطط الدروس بشكل احترافي وسريع، مما وفر لي الكثير من الوقت والجهد.'
    },
    {
      name: 'سارة عبدالله',
      role: 'مشرفة تربوية',
      avatar: '/images/avatars/teacher2.jpg',
      content: 'أصبح إعداد التقارير الإشرافية أسهل بكثير مع معلم برو. التطبيق يقدم قوالب احترافية ومتنوعة تناسب جميع الاحتياجات.'
    },
    {
      name: 'محمد العتيبي',
      role: 'قائد مدرسة',
      avatar: '/images/avatars/teacher3.jpg',
      content: 'نوصي جميع المعلمين في مدرستنا باستخدام معلم برو لما له من أثر إيجابي في تطوير العملية التعليمية وتوثيقها.'
    }
  ];

  return (
    <Box component={motion.div} initial="hidden" animate="visible" variants={fadeInUp}>
      {/* Hero Section - Modern & Attractive */}
      <Box
        sx={{
          position: 'relative',
          minHeight: '100vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          overflow: 'hidden',
          pt: { xs: 8, md: 0 },
          background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
        }}
      >
        {/* Background Elements */}
        <Box
          sx={{
            position: 'absolute',
            top: '5%',
            right: '5%',
            width: { xs: 200, md: 300 },
            height: { xs: 200, md: 300 },
            borderRadius: '50%',
            background: 'radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0) 70%)',
            zIndex: 0,
            animation: 'pulse 8s infinite ease-in-out',
            '@keyframes pulse': {
              '0%': { transform: 'scale(1)', opacity: 0.5 },
              '50%': { transform: 'scale(1.1)', opacity: 0.8 },
              '100%': { transform: 'scale(1)', opacity: 0.5 },
            },
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            bottom: '10%',
            left: '5%',
            width: { xs: 150, md: 250 },
            height: { xs: 150, md: 250 },
            borderRadius: '50%',
            background: 'radial-gradient(circle, rgba(59, 130, 246, 0.08) 0%, rgba(59, 130, 246, 0) 70%)',
            zIndex: 0,
            animation: 'float 10s infinite ease-in-out',
            '@keyframes float': {
              '0%': { transform: 'translateY(0)' },
              '50%': { transform: 'translateY(-20px)' },
              '100%': { transform: 'translateY(0)' },
            },
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={6}>
              <motion.div variants={fadeInRight}>
                <Box sx={{ mb: 4 }}>
                  <motion.div
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.2, duration: 0.5 }}
                  >
                    <Box
                      sx={{
                        display: 'inline-flex',
                        alignItems: 'center',
                        background: 'linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%)',
                        px: 2,
                        py: 1,
                        borderRadius: 5,
                        mb: 2
                      }}
                    >
                      <AutoAwesomeIcon sx={{ color: 'primary.main', mr: 1, fontSize: '1rem' }} />
                      <Typography variant="subtitle2" color="primary.main" fontWeight="medium">
                        الحل الأمثل للمعلمين
                      </Typography>
                    </Box>
                  </motion.div>

                  <Typography
                    variant="h2"
                    component="h1"
                    gutterBottom
                    fontWeight="bold"
                    sx={{
                      fontSize: { xs: '2.2rem', sm: '2.8rem', md: '3.5rem' },
                      lineHeight: 1.2,
                      background: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 100%)',
                      backgroundClip: 'text',
                      textFillColor: 'transparent',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      mb: 2
                    }}
                  >
                    {t('app.name')}
                  </Typography>

                  <Typography
                    variant="h5"
                    paragraph
                    sx={{
                      fontSize: { xs: '1.1rem', sm: '1.3rem', md: '1.5rem' },
                      color: 'text.secondary',
                      mb: 4,
                      maxWidth: '90%',
                      lineHeight: 1.5
                    }}
                  >
                    منصة ذكية لإنشاء المستندات التعليمية بسهولة وسرعة باستخدام الذكاء الاصطناعي
                  </Typography>

                  <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ mb: 4 }}>
                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                      <Button
                        component={Link}
                        to="/chat"
                        variant="contained"
                        size="large"
                        sx={{
                          px: 4,
                          py: 1.5,
                          borderRadius: 2,
                          fontSize: '1.1rem',
                          fontWeight: 'bold',
                          boxShadow: '0 10px 25px rgba(59, 130, 246, 0.3)',
                          background: 'linear-gradient(90deg, #3b82f6 0%, #2563eb 100%)',
                          '&:hover': {
                            boxShadow: '0 15px 30px rgba(59, 130, 246, 0.4)',
                          }
                        }}
                      >
                        ابدأ الآن مجاناً
                      </Button>
                    </motion.div>

                    <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                      <Button
                        onClick={() => scrollToSection(featuresRef)}
                        variant="outlined"
                        size="large"
                        endIcon={<KeyboardArrowDownIcon />}
                        sx={{
                          px: 3,
                          py: 1.5,
                          borderRadius: 2,
                          borderWidth: 2,
                          fontSize: '1.1rem',
                          '&:hover': {
                            borderWidth: 2,
                            bgcolor: 'rgba(59, 130, 246, 0.05)'
                          }
                        }}
                      >
                        اكتشف المزيد
                      </Button>
                    </motion.div>
                  </Stack>

                  <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <CheckCircleIcon sx={{ color: 'success.main', mr: 1, fontSize: '1.2rem' }} />
                      <Typography variant="body1" color="text.secondary">سهل الاستخدام</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mx: 2 }}>
                      <CheckCircleIcon sx={{ color: 'success.main', mr: 1, fontSize: '1.2rem' }} />
                      <Typography variant="body1" color="text.secondary">تحديثات مستمرة</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <CheckCircleIcon sx={{ color: 'success.main', mr: 1, fontSize: '1.2rem' }} />
                      <Typography variant="body1" color="text.secondary">دعم فني متميز</Typography>
                    </Box>
                  </Box>
                </Box>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={6}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.3, duration: 0.5 }}
              >
                <Box
                  sx={{
                    position: 'relative',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                  }}
                >
                  {/* App Logo/Icon */}
                  <Box
                    sx={{
                      position: 'absolute',
                      top: { xs: -30, md: -50 },
                      right: { xs: 20, md: 50 },
                      zIndex: 2,
                      transform: 'rotate(15deg)',
                      animation: 'float 5s infinite ease-in-out',
                    }}
                  >
                    <motion.div whileHover={{ rotate: 15, scale: 1.1 }}>
                      <Avatar
                        sx={{
                          width: { xs: 60, md: 80 },
                          height: { xs: 60, md: 80 },
                          bgcolor: 'primary.main',
                          boxShadow: '0 8px 20px rgba(59, 130, 246, 0.3)',
                        }}
                      >
                        <GradingIcon sx={{ fontSize: { xs: 35, md: 45 }, color: 'white' }} />
                      </Avatar>
                    </motion.div>
                  </Box>

                  {/* App Demo/Mockup */}
                  <Box
                    sx={{
                      position: 'relative',
                      width: '100%',
                      height: { xs: 400, md: 500 },
                      borderRadius: 4,
                      overflow: 'hidden',
                      boxShadow: '0 20px 50px rgba(0, 0, 0, 0.15)',
                      border: '1px solid rgba(0, 0, 0, 0.05)',
                      background: 'white',
                    }}
                  >
                    {/* App Header */}
                    <Box
                      sx={{
                        height: 60,
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        px: 3,
                        background: 'linear-gradient(90deg, #3b82f6 0%, #2563eb 100%)',
                      }}
                    >
                      <Typography variant="subtitle1" fontWeight="bold" color="white">
                        معلم برو
                      </Typography>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: '#ff5f57' }} />
                        <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: '#ffbd2e' }} />
                        <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: '#28c941' }} />
                      </Box>
                    </Box>

                    {/* App Content */}
                    <Box sx={{ p: 3, height: 'calc(100% - 60px)', overflow: 'hidden' }}>
                      {/* Chat Interface */}
                      <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                        <Box sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                          <Typography variant="h6" fontWeight="bold" color="primary.main">
                            المحادثة الذكية
                          </Typography>
                          <Chip
                            label="متصل"
                            size="small"
                            sx={{
                              ml: 2,
                              bgcolor: 'success.light',
                              color: 'white',
                              fontSize: '0.7rem'
                            }}
                          />
                        </Box>

                        <Box sx={{ flexGrow: 1, overflow: 'auto', mb: 2 }}>
                          {/* AI Message */}
                          <Box sx={{ display: 'flex', mb: 2, animation: 'fadeIn 0.5s ease-in-out' }}>
                            <Avatar sx={{ bgcolor: 'primary.main', width: 36, height: 36, mr: 1 }}>AI</Avatar>
                            <Paper
                              elevation={0}
                              sx={{
                                bgcolor: 'grey.100',
                                p: 2,
                                borderRadius: '0 15px 15px 15px',
                                maxWidth: '80%'
                              }}
                            >
                              <Typography variant="body2">
                                مرحباً! كيف يمكنني مساعدتك في إعداد المستندات التعليمية اليوم؟
                              </Typography>
                            </Paper>
                          </Box>

                          {/* User Message */}
                          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2, animation: 'fadeIn 0.5s ease-in-out 0.3s both' }}>
                            <Paper
                              elevation={0}
                              sx={{
                                bgcolor: 'primary.light',
                                p: 2,
                                borderRadius: '15px 0 15px 15px',
                                maxWidth: '80%',
                                color: 'white'
                              }}
                            >
                              <Typography variant="body2">
                                أحتاج إلى إعداد خطة درس لمادة اللغة العربية للصف الثالث
                              </Typography>
                            </Paper>
                            <Avatar sx={{ bgcolor: 'grey.300', width: 36, height: 36, ml: 1 }}>أ</Avatar>
                          </Box>

                          {/* AI Response */}
                          <Box sx={{ display: 'flex', mb: 2, animation: 'fadeIn 0.5s ease-in-out 0.6s both' }}>
                            <Avatar sx={{ bgcolor: 'primary.main', width: 36, height: 36, mr: 1 }}>AI</Avatar>
                            <Paper
                              elevation={0}
                              sx={{
                                bgcolor: 'grey.100',
                                p: 2,
                                borderRadius: '0 15px 15px 15px',
                                maxWidth: '80%'
                              }}
                            >
                              <Typography variant="body2">
                                بالتأكيد! سأساعدك في إعداد خطة درس متكاملة لمادة اللغة العربية للصف الثالث. هل تفضل استخدام أحد القوالب الجاهزة أم إنشاء خطة مخصصة؟
                              </Typography>
                            </Paper>
                          </Box>

                          {/* AI Template Suggestions - Animated */}
                          <Box sx={{ display: 'flex', mb: 2, animation: 'fadeIn 0.5s ease-in-out 0.9s both' }}>
                            <Avatar sx={{ bgcolor: 'primary.main', width: 36, height: 36, mr: 1 }}>AI</Avatar>
                            <Box sx={{ maxWidth: '80%' }}>
                              <Paper
                                elevation={0}
                                sx={{
                                  bgcolor: 'grey.100',
                                  p: 2,
                                  borderRadius: '0 15px 15px 15px',
                                  mb: 2
                                }}
                              >
                                <Typography variant="body2">
                                  إليك بعض القوالب المقترحة:
                                </Typography>
                              </Paper>

                              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                                <Chip
                                  label="خطة درس تفاعلية"
                                  color="primary"
                                  variant="outlined"
                                  sx={{ cursor: 'pointer' }}
                                />
                                <Chip
                                  label="خطة درس تقليدية"
                                  color="primary"
                                  variant="outlined"
                                  sx={{ cursor: 'pointer' }}
                                />
                                <Chip
                                  label="خطة مهارات القراءة"
                                  color="primary"
                                  variant="outlined"
                                  sx={{ cursor: 'pointer' }}
                                />
                              </Box>
                            </Box>
                          </Box>
                        </Box>

                        {/* Chat Input */}
                        <Box sx={{ display: 'flex', alignItems: 'center', pt: 1 }}>
                          <Paper
                            elevation={0}
                            sx={{
                              flexGrow: 1,
                              bgcolor: 'grey.100',
                              borderRadius: 3,
                              p: 1.5,
                              mr: 1,
                              color: 'grey.500',
                              fontSize: '0.9rem',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'space-between'
                            }}
                          >
                            <Typography variant="body2" color="text.secondary">
                              اكتب رسالتك هنا...
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                              <Box sx={{ color: 'grey.400', fontSize: '1.2rem', cursor: 'pointer' }}>
                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M12 18C15.3137 18 18 15.3137 18 12C18 8.68629 15.3137 6 12 6C8.68629 6 6 8.68629 6 12C6 15.3137 8.68629 18 12 18Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                  <path d="M12 2V4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                  <path d="M12 20V22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                  <path d="M4.93 4.93L6.34 6.34" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                  <path d="M17.66 17.66L19.07 19.07" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                  <path d="M2 12H4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                  <path d="M20 12H22" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                  <path d="M6.34 17.66L4.93 19.07" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                  <path d="M19.07 4.93L17.66 6.34" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                              </Box>
                              <Box sx={{ color: 'grey.400', fontSize: '1.2rem', cursor: 'pointer' }}>
                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M21 15V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                  <path d="M7 10L12 15L17 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                  <path d="M12 15V3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                </svg>
                              </Box>
                            </Box>
                          </Paper>
                          <Box
                            sx={{
                              width: 40,
                              height: 40,
                              borderRadius: '50%',
                              bgcolor: 'primary.main',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              color: 'white',
                              cursor: 'pointer',
                              transition: 'all 0.2s ease',
                              '&:hover': {
                                bgcolor: 'primary.dark',
                                transform: 'scale(1.05)'
                              }
                            }}
                          >
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M22 2L11 13" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          </Box>
                        </Box>
                      </Box>
                    </Box>
                  </Box>
                </Box>
              </motion.div>
            </Grid>
          </Grid>

          {/* Scroll Down Button */}
          <Box
            sx={{
              position: 'absolute',
              bottom: 30,
              left: '50%',
              transform: 'translateX(-50%)',
              animation: 'bounce 2s infinite ease-in-out',
              '@keyframes bounce': {
                '0%, 100%': { transform: 'translateX(-50%) translateY(0)' },
                '50%': { transform: 'translateX(-50%) translateY(10px)' },
              },
              cursor: 'pointer',
              zIndex: 10
            }}
            onClick={() => scrollToSection(featuresRef)}
          >
            <Box sx={{
              bgcolor: 'white',
              borderRadius: '50%',
              width: 50,
              height: 50,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 4px 10px rgba(0,0,0,0.1)'
            }}>
              <KeyboardArrowDownIcon sx={{ color: 'primary.main', fontSize: 30 }} />
            </Box>
          </Box>
        </Container>
      </Box>

      {/* Features Section */}
      <Box
        ref={featuresRef}
        sx={{
          py: { xs: 8, md: 12 },
          background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: { xs: 6, md: 8 } }}>
            <Box
              sx={{
                display: 'inline-flex',
                alignItems: 'center',
                background: 'linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%)',
                px: 2,
                py: 1,
                borderRadius: 5,
                mb: 2
              }}
            >
              <AutoAwesomeIcon sx={{ color: 'primary.main', mr: 1, fontSize: '1rem' }} />
              <Typography variant="subtitle2" color="primary.main" fontWeight="medium">
                ميزات فريدة
              </Typography>
            </Box>

            <Typography
              variant="h3"
              component="h2"
              align="center"
              gutterBottom
              fontWeight="bold"
              sx={{
                fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
                background: 'linear-gradient(135deg, #1e40af 0%, #3b82f6 100%)',
                backgroundClip: 'text',
                textFillColor: 'transparent',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 2
              }}
            >
              ميزات تطبيق معلم برو
            </Typography>

            <Typography
              variant="h6"
              align="center"
              color="text.secondary"
              paragraph
              sx={{
                mb: 2,
                maxWidth: '700px',
                mx: 'auto',
                fontSize: { xs: '1rem', md: '1.2rem' },
                lineHeight: 1.6
              }}
            >
              أدوات متكاملة لمساعدة المعلمين على إنشاء محتوى تعليمي وإداري احترافي بسهولة وسرعة
            </Typography>
          </Box>

          {/* Main Features */}
          <Grid container spacing={{ xs: 4, md: 6 }} sx={{ mb: 10 }}>
            {features.map((feature, index) => (
              <Grid item xs={12} md={4} key={index}>
                <motion.div
                  whileHover={{ y: -10 }}
                  transition={{ type: 'spring', stiffness: 300 }}
                >
                  <Paper
                    elevation={0}
                    sx={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      borderRadius: 4,
                      boxShadow: '0 10px 30px rgba(0,0,0,0.05)',
                      overflow: 'hidden',
                      transition: 'all 0.3s ease',
                      position: 'relative',
                      border: '1px solid rgba(0,0,0,0.05)',
                      '&:hover': {
                        boxShadow: '0 15px 35px rgba(37, 99, 235, 0.1)',
                        transform: 'translateY(-10px)'
                      }
                    }}
                  >
                    <Box
                      sx={{
                        position: 'relative',
                        height: 200,
                        background: 'linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%)',
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        overflow: 'hidden'
                      }}
                    >
                      <Box
                        sx={{
                          position: 'absolute',
                          top: -20,
                          right: -20,
                          width: 100,
                          height: 100,
                          borderRadius: '50%',
                          background: 'radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0) 70%)',
                          zIndex: 0
                        }}
                      />

                      <motion.div
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        transition={{ type: 'spring', stiffness: 400 }}
                      >
                        <Box
                          component="img"
                          src={feature.image}
                          alt={feature.title}
                          sx={{
                            width: 150,
                            height: 150,
                            objectFit: 'contain',
                            filter: 'drop-shadow(0 8px 15px rgba(37, 99, 235, 0.2))',
                            transition: 'all 0.3s ease',
                            position: 'relative',
                            zIndex: 1
                          }}
                        />
                      </motion.div>
                    </Box>

                    <Box
                      sx={{
                        flexGrow: 1,
                        textAlign: 'center',
                        p: 4
                      }}
                    >
                      <Typography
                        gutterBottom
                        variant="h5"
                        component="h3"
                        fontWeight="bold"
                        sx={{
                          mb: 2,
                          color: 'primary.main'
                        }}
                      >
                        {feature.title}
                      </Typography>

                      <Typography
                        variant="body1"
                        color="text.secondary"
                        sx={{
                          lineHeight: 1.6,
                          mb: 3
                        }}
                      >
                        {feature.description}
                      </Typography>

                      <motion.div
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        <Button
                          variant="outlined"
                          color="primary"
                          size="medium"
                          sx={{
                            borderRadius: 2,
                            px: 3,
                            py: 1,
                            borderWidth: 2,
                            '&:hover': {
                              borderWidth: 2,
                              bgcolor: 'rgba(37, 99, 235, 0.05)'
                            }
                          }}
                        >
                          اكتشف المزيد
                        </Button>
                      </motion.div>
                    </Box>
                  </Paper>
                </motion.div>
              </Grid>
            ))}
          </Grid>

          {/* Benefits Section */}
          <Box sx={{ mt: 10, mb: 6 }}>
            <Typography
              variant="h4"
              component="h3"
              align="center"
              gutterBottom
              fontWeight="bold"
              sx={{
                fontSize: { xs: '1.8rem', sm: '2.2rem', md: '2.5rem' },
                mb: 6
              }}
            >
              لماذا تختار معلم برو؟
            </Typography>

            <Grid container spacing={4}>
              {benefits.map((benefit, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <motion.div
                    whileHover={{ y: -5 }}
                    transition={{ type: 'spring', stiffness: 300 }}
                  >
                    <Box
                      sx={{
                        textAlign: 'center',
                        p: 3,
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center'
                      }}
                    >
                      <Box
                        sx={{
                          width: 70,
                          height: 70,
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mb: 3,
                          background: 'white',
                          boxShadow: '0 10px 25px rgba(0,0,0,0.05)',
                          border: '1px solid rgba(0,0,0,0.03)'
                        }}
                      >
                        {benefit.icon}
                      </Box>

                      <Typography
                        variant="h6"
                        component="h4"
                        fontWeight="bold"
                        sx={{ mb: 2 }}
                      >
                        {benefit.title}
                      </Typography>

                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ lineHeight: 1.6 }}
                      >
                        {benefit.description}
                      </Typography>
                    </Box>
                  </motion.div>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Container>
      </Box>

      {/* Templates Preview Section */}
      <Box
        ref={templatesRef}
        sx={{
          py: { xs: 10, md: 14 },
          background: 'white',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {/* Background Elements */}
        <Box
          sx={{
            position: 'absolute',
            top: '10%',
            left: '5%',
            width: { xs: 200, md: 300 },
            height: { xs: 200, md: 300 },
            borderRadius: '50%',
            background: 'radial-gradient(circle, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0) 70%)',
            zIndex: 0
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            bottom: '5%',
            right: '10%',
            width: { xs: 150, md: 250 },
            height: { xs: 150, md: 250 },
            borderRadius: '50%',
            background: 'radial-gradient(circle, rgba(59, 130, 246, 0.08) 0%, rgba(59, 130, 246, 0) 70%)',
            zIndex: 0
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
          <Box sx={{ textAlign: 'center', mb: { xs: 6, md: 8 } }}>
            <Box
              sx={{
                display: 'inline-flex',
                alignItems: 'center',
                background: 'linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%)',
                px: 2,
                py: 1,
                borderRadius: 5,
                mb: 2
              }}
            >
              <DescriptionIcon sx={{ color: 'primary.main', mr: 1, fontSize: '1rem' }} />
              <Typography variant="subtitle2" color="primary.main" fontWeight="medium">
                قوالب جاهزة
              </Typography>
            </Box>

            <Typography
              variant="h3"
              component="h2"
              align="center"
              gutterBottom
              fontWeight="bold"
              sx={{
                fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
                mb: 2
              }}
            >
              قوالب احترافية متنوعة
            </Typography>

            <Typography
              variant="h6"
              align="center"
              color="text.secondary"
              paragraph
              sx={{
                mb: 2,
                maxWidth: '700px',
                mx: 'auto',
                fontSize: { xs: '1rem', md: '1.2rem' },
                lineHeight: 1.6
              }}
            >
              اختر من مجموعة متنوعة من القوالب المصممة خصيصاً للمعلمين لتوفير الوقت والجهد
            </Typography>
          </Box>

          <Grid container spacing={4} sx={{ mb: 6 }}>
            {[1, 2, 3].map((item) => (
              <Grid item xs={12} md={4} key={item}>
                <motion.div
                  whileHover={{ y: -10 }}
                  transition={{ type: 'spring', stiffness: 300 }}
                >
                  <Paper
                    elevation={0}
                    sx={{
                      borderRadius: 4,
                      overflow: 'hidden',
                      boxShadow: '0 15px 35px rgba(0,0,0,0.08)',
                      transition: 'all 0.3s ease',
                      border: '1px solid rgba(0,0,0,0.05)',
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      '&:hover': {
                        boxShadow: '0 20px 45px rgba(37, 99, 235, 0.12)',
                      }
                    }}
                  >
                    <Box
                      sx={{
                        position: 'relative',
                        height: 220,
                        overflow: 'hidden',
                        bgcolor: item === 1 ? '#e0f2fe' : item === 2 ? '#fef3c7' : '#dcfce7',
                      }}
                    >
                      <Box
                        component="img"
                        src={`/images/templates/template-${item}.svg`}
                        alt={`Template ${item}`}
                        sx={{
                          width: '100%',
                          height: '100%',
                          objectFit: 'cover',
                          transition: 'transform 0.5s ease',
                          '&:hover': {
                            transform: 'scale(1.05)'
                          }
                        }}
                      />

                      <Box
                        sx={{
                          position: 'absolute',
                          top: 16,
                          right: 16,
                          bgcolor: 'white',
                          color: 'primary.main',
                          borderRadius: 5,
                          px: 2,
                          py: 0.5,
                          fontSize: '0.75rem',
                          fontWeight: 'bold',
                          boxShadow: '0 4px 10px rgba(0,0,0,0.05)',
                          zIndex: 1
                        }}
                      >
                        {item === 1 ? 'الأكثر استخداماً' : item === 2 ? 'جديد' : 'موصى به'}
                      </Box>
                    </Box>

                    <Box sx={{ p: 4, flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
                      <Typography
                        variant="h5"
                        component="h3"
                        gutterBottom
                        fontWeight="bold"
                        sx={{ mb: 1 }}
                      >
                        {item === 1 ? 'تقرير نشاط طلابي' : item === 2 ? 'ملف إنجاز معلم' : 'خطة درس'}
                      </Typography>

                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{ mb: 3, flexGrow: 1 }}
                      >
                        {item === 1
                          ? 'قالب احترافي لتوثيق الأنشطة الطلابية بطريقة منظمة وجذابة'
                          : item === 2
                            ? 'قالب شامل لتوثيق إنجازات المعلم وخبراته التعليمية'
                            : 'قالب متكامل لإعداد خطط الدروس بطريقة منظمة وفعالة'
                        }
                      </Typography>

                      <Box sx={{ display: 'flex', gap: 2 }}>
                        <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }} style={{ flexGrow: 1 }}>
                          <Button
                            component={Link}
                            to={`/template-selector/${item}`}
                            variant="contained"
                            fullWidth
                            sx={{
                              py: 1.2,
                              borderRadius: 2,
                              boxShadow: '0 4px 10px rgba(37, 99, 235, 0.2)',
                            }}
                          >
                            استخدام القالب
                          </Button>
                        </motion.div>

                        <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }}>
                          <Button
                            component={Link}
                            to={`/templates/${item}`}
                            variant="outlined"
                            sx={{
                              py: 1.2,
                              borderRadius: 2,
                              minWidth: 0,
                              width: 50,
                              borderWidth: 2,
                              '&:hover': {
                                borderWidth: 2
                              }
                            }}
                          >
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M15 3H21V9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              <path d="M10 14L21 3" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                              <path d="M18 13V19C18 20.1046 17.1046 21 16 21H5C3.89543 21 3 20.1046 3 19V8C3 6.89543 3.89543 6 5 6H11" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                            </svg>
                          </Button>
                        </motion.div>
                      </Box>
                    </Box>
                  </Paper>
                </motion.div>
              </Grid>
            ))}
          </Grid>

          <Box sx={{ textAlign: 'center', mt: 6 }}>
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                component={Link}
                to="/templates"
                variant="outlined"
                size="large"
                sx={{
                  px: 5,
                  py: 1.5,
                  borderRadius: 2,
                  borderWidth: 2,
                  fontSize: '1.1rem',
                  '&:hover': {
                    borderWidth: 2,
                    bgcolor: 'rgba(37, 99, 235, 0.05)'
                  }
                }}
              >
                عرض جميع القوالب
              </Button>
            </motion.div>
          </Box>
        </Container>
      </Box>

      {/* Testimonials Section */}
      <Box
        sx={{
          py: { xs: 10, md: 14 },
          background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: { xs: 6, md: 8 } }}>
            <Box
              sx={{
                display: 'inline-flex',
                alignItems: 'center',
                background: 'linear-gradient(90deg, rgba(59, 130, 246, 0.1) 0%, rgba(59, 130, 246, 0.05) 100%)',
                px: 2,
                py: 1,
                borderRadius: 5,
                mb: 2
              }}
            >
              <FormatQuoteIcon sx={{ color: 'primary.main', mr: 1, fontSize: '1rem' }} />
              <Typography variant="subtitle2" color="primary.main" fontWeight="medium">
                آراء المستخدمين
              </Typography>
            </Box>

            <Typography
              variant="h3"
              component="h2"
              align="center"
              gutterBottom
              fontWeight="bold"
              sx={{
                fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
                mb: 2
              }}
            >
              ماذا يقول المعلمون عنا
            </Typography>

            <Typography
              variant="h6"
              align="center"
              color="text.secondary"
              paragraph
              sx={{
                mb: 2,
                maxWidth: '700px',
                mx: 'auto',
                fontSize: { xs: '1rem', md: '1.2rem' },
                lineHeight: 1.6
              }}
            >
              تجارب حقيقية من معلمين استفادوا من منصة معلم برو في تطوير أعمالهم
            </Typography>
          </Box>

          <Grid container spacing={4}>
            {testimonials.map((testimonial, index) => (
              <Grid item xs={12} md={4} key={index}>
                <motion.div
                  whileHover={{ y: -10 }}
                  transition={{ type: 'spring', stiffness: 300 }}
                >
                  <Paper
                    elevation={0}
                    sx={{
                      p: 4,
                      borderRadius: 4,
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      position: 'relative',
                      boxShadow: '0 10px 30px rgba(0,0,0,0.05)',
                      border: '1px solid rgba(0,0,0,0.05)',
                      '&:hover': {
                        boxShadow: '0 15px 35px rgba(37, 99, 235, 0.1)',
                      }
                    }}
                  >
                    <Box sx={{ position: 'absolute', top: 20, right: 20, color: 'primary.light', fontSize: 40 }}>
                      <FormatQuoteIcon fontSize="inherit" />
                    </Box>

                    <Box sx={{ flexGrow: 1, pt: 3 }}>
                      <Typography
                        variant="body1"
                        color="text.secondary"
                        paragraph
                        sx={{ mb: 4, position: 'relative', zIndex: 1, lineHeight: 1.8 }}
                      >
                        "{testimonial.content}"
                      </Typography>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                      <Avatar
                        src={testimonial.avatar}
                        alt={testimonial.name}
                        sx={{ width: 56, height: 56, mr: 2, border: '2px solid white', boxShadow: '0 4px 10px rgba(0,0,0,0.1)' }}
                      />
                      <Box>
                        <Typography variant="subtitle1" fontWeight="bold">
                          {testimonial.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {testimonial.role}
                        </Typography>
                      </Box>
                    </Box>
                  </Paper>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* CTA Section */}
      <Box
        sx={{
          py: { xs: 10, sm: 12, md: 16 },
          background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
          position: 'relative',
          overflow: 'hidden',
          boxShadow: '0 20px 50px rgba(37, 99, 235, 0.3)'
        }}
      >
        {/* Decorative Elements */}
        <Box
          sx={{
            position: 'absolute',
            top: -50,
            right: -50,
            width: 200,
            height: 200,
            borderRadius: '50%',
            background: 'radial-gradient(circle, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 70%)',
            animation: 'pulse 6s infinite ease-in-out',
            '@keyframes pulse': {
              '0%': { transform: 'scale(1)', opacity: 0.5 },
              '50%': { transform: 'scale(1.1)', opacity: 0.8 },
              '100%': { transform: 'scale(1)', opacity: 0.5 },
            },
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            bottom: -70,
            left: -70,
            width: 250,
            height: 250,
            borderRadius: '50%',
            background: 'radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%)',
            animation: 'float 8s infinite ease-in-out',
            '@keyframes float': {
              '0%': { transform: 'translateY(0)' },
              '50%': { transform: 'translateY(-15px)' },
              '100%': { transform: 'translateY(0)' },
            },
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
          <Grid container spacing={6} alignItems="center">
            <Grid item xs={12} md={7}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5 }}
              >
                <Box>
                  <Typography
                    variant="h2"
                    component="h2"
                    gutterBottom
                    fontWeight="bold"
                    color="white"
                    sx={{
                      fontSize: { xs: '2rem', sm: '2.5rem', md: '3.5rem' },
                      textShadow: '0 2px 15px rgba(0,0,0,0.2)',
                      mb: 3,
                      lineHeight: 1.2
                    }}
                  >
                    ابدأ استخدام معلم برو اليوم
                  </Typography>

                  <Typography
                    variant="h6"
                    color="white"
                    paragraph
                    sx={{
                      mb: 5,
                      opacity: 0.9,
                      fontSize: { xs: '1rem', sm: '1.1rem', md: '1.3rem' },
                      maxWidth: '700px',
                      lineHeight: 1.6
                    }}
                  >
                    انضم إلى آلاف المعلمين الذين يستخدمون معلم برو لإنشاء محتوى تعليمي احترافي بمساعدة الذكاء الاصطناعي. ابدأ رحلتك مع معلم برو الآن واستمتع بتجربة فريدة.
                  </Typography>

                  <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 3, maxWidth: '500px' }}>
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      style={{ width: '100%' }}
                    >
                      <Button
                        component={Link}
                        to="/register"
                        variant="contained"
                        size="large"
                        fullWidth
                        sx={{
                          px: 5,
                          py: 2,
                          bgcolor: 'white',
                          color: 'primary.main',
                          fontWeight: 'bold',
                          fontSize: '1.1rem',
                          boxShadow: '0 10px 25px rgba(0,0,0,0.2)',
                          borderRadius: 3,
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            bgcolor: 'white',
                            boxShadow: '0 15px 30px rgba(0,0,0,0.3)',
                            transform: 'translateY(-3px)'
                          }
                        }}
                      >
                        إنشاء حساب مجاني
                      </Button>
                    </motion.div>

                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      style={{ width: '100%' }}
                    >
                      <Button
                        component={Link}
                        to="/login"
                        variant="outlined"
                        size="large"
                        fullWidth
                        sx={{
                          px: 5,
                          py: 2,
                          color: 'white',
                          borderColor: 'white',
                          borderWidth: 2,
                          fontWeight: 'bold',
                          fontSize: '1.1rem',
                          borderRadius: 3,
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            borderColor: 'white',
                            borderWidth: 2,
                            bgcolor: 'rgba(255,255,255,0.1)',
                            transform: 'translateY(-3px)'
                          }
                        }}
                      >
                        تسجيل الدخول
                      </Button>
                    </motion.div>
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 4 }}>
                    <CheckCircleIcon sx={{ color: 'white', mr: 1, fontSize: '1.2rem' }} />
                    <Typography variant="body1" color="white" sx={{ mr: 4, opacity: 0.9 }}>
                      لا يلزم بطاقة ائتمان
                    </Typography>

                    <CheckCircleIcon sx={{ color: 'white', mr: 1, fontSize: '1.2rem' }} />
                    <Typography variant="body1" color="white" sx={{ opacity: 0.9 }}>
                      إلغاء الاشتراك في أي وقت
                    </Typography>
                  </Box>
                </Box>
              </motion.div>
            </Grid>

            <Grid item xs={12} md={5}>
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ delay: 0.2, duration: 0.5 }}
              >
                <Box
                  sx={{
                    position: 'relative',
                    borderRadius: 4,
                    overflow: 'hidden',
                    boxShadow: '0 20px 40px rgba(0,0,0,0.3)',
                    height: { xs: 300, md: 400 },
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: 'rgba(255,255,255,0.1)',
                    backdropFilter: 'blur(10px)',
                    border: '1px solid rgba(255,255,255,0.2)'
                  }}
                >
                  <Box
                    component="img"
                    src="/images/app-preview.png"
                    alt="معلم برو"
                    sx={{
                      width: '90%',
                      height: '90%',
                      objectFit: 'contain',
                      filter: 'drop-shadow(0 10px 20px rgba(0,0,0,0.2))'
                    }}
                  />
                </Box>
              </motion.div>
            </Grid>
          </Grid>
        </Container>
      </Box>
    </Box>
  );
};

export default HomePage;
