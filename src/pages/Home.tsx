import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardMedia from '@mui/material/CardMedia';
import Paper from '@mui/material/Paper';
import Divider from '@mui/material/Divider';
import Avatar from '@mui/material/Avatar';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import ChatIcon from '@mui/icons-material/Chat';
import DescriptionIcon from '@mui/icons-material/Description';
import ShareIcon from '@mui/icons-material/Share';
import SchoolIcon from '@mui/icons-material/School';
import GradingIcon from '@mui/icons-material/Grading';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import SpeedIcon from '@mui/icons-material/Speed';
import SecurityIcon from '@mui/icons-material/Security';
import EmojiObjectsIcon from '@mui/icons-material/EmojiObjects';
import FormatQuoteIcon from '@mui/icons-material/FormatQuote';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { motion } from 'framer-motion';
import { useEffect, useRef } from 'react';
import AnimatedSection from '../components/AnimatedSection';
import { fadeInUp, fadeInRight, scaleUp, fadeIn, staggerContainer } from '../styles/animations';

const HomePage = () => {
  const { t } = useTranslation();
  const featuresRef = useRef<HTMLDivElement>(null);
  const templatesRef = useRef<HTMLDivElement>(null);

  // تمرير إلى أعلى الصفحة عند تحميلها
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // التمرير إلى القسم المحدد
  const scrollToSection = (ref: React.RefObject<HTMLDivElement>) => {
    if (ref.current) {
      ref.current.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const features = [
    {
      icon: <ChatIcon fontSize="large" color="primary" />,
      image: '/images/features/ai-chat.svg',
      title: 'المحادثة الذكية',
      description: 'تحدث مع الذكاء الاصطناعي بلغة طبيعية لإنشاء المحتوى التعليمي المناسب لاحتياجاتك'
    },
    {
      icon: <DescriptionIcon fontSize="large" color="primary" />,
      image: '/images/features/templates.svg',
      title: 'قوالب احترافية',
      description: 'اختر من مكتبة متنوعة من القوالب الاحترافية المصممة خصيصاً للمعلمين'
    },
    {
      icon: <ShareIcon fontSize="large" color="primary" />,
      image: '/images/features/export.svg',
      title: 'تصدير ومشاركة',
      description: 'صدّر مستنداتك بصيغ متعددة وشاركها بسهولة مع زملائك'
    }
  ];

  const benefits = [
    {
      icon: <SpeedIcon fontSize="large" sx={{ color: '#3b82f6' }} />,
      title: 'توفير الوقت والجهد',
      description: 'إنشاء المستندات التعليمية في دقائق بدلاً من ساعات'
    },
    {
      icon: <AutoAwesomeIcon fontSize="large" sx={{ color: '#f59e0b' }} />,
      title: 'محتوى احترافي',
      description: 'تصميمات عالية الجودة تعكس احترافية المعلم'
    },
    {
      icon: <EmojiObjectsIcon fontSize="large" sx={{ color: '#10b981' }} />,
      title: 'أفكار إبداعية',
      description: 'اقتراحات ذكية لتطوير المحتوى التعليمي'
    },
    {
      icon: <SecurityIcon fontSize="large" sx={{ color: '#8b5cf6' }} />,
      title: 'خصوصية وأمان',
      description: 'حماية كاملة لبياناتك ومستنداتك التعليمية'
    }
  ];

  const testimonials = [
    {
      name: 'أحمد محمد',
      role: 'معلم لغة عربية',
      avatar: '/images/avatars/teacher1.jpg',
      content: 'ساعدني تطبيق معلم برو في إعداد خطط الدروس بشكل احترافي وسريع، مما وفر لي الكثير من الوقت والجهد.'
    },
    {
      name: 'سارة عبدالله',
      role: 'مشرفة تربوية',
      avatar: '/images/avatars/teacher2.jpg',
      content: 'أصبح إعداد التقارير الإشرافية أسهل بكثير مع معلم برو. التطبيق يقدم قوالب احترافية ومتنوعة تناسب جميع الاحتياجات.'
    },
    {
      name: 'محمد العتيبي',
      role: 'قائد مدرسة',
      avatar: '/images/avatars/teacher3.jpg',
      content: 'نوصي جميع المعلمين في مدرستنا باستخدام معلم برو لما له من أثر إيجابي في تطوير العملية التعليمية وتوثيقها.'
    }
  ];

  return (
    <Box component={motion.div} initial="hidden" animate="visible" variants={fadeInUp} sx={{ mt: -4 }}>
      {/* App Name Showcase */}
      <Box
        component={motion.div}
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          py: { xs: 4, md: 6 },
          mb: { xs: 2, md: 4 }
        }}
      >
        <Box
          sx={{
            position: 'relative',
            display: 'inline-flex',
            flexDirection: 'column',
            alignItems: 'center',
            p: { xs: 3, md: 4 },
            borderRadius: 4,
            background: 'linear-gradient(135deg, rgba(37, 99, 235, 0.05) 0%, rgba(59, 130, 246, 0.1) 100%)',
            boxShadow: '0 12px 40px rgba(0, 0, 0, 0.08)',
            border: '1px solid rgba(37, 99, 235, 0.1)',
            overflow: 'hidden',
            zIndex: 1,
            width: { xs: '90%', sm: '80%', md: '60%' },
            maxWidth: '600px',
            transition: 'all 0.3s ease-in-out',
            '&:hover': {
              transform: 'translateY(-5px)',
              boxShadow: '0 16px 50px rgba(0, 0, 0, 0.12)',
            }
          }}
        >
          {/* Decorative Elements */}
          <Box
            sx={{
              position: 'absolute',
              top: -20,
              right: -20,
              width: 100,
              height: 100,
              borderRadius: '50%',
              background: 'linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(59, 130, 246, 0.2) 100%)',
              zIndex: -1,
              animation: 'pulse 3s infinite ease-in-out',
              '@keyframes pulse': {
                '0%': { transform: 'scale(1)', opacity: 0.5 },
                '50%': { transform: 'scale(1.05)', opacity: 0.8 },
                '100%': { transform: 'scale(1)', opacity: 0.5 },
              },
            }}
          />
          <Box
            sx={{
              position: 'absolute',
              bottom: -30,
              left: -30,
              width: 120,
              height: 120,
              borderRadius: '50%',
              background: 'linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(59, 130, 246, 0.2) 100%)',
              zIndex: -1,
              animation: 'float 4s infinite ease-in-out',
              '@keyframes float': {
                '0%': { transform: 'translateY(0)' },
                '50%': { transform: 'translateY(-5px)' },
                '100%': { transform: 'translateY(0)' },
              },
            }}
          />

          <motion.div
            whileHover={{ scale: 1.05, rotate: 5 }}
            transition={{ type: 'spring', stiffness: 300 }}
          >
            <GradingIcon
              sx={{
                fontSize: { xs: 48, md: 64 },
                color: 'primary.main',
                mb: 2,
                filter: 'drop-shadow(0 4px 6px rgba(37, 99, 235, 0.3))'
              }}
            />
          </motion.div>
          <Typography
            variant="h3"
            component="h1"
            align="center"
            sx={{
              fontWeight: 'bold',
              fontSize: { xs: '2rem', sm: '2.5rem', md: '3rem' },
              background: 'linear-gradient(135deg, #2563eb 0%, #3b82f6 100%)',
              backgroundClip: 'text',
              textFillColor: 'transparent',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              mb: 1,
              textShadow: '0 4px 8px rgba(37, 99, 235, 0.1)'
            }}
          >
            {t('app.name')}
          </Typography>
          <Typography
            variant="h6"
            component="p"
            align="center"
            color="text.secondary"
            sx={{
              maxWidth: { xs: '100%', md: '80%' },
              fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' }
            }}
          >
            منصة ذكية لإنشاء المستندات التعليمية بسهولة وسرعة
          </Typography>
        </Box>
      </Box>

      {/* Hero Section */}
      <Box
        component={motion.div}
        variants={fadeInUp}
        sx={{
          bgcolor: 'primary.main',
          color: 'white',
          py: { xs: 6, md: 8 },
          borderRadius: { xs: '0 0 15px 15px', md: '0 0 30px 30px' },
          mb: 6,
          backgroundImage: 'linear-gradient(135deg, #2563eb 0%, #3b82f6 100%)',
          boxShadow: '0 10px 30px rgba(37, 99, 235, 0.25)',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {/* Decorative background elements */}
        <Box
          sx={{
            position: 'absolute',
            top: '10%',
            right: '5%',
            width: '300px',
            height: '300px',
            borderRadius: '50%',
            background: 'radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%)',
            zIndex: 0
          }}
        />
        <Box
          sx={{
            position: 'absolute',
            bottom: '5%',
            left: '10%',
            width: '200px',
            height: '200px',
            borderRadius: '50%',
            background: 'radial-gradient(circle, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0) 70%)',
            zIndex: 0
          }}
        />

        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
          <Grid container spacing={{ xs: 4, md: 6 }} alignItems="center">
            <Grid item xs={12} md={6}>
              <motion.div variants={fadeInRight}>
                <Typography
                  variant="h3"
                  component="h1"
                  gutterBottom
                  fontWeight="bold"
                  sx={{
                    fontSize: { xs: '1.8rem', sm: '2.2rem', md: '2.8rem' },
                    textShadow: '0 2px 10px rgba(0,0,0,0.1)',
                    mb: 2
                  }}
                >
                  {t('home.hero.title')}
                </Typography>
                <Typography
                  variant="h6"
                  paragraph
                  sx={{
                    fontSize: { xs: '1rem', sm: '1.1rem', md: '1.2rem' },
                    opacity: 0.9,
                    mb: 3,
                    maxWidth: '90%'
                  }}
                >
                  {t('home.hero.subtitle')}
                </Typography>
                <Box sx={{
                  mt: 4,
                  display: 'flex',
                  gap: { xs: 1, sm: 2 },
                  flexDirection: { xs: 'column', sm: 'row' },
                  width: { xs: '100%', sm: 'auto' }
                }}>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    style={{ width: '100%' }}
                  >
                    <Button
                      component={Link}
                      to="/chat"
                      variant="contained"
                      size="large"
                      fullWidth={true}
                      sx={{
                        bgcolor: 'white',
                        color: 'primary.main',
                        fontWeight: 'bold',
                        '&:hover': { bgcolor: 'grey.100' },
                        px: 3,
                        py: 1.5,
                        borderRadius: 2,
                        boxShadow: '0 8px 15px rgba(0,0,0,0.15)',
                        transition: 'all 0.3s ease'
                      }}
                    >
                      {t('home.cta.start')}
                    </Button>
                  </motion.div>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    style={{ width: '100%' }}
                  >
                    <Button
                      component={Link}
                      to="/templates"
                      variant="outlined"
                      size="large"
                      fullWidth={true}
                      sx={{
                        color: 'white',
                        borderColor: 'white',
                        borderWidth: 2,
                        '&:hover': {
                          borderColor: 'white',
                          bgcolor: 'rgba(255,255,255,0.1)'
                        },
                        px: 3,
                        py: 1.5,
                        borderRadius: 2,
                        transition: 'all 0.3s ease'
                      }}
                    >
                      {t('home.cta.learn')}
                    </Button>
                  </motion.div>
                </Box>
              </motion.div>
            </Grid>
            <Grid item xs={12} md={6}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.5 }}
              >
                {/* AI Chat Illustration */}
                <Box
                  sx={{
                    position: 'relative',
                    bgcolor: 'rgba(255, 255, 255, 0.9)',
                    borderRadius: 4,
                    p: 2,
                    boxShadow: '0 15px 35px rgba(0, 0, 0, 0.2)',
                    overflow: 'hidden',
                    transform: 'perspective(1000px) rotateY(-5deg)',
                    transformStyle: 'preserve-3d',
                    transition: 'all 0.5s ease',
                    '&:hover': {
                      transform: 'perspective(1000px) rotateY(0deg) translateY(-5px)',
                      boxShadow: '0 20px 40px rgba(0, 0, 0, 0.25)',
                    }
                  }}
                >
                  {/* Chat Header */}
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between',
                      borderBottom: '1px solid rgba(0, 0, 0, 0.1)',
                      pb: 1,
                      mb: 2
                    }}
                  >
                    <Typography variant="subtitle1" fontWeight="bold" color="primary.main">
                      المحادثة الذكية
                    </Typography>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: '#ff5f57' }} />
                      <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: '#ffbd2e' }} />
                      <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: '#28c941' }} />
                    </Box>
                  </Box>

                  {/* Chat Messages */}
                  <Box sx={{ mb: 2 }}>
                    {/* AI Message */}
                    <Box sx={{ display: 'flex', mb: 2 }}>
                      <Box
                        sx={{
                          width: 36,
                          height: 36,
                          borderRadius: '50%',
                          bgcolor: 'primary.main',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mr: 1,
                          color: 'white',
                          fontSize: '1.2rem',
                          fontWeight: 'bold'
                        }}
                      >
                        AI
                      </Box>
                      <Box
                        sx={{
                          bgcolor: 'grey.100',
                          p: 1.5,
                          borderRadius: '0 15px 15px 15px',
                          maxWidth: '80%'
                        }}
                      >
                        <Typography variant="body2" color="text.primary">
                          مرحباً! كيف يمكنني مساعدتك في إعداد المستندات التعليمية اليوم؟
                        </Typography>
                      </Box>
                    </Box>

                    {/* User Message */}
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                      <Box
                        sx={{
                          bgcolor: 'primary.light',
                          p: 1.5,
                          borderRadius: '15px 0 15px 15px',
                          maxWidth: '80%',
                          color: 'white'
                        }}
                      >
                        <Typography variant="body2">
                          أحتاج إلى إعداد خطة درس لمادة اللغة العربية للصف الثالث
                        </Typography>
                      </Box>
                      <Box
                        sx={{
                          width: 36,
                          height: 36,
                          borderRadius: '50%',
                          bgcolor: 'grey.300',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          ml: 1,
                          fontWeight: 'bold'
                        }}
                      >
                        أ
                      </Box>
                    </Box>

                    {/* AI Response */}
                    <Box sx={{ display: 'flex', mb: 2 }}>
                      <Box
                        sx={{
                          width: 36,
                          height: 36,
                          borderRadius: '50%',
                          bgcolor: 'primary.main',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mr: 1,
                          color: 'white',
                          fontSize: '1.2rem',
                          fontWeight: 'bold'
                        }}
                      >
                        AI
                      </Box>
                      <Box
                        sx={{
                          bgcolor: 'grey.100',
                          p: 1.5,
                          borderRadius: '0 15px 15px 15px',
                          maxWidth: '80%'
                        }}
                      >
                        <Typography variant="body2" color="text.primary">
                          بالتأكيد! سأساعدك في إعداد خطة درس متكاملة لمادة اللغة العربية للصف الثالث. هل تفضل استخدام أحد القوالب الجاهزة أم إنشاء خطة مخصصة؟
                        </Typography>
                      </Box>
                    </Box>
                  </Box>

                  {/* Chat Input */}
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      borderTop: '1px solid rgba(0, 0, 0, 0.1)',
                      pt: 1.5
                    }}
                  >
                    <Box
                      sx={{
                        flexGrow: 1,
                        bgcolor: 'grey.100',
                        borderRadius: 3,
                        p: 1,
                        mr: 1,
                        color: 'grey.500',
                        fontSize: '0.9rem'
                      }}
                    >
                      اكتب رسالتك هنا...
                    </Box>
                    <Box
                      sx={{
                        width: 36,
                        height: 36,
                        borderRadius: '50%',
                        bgcolor: 'primary.main',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: 'white',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease',
                        '&:hover': {
                          bgcolor: 'primary.dark',
                          transform: 'scale(1.05)'
                        }
                      }}
                    >
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M22 2L11 13" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                        <path d="M22 2L15 22L11 13L2 9L22 2Z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                      </svg>
                    </Box>
                  </Box>
                </Box>
              </motion.div>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Features Section */}
      <AnimatedSection delay={0.2}>
        <Container maxWidth="lg" sx={{ mb: 8 }}>
          <Box
            sx={{
              textAlign: 'center',
              mb: 6,
              position: 'relative',
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: '-15px',
                left: '50%',
                transform: 'translateX(-50%)',
                width: '80px',
                height: '4px',
                borderRadius: '2px',
                background: 'linear-gradient(90deg, #3b82f6 0%, #2563eb 100%)',
              }
            }}
          >
            <Typography
              variant="h4"
              component="h2"
              align="center"
              gutterBottom
              fontWeight="bold"
              sx={{
                fontSize: { xs: '1.8rem', sm: '2.2rem', md: '2.5rem' },
                background: 'linear-gradient(135deg, #2563eb 0%, #3b82f6 100%)',
                backgroundClip: 'text',
                textFillColor: 'transparent',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                mb: 2
              }}
            >
              ميزات تطبيق معلم برو
            </Typography>
            <Typography
              variant="subtitle1"
              align="center"
              color="text.secondary"
              paragraph
              sx={{
                mb: 2,
                maxWidth: '700px',
                mx: 'auto',
                fontSize: { xs: '0.95rem', md: '1.1rem' }
              }}
            >
              أدوات متكاملة لمساعدة المعلمين على إنشاء محتوى تعليمي وإداري احترافي
            </Typography>
          </Box>

          <Grid container spacing={{ xs: 3, md: 5 }} sx={{ mt: 4 }}>
            {features.map((feature, index) => (
              <Grid item xs={12} md={4} key={index}>
                <motion.div
                  whileHover={{ y: -10 }}
                  transition={{ type: 'spring', stiffness: 300 }}
                >
                  <Card
                    sx={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      borderRadius: 4,
                      boxShadow: '0 10px 30px rgba(0,0,0,0.08)',
                      overflow: 'hidden',
                      transition: 'all 0.3s ease',
                      position: 'relative',
                      '&:hover': {
                        boxShadow: '0 15px 35px rgba(37, 99, 235, 0.15)',
                      },
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        height: '5px',
                        background: 'linear-gradient(90deg, #3b82f6, #2563eb)',
                        opacity: 0.8,
                      }
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'center',
                        alignItems: 'center',
                        pt: 4,
                        pb: 2,
                        position: 'relative',
                        overflow: 'hidden',
                        '&::after': {
                          content: '""',
                          position: 'absolute',
                          bottom: 0,
                          left: '10%',
                          width: '80%',
                          height: '1px',
                          background: 'linear-gradient(90deg, transparent, rgba(0,0,0,0.05), transparent)',
                        }
                      }}
                    >
                      <motion.div
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        transition={{ type: 'spring', stiffness: 400 }}
                      >
                        <Box
                          component="img"
                          src={feature.image}
                          alt={feature.title}
                          sx={{
                            width: 130,
                            height: 130,
                            objectFit: 'contain',
                            filter: 'drop-shadow(0 8px 15px rgba(37, 99, 235, 0.2))',
                            transition: 'all 0.3s ease',
                          }}
                        />
                      </motion.div>
                    </Box>
                    <CardContent
                      sx={{
                        flexGrow: 1,
                        textAlign: 'center',
                        px: 3,
                        pb: 4
                      }}
                    >
                      <Typography
                        gutterBottom
                        variant="h5"
                        component="h3"
                        fontWeight="bold"
                        sx={{
                          mb: 2,
                          color: 'primary.main'
                        }}
                      >
                        {feature.title}
                      </Typography>
                      <Typography
                        variant="body1"
                        color="text.secondary"
                        sx={{
                          lineHeight: 1.6
                        }}
                      >
                        {feature.description}
                      </Typography>
                      <Box sx={{ mt: 3 }}>
                        <motion.div
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Button
                            variant="outlined"
                            color="primary"
                            size="small"
                            sx={{
                              borderRadius: 2,
                              px: 2,
                              py: 0.5,
                              borderWidth: 1.5,
                              '&:hover': {
                                borderWidth: 1.5,
                                bgcolor: 'rgba(37, 99, 235, 0.05)'
                              }
                            }}
                          >
                            اكتشف المزيد
                          </Button>
                        </motion.div>
                      </Box>
                    </CardContent>
                  </Card>
                </motion.div>
              </Grid>
            ))}
          </Grid>
        </Container>
      </AnimatedSection>

      {/* Templates Preview Section */}
      <Box
        sx={{
          bgcolor: 'grey.50',
          py: 8,
          borderRadius: { xs: 0, md: '20px' },
          mx: { xs: 0, md: 4 },
          boxShadow: '0 4px 20px rgba(0,0,0,0.03)',
          overflow: 'hidden'
        }}
      >
        <AnimatedSection delay={0.4}>
          <Container maxWidth="lg">
            <Typography variant="h4" component="h2" align="center" gutterBottom fontWeight="bold">
              قوالب احترافية متنوعة
            </Typography>
            <Typography variant="subtitle1" align="center" color="text.secondary" paragraph sx={{ mb: 6 }}>
              اختر من مجموعة متنوعة من القوالب المصممة خصيصاً للمعلمين
            </Typography>

            <Grid container spacing={3}>
              {[1, 2, 3].map((item) => (
                <Grid item xs={12} md={4} key={item}>
                  <motion.div
                    whileHover={{ y: -5 }}
                    transition={{ type: 'spring', stiffness: 300 }}
                  >
                    <Card sx={{
                      borderRadius: 3,
                      overflow: 'hidden',
                      boxShadow: '0 4px 15px rgba(0,0,0,0.1)',
                      transition: 'all 0.3s ease'
                    }}>
                      <CardMedia
                        component="img"
                        height="200"
                        image={`/images/templates/template-${item}.svg`}
                        alt={`Template ${item}`}
                      />
                      <CardContent>
                        <Typography gutterBottom variant="h6" component="div">
                          {item === 1 ? 'تقرير نشاط طلابي' : item === 2 ? 'ملف إنجاز معلم' : 'خطة درس'}
                        </Typography>
                        <motion.div whileHover={{ scale: 1.03 }} whileTap={{ scale: 0.97 }}>
                          <Button
                            component={Link}
                            to="/templates"
                            variant="contained"
                            fullWidth
                            sx={{
                              mt: 2,
                              py: 1,
                              borderRadius: 2
                            }}
                          >
                            استخدام القالب
                          </Button>
                        </motion.div>
                      </CardContent>
                    </Card>
                  </motion.div>
                </Grid>
              ))}
            </Grid>

            <Box sx={{ textAlign: 'center', mt: 4 }}>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  component={Link}
                  to="/templates"
                  variant="outlined"
                  size="large"
                  sx={{
                    px: 4,
                    py: 1.2,
                    borderRadius: 2
                  }}
                >
                  عرض جميع القوالب
                </Button>
              </motion.div>
            </Box>
          </Container>
        </AnimatedSection>
      </Box>

      {/* CTA Section */}
      <AnimatedSection delay={0.6}>
        <Box
          sx={{
            py: { xs: 8, sm: 10, md: 12 },
            px: { xs: 2, sm: 3, md: 4 },
            background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
            borderRadius: { xs: '15px', sm: '20px', md: '30px' },
            my: { xs: 6, sm: 8, md: 10 },
            mx: { xs: 2, sm: 3, md: 4 },
            position: 'relative',
            overflow: 'hidden',
            boxShadow: '0 20px 50px rgba(37, 99, 235, 0.3)'
          }}
        >
          {/* Decorative Elements */}
          <Box
            sx={{
              position: 'absolute',
              top: -50,
              right: -50,
              width: 200,
              height: 200,
              borderRadius: '50%',
              background: 'radial-gradient(circle, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 70%)',
              animation: 'pulse 6s infinite ease-in-out',
              '@keyframes pulse': {
                '0%': { transform: 'scale(1)', opacity: 0.5 },
                '50%': { transform: 'scale(1.1)', opacity: 0.8 },
                '100%': { transform: 'scale(1)', opacity: 0.5 },
              },
            }}
          />
          <Box
            sx={{
              position: 'absolute',
              bottom: -70,
              left: -70,
              width: 250,
              height: 250,
              borderRadius: '50%',
              background: 'radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%)',
              animation: 'float 8s infinite ease-in-out',
              '@keyframes float': {
                '0%': { transform: 'translateY(0)' },
                '50%': { transform: 'translateY(-15px)' },
                '100%': { transform: 'translateY(0)' },
              },
            }}
          />
          <Box
            sx={{
              position: 'absolute',
              top: '30%',
              left: '10%',
              width: 100,
              height: 100,
              borderRadius: '50%',
              background: 'radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 70%)',
              animation: 'float 5s infinite ease-in-out 1s',
            }}
          />
          <Box
            sx={{
              position: 'absolute',
              bottom: '20%',
              right: '15%',
              width: 150,
              height: 150,
              borderRadius: '50%',
              background: 'radial-gradient(circle, rgba(255,255,255,0.08) 0%, rgba(255,255,255,0) 70%)',
              animation: 'pulse 7s infinite ease-in-out 2s',
            }}
          />

          <Container maxWidth="md" sx={{ position: 'relative', zIndex: 2 }}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <Box
                sx={{
                  textAlign: 'center',
                  position: 'relative',
                  zIndex: 2,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center'
                }}
              >
                <Typography
                  variant="h3"
                  component="h2"
                  gutterBottom
                  fontWeight="bold"
                  color="white"
                  sx={{
                    fontSize: { xs: '1.75rem', sm: '2.25rem', md: '3rem' },
                    textShadow: '0 2px 15px rgba(0,0,0,0.2)',
                    mb: 3
                  }}
                >
                  ابدأ استخدام معلم برو اليوم
                </Typography>
                <Typography
                  variant="h6"
                  color="white"
                  paragraph
                  sx={{
                    mb: 5,
                    opacity: 0.9,
                    fontSize: { xs: '0.9rem', sm: '1rem', md: '1.25rem' },
                    maxWidth: '700px'
                  }}
                >
                  انضم إلى آلاف المعلمين الذين يستخدمون معلم برو لإنشاء محتوى تعليمي احترافي بمساعدة الذكاء الاصطناعي
                </Typography>

                <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 2, width: '100%', maxWidth: '500px' }}>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    style={{ width: '100%' }}
                  >
                    <Button
                      component={Link}
                      to="/register"
                      variant="contained"
                      size="large"
                      fullWidth
                      sx={{
                        px: 5,
                        py: 2,
                        bgcolor: 'white',
                        color: 'primary.main',
                        fontWeight: 'bold',
                        fontSize: '1.1rem',
                        boxShadow: '0 10px 25px rgba(0,0,0,0.2)',
                        borderRadius: 3,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          bgcolor: 'white',
                          boxShadow: '0 15px 30px rgba(0,0,0,0.3)',
                          transform: 'translateY(-3px)'
                        },
                        '&:active': {
                          transform: 'translateY(0)'
                        }
                      }}
                    >
                      إنشاء حساب مجاني
                    </Button>
                  </motion.div>

                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    style={{ width: '100%' }}
                  >
                    <Button
                      component={Link}
                      to="/login"
                      variant="outlined"
                      size="large"
                      fullWidth
                      sx={{
                        px: 5,
                        py: 2,
                        color: 'white',
                        borderColor: 'white',
                        borderWidth: 2,
                        fontWeight: 'bold',
                        fontSize: '1.1rem',
                        borderRadius: 3,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                          borderColor: 'white',
                          borderWidth: 2,
                          bgcolor: 'rgba(255,255,255,0.1)',
                          transform: 'translateY(-3px)'
                        },
                        '&:active': {
                          transform: 'translateY(0)'
                        }
                      }}
                    >
                      تسجيل الدخول
                    </Button>
                  </motion.div>
                </Box>

                <Typography
                  variant="body2"
                  color="white"
                  sx={{
                    mt: 4,
                    opacity: 0.7,
                    fontSize: '0.9rem'
                  }}
                >
                  لا يلزم بطاقة ائتمان • إلغاء الاشتراك في أي وقت
                </Typography>
              </Box>
            </motion.div>
          </Container>
        </Box>
      </AnimatedSection>
    </Box>
  );
};

export default HomePage;
