import { useTranslation } from 'react-i18next';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import Grid from '@mui/material/Grid';
import Avatar from '@mui/material/Avatar';
import Button from '@mui/material/Button';
import TextField from '@mui/material/TextField';
import Divider from '@mui/material/Divider';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemText from '@mui/material/ListItemText';
import ListItemAvatar from '@mui/material/ListItemAvatar';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import { useState } from 'react';
import DescriptionIcon from '@mui/icons-material/Description';
import SettingsIcon from '@mui/icons-material/Settings';
import PersonIcon from '@mui/icons-material/Person';
import SchoolIcon from '@mui/icons-material/School';
import EmailIcon from '@mui/icons-material/Email';
import PhoneIcon from '@mui/icons-material/Phone';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import WorkIcon from '@mui/icons-material/Work';
import MenuBookIcon from '@mui/icons-material/MenuBook';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`profile-tabpanel-${index}`}
      aria-labelledby={`profile-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const ProfilePage = () => {
  const { t } = useTranslation();
  const [tabValue, setTabValue] = useState(0);
  const [madrasatiConnected, setMadrasatiConnected] = useState(false);
  const [noorConnected, setNoorConnected] = useState(false);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };
  
  // محاكاة الاتصال بمنصة مدرستي
  const handleConnectMadrasati = () => {
    setMadrasatiConnected(true);
  };
  
  // محاكاة الاتصال بنظام نور
  const handleConnectNoor = () => {
    setNoorConnected(true);
  };
  
  // محاكاة قطع الاتصال بمنصة مدرستي
  const handleDisconnectMadrasati = () => {
    setMadrasatiConnected(false);
  };
  
  // محاكاة قطع الاتصال بنظام نور
  const handleDisconnectNoor = () => {
    setNoorConnected(false);
  };

  // Mock user data
  const userData = {
    name: 'محمد أحمد',
    email: '<EMAIL>',
    phone: '0512345678',
    school: 'مدرسة الأمل الثانوية',
    location: 'الرياض، المملكة العربية السعودية',
    position: 'معلم لغة عربية',
    avatar: '/avatar-placeholder.png',
    subscription: {
      plan: 'الباقة المميزة',
      status: 'نشط',
      expiry: '31/12/2023',
    },
    documents: [
      { id: 1, title: 'تقرير نشاط يوم المعلم', date: '15/10/2023', type: 'تقرير' },
      { id: 2, title: 'ملف إنجاز الفصل الأول', date: '01/11/2023', type: 'ملف إنجاز' },
      { id: 3, title: 'خطة درس مهارات القراءة', date: '10/11/2023', type: 'خطة درس' },
    ],
    templates: [
      { id: 1, title: 'قالب تقرير النشاط المعدل', date: '20/10/2023' },
      { id: 2, title: 'قالب ملف الإنجاز الشخصي', date: '05/11/2023' },
    ],
  };

  return (
    <Container maxWidth="lg" sx={{ pb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
        {t('profile.title')}
      </Typography>

      <Grid container spacing={3}>
        {/* Profile Summary */}
        <Grid item xs={12} md={4}>
          <Paper
            sx={{
              p: 3,
              textAlign: 'center',
              borderRadius: 3,
              boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
              overflow: 'hidden',
              position: 'relative',
            }}
            className="fade-in"
          >
            <Box
              sx={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                height: 100,
                background: 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)',
                zIndex: 0,
              }}
            />
            <Avatar
              src={userData.avatar}
              sx={{
                width: 120,
                height: 120,
                mx: 'auto',
                mb: 2,
                mt: 3,
                border: '4px solid white',
                boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                position: 'relative',
                zIndex: 1,
              }}
            />
            <Typography variant="h5" gutterBottom fontWeight="bold">
              {userData.name}
            </Typography>
            <Typography variant="body1" color="primary" gutterBottom fontWeight="medium">
              {userData.position}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {userData.school}
            </Typography>

            <Divider sx={{ my: 3 }} />

            <List sx={{ width: '100%' }}>
              <ListItem sx={{ px: 0 }}>
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: 'rgba(37, 99, 235, 0.1)', color: 'primary.main' }}>
                    <EmailIcon />
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={<Typography variant="body2" color="text.secondary">البريد الإلكتروني</Typography>}
                  secondary={<Typography variant="body1">{userData.email}</Typography>}
                />
              </ListItem>
              <ListItem sx={{ px: 0 }}>
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: 'rgba(37, 99, 235, 0.1)', color: 'primary.main' }}>
                    <PhoneIcon />
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={<Typography variant="body2" color="text.secondary">رقم الهاتف</Typography>}
                  secondary={<Typography variant="body1">{userData.phone}</Typography>}
                />
              </ListItem>
              <ListItem sx={{ px: 0 }}>
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: 'rgba(37, 99, 235, 0.1)', color: 'primary.main' }}>
                    <LocationOnIcon />
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={<Typography variant="body2" color="text.secondary">الموقع</Typography>}
                  secondary={<Typography variant="body1">{userData.location}</Typography>}
                />
              </ListItem>
              <ListItem sx={{ px: 0 }}>
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: 'rgba(37, 99, 235, 0.1)', color: 'primary.main' }}>
                    <WorkIcon />
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={<Typography variant="body2" color="text.secondary">المهنة</Typography>}
                  secondary={<Typography variant="body1">{userData.position}</Typography>}
                />
              </ListItem>
              <ListItem sx={{ px: 0 }}>
                <ListItemAvatar>
                  <Avatar sx={{ bgcolor: 'rgba(37, 99, 235, 0.1)', color: 'primary.main' }}>
                    <SchoolIcon />
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={<Typography variant="body2" color="text.secondary">المدرسة / الجهة</Typography>}
                  secondary={<Typography variant="body1">{userData.school}</Typography>}
                />
              </ListItem>
            </List>

            <Button
              variant="outlined"
              fullWidth
              sx={{
                mt: 2,
                borderWidth: 2,
                py: 1,
                '&:hover': {
                  borderWidth: 2,
                  bgcolor: 'rgba(37, 99, 235, 0.04)',
                },
              }}
            >
              تعديل الملف الشخصي
            </Button>
          </Paper>

          {/* Subscription Info */}
          <Paper
            sx={{
              p: 3,
              mt: 3,
              borderRadius: 3,
              boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
              overflow: 'hidden',
            }}
            className="fade-in"
          >
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <Box
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: '50%',
                  bgcolor: 'primary.main',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mr: 2,
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" style={{ color: 'white' }}>
                  <path d="M20.2 7.8l-7.7 7.7-4-4-5.7 5.7"/>
                  <path d="M15 7h6v6"/>
                </svg>
              </Box>
              <Typography variant="h6" fontWeight="bold">
                {t('profile.subscription')}
              </Typography>
            </Box>
            <Box
              sx={{
                background: 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)',
                p: 3,
                borderRadius: 2,
                color: 'white',
                position: 'relative',
                overflow: 'hidden',
              }}
            >
              <Box
                sx={{
                  position: 'absolute',
                  top: -15,
                  right: -15,
                  width: 80,
                  height: 80,
                  borderRadius: '50%',
                  bgcolor: 'rgba(255,255,255,0.1)',
                }}
              />
              <Box
                sx={{
                  position: 'absolute',
                  bottom: -20,
                  left: -20,
                  width: 100,
                  height: 100,
                  borderRadius: '50%',
                  bgcolor: 'rgba(255,255,255,0.05)',
                }}
              />
              <Typography variant="h5" fontWeight="bold" sx={{ mb: 1 }}>
                {userData.subscription.plan}
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  الحالة:
                </Typography>
                <Typography variant="body2" fontWeight="bold">
                  {userData.subscription.status}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  تاريخ الانتهاء:
                </Typography>
                <Typography variant="body2" fontWeight="bold">
                  {userData.subscription.expiry}
                </Typography>
              </Box>
            </Box>
            <Button
              variant="contained"
              fullWidth
              sx={{
                mt: 2,
                py: 1.5,
                boxShadow: '0 4px 12px rgba(37, 99, 235, 0.3)',
                '&:hover': {
                  boxShadow: '0 6px 16px rgba(37, 99, 235, 0.4)',
                },
              }}
            >
              ترقية الاشتراك
            </Button>
          </Paper>
        </Grid>

        {/* Main Content */}
        <Grid item xs={12} md={8}>
          <Paper
            sx={{
              width: '100%',
              borderRadius: 3,
              boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
              overflow: 'hidden',
            }}
            className="fade-in"
          >
            <Box sx={{ borderBottom: 1, borderColor: 'divider', px: 3, pt: 2 }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                aria-label="profile tabs"
                sx={{
                  '& .MuiTab-root': {
                    minHeight: 64,
                    fontWeight: 500,
                    fontSize: '0.95rem',
                    transition: 'all 0.2s',
                    '&.Mui-selected': {
                      fontWeight: 'bold',
                      color: 'primary.main',
                    },
                  },
                  '& .MuiTabs-indicator': {
                    height: 3,
                    borderRadius: '3px 3px 0 0',
                  },
                }}
              >
                <Tab
                  label={t('profile.documents')}
                  icon={<DescriptionIcon />}
                  iconPosition="start"
                />
                <Tab
                  label={t('profile.templates')}
                  icon={<DescriptionIcon />}
                  iconPosition="start"
                />
                <Tab
                  label={t('profile.systems')}
                  icon={<SchoolIcon />}
                  iconPosition="start"
                />
                <Tab
                  label={t('profile.settings')}
                  icon={<SettingsIcon />}
                  iconPosition="start"
                />
              </Tabs>
            </Box>

            {/* Documents Tab */}
            <TabPanel value={tabValue} index={0}>
              <Box className="fade-in-up">
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Box
                    sx={{
                      width: 40,
                      height: 40,
                      borderRadius: '50%',
                      bgcolor: 'rgba(37, 99, 235, 0.1)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 2,
                    }}
                  >
                    <DescriptionIcon color="primary" />
                  </Box>
                  <Typography variant="h6" fontWeight="bold">
                    المستندات المحفوظة
                  </Typography>
                </Box>

                {userData.documents.length > 0 ? (
                  <Grid container spacing={3}>
                    {userData.documents.map((doc) => (
                      <Grid item xs={12} sm={6} key={doc.id}>
                        <Card
                          sx={{
                            borderRadius: 2,
                            boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                            transition: 'all 0.3s',
                            '&:hover': {
                              transform: 'translateY(-4px)',
                              boxShadow: '0 8px 16px rgba(0,0,0,0.1)',
                            },
                          }}
                          className="hover-scale"
                        >
                          <CardContent sx={{ p: 3 }}>
                            <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                              <Box
                                sx={{
                                  p: 1.5,
                                  borderRadius: 2,
                                  bgcolor: 'rgba(37, 99, 235, 0.1)',
                                  mr: 2,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                }}
                              >
                                <DescriptionIcon color="primary" />
                              </Box>
                              <Box sx={{ flexGrow: 1 }}>
                                <Typography variant="subtitle1" component="div" fontWeight="bold">
                                  {doc.title}
                                </Typography>
                                <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                                  <Typography variant="body2" color="text.secondary" sx={{ mr: 2 }}>
                                    النوع: {doc.type}
                                  </Typography>
                                  <Typography variant="body2" color="text.secondary">
                                    {doc.date}
                                  </Typography>
                                </Box>
                              </Box>
                            </Box>
                            <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                              <Button
                                size="small"
                                variant="contained"
                                sx={{
                                  flexGrow: 1,
                                  boxShadow: 'none',
                                  '&:hover': { boxShadow: '0 4px 8px rgba(0,0,0,0.1)' },
                                }}
                              >
                                عرض
                              </Button>
                              <Button
                                size="small"
                                variant="outlined"
                                color="primary"
                                sx={{
                                  flexGrow: 1,
                                  borderWidth: 1.5,
                                  '&:hover': { borderWidth: 1.5 },
                                }}
                              >
                                تعديل
                              </Button>
                              <Button
                                size="small"
                                variant="outlined"
                                color="error"
                                sx={{
                                  borderWidth: 1.5,
                                  '&:hover': { borderWidth: 1.5 },
                                }}
                              >
                                حذف
                              </Button>
                            </Box>
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                ) : (
                  <Box
                    sx={{
                      py: 6,
                      textAlign: 'center',
                      bgcolor: 'rgba(37, 99, 235, 0.02)',
                      borderRadius: 2,
                      border: '1px dashed rgba(37, 99, 235, 0.2)',
                    }}
                  >
                    <DescriptionIcon sx={{ fontSize: 48, color: 'text.disabled', mb: 1 }} />
                    <Typography variant="h6" color="text.secondary" gutterBottom>
                      لا توجد مستندات محفوظة
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      قم بإنشاء مستندات جديدة باستخدام المحادثة الذكية أو القوالب
                    </Typography>
                    <Button variant="contained">إنشاء مستند جديد</Button>
                  </Box>
                )}
              </Box>
            </TabPanel>

            {/* Templates Tab */}
            <TabPanel value={tabValue} index={1}>
              <Box className="fade-in-up">
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Box
                    sx={{
                      width: 40,
                      height: 40,
                      borderRadius: '50%',
                      bgcolor: 'rgba(37, 99, 235, 0.1)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 2,
                    }}
                  >
                    <DescriptionIcon color="primary" />
                  </Box>
                  <Typography variant="h6" fontWeight="bold">
                    القوالب المخصصة
                  </Typography>
                </Box>

                {userData.templates.length > 0 ? (
                  <Grid container spacing={3}>
                    {userData.templates.map((template) => (
                      <Grid item xs={12} sm={6} key={template.id}>
                        <Card
                          sx={{
                            borderRadius: 2,
                            boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                            transition: 'all 0.3s',
                            '&:hover': {
                              transform: 'translateY(-4px)',
                              boxShadow: '0 8px 16px rgba(0,0,0,0.1)',
                            },
                          }}
                          className="hover-scale"
                        >
                          <CardContent sx={{ p: 3 }}>
                            <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                              <Box
                                sx={{
                                  p: 1.5,
                                  borderRadius: 2,
                                  bgcolor: 'rgba(14, 165, 233, 0.1)',
                                  mr: 2,
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                }}
                              >
                                <DescriptionIcon sx={{ color: '#0ea5e9' }} />
                              </Box>
                              <Box sx={{ flexGrow: 1 }}>
                                <Typography variant="subtitle1" component="div" fontWeight="bold">
                                  {template.title}
                                </Typography>
                                <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                                  تاريخ الإنشاء: {template.date}
                                </Typography>
                              </Box>
                            </Box>
                            <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
                              <Button
                                size="small"
                                variant="contained"
                                sx={{
                                  flexGrow: 1,
                                  boxShadow: 'none',
                                  bgcolor: '#0ea5e9',
                                  '&:hover': {
                                    boxShadow: '0 4px 8px rgba(14, 165, 233, 0.3)',
                                    bgcolor: '#0284c7',
                                  },
                                }}
                              >
                                استخدام
                              </Button>
                              <Button
                                size="small"
                                variant="outlined"
                                sx={{
                                  flexGrow: 1,
                                  borderWidth: 1.5,
                                  borderColor: '#0ea5e9',
                                  color: '#0ea5e9',
                                  '&:hover': {
                                    borderWidth: 1.5,
                                    borderColor: '#0284c7',
                                    color: '#0284c7',
                                  },
                                }}
                              >
                                تعديل
                              </Button>
                              <Button
                                size="small"
                                variant="outlined"
                                color="error"
                                sx={{
                                  borderWidth: 1.5,
                                  '&:hover': { borderWidth: 1.5 },
                                }}
                              >
                                حذف
                              </Button>
                            </Box>
                          </CardContent>
                        </Card>
                      </Grid>
                    ))}
                  </Grid>
                ) : (
                  <Box
                    sx={{
                      py: 6,
                      textAlign: 'center',
                      bgcolor: 'rgba(14, 165, 233, 0.02)',
                      borderRadius: 2,
                      border: '1px dashed rgba(14, 165, 233, 0.2)',
                    }}
                  >
                    <DescriptionIcon sx={{ fontSize: 48, color: 'text.disabled', mb: 1 }} />
                    <Typography variant="h6" color="text.secondary" gutterBottom>
                      لا توجد قوالب مخصصة
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                      قم بتخصيص القوالب الجاهزة وحفظها لاستخدامها لاحقاً
                    </Typography>
                    <Button
                      variant="contained"
                      sx={{
                        bgcolor: '#0ea5e9',
                        '&:hover': { bgcolor: '#0284c7' },
                      }}
                    >
                      استعراض القوالب
                    </Button>
                  </Box>
                )}
              </Box>
            </TabPanel>

            {/* Systems Integration Tab */}
            <TabPanel value={tabValue} index={2}>
              <Box className="fade-in-up">
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Box
                    sx={{
                      width: 40,
                      height: 40,
                      borderRadius: '50%',
                      bgcolor: 'rgba(37, 99, 235, 0.1)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 2,
                    }}
                  >
                    <SchoolIcon color="primary" />
                  </Box>
                  <Typography variant="h6" fontWeight="bold">
                    الأنظمة المتكاملة
                  </Typography>
                </Box>
                
                <Typography variant="body1" paragraph color="text.secondary">
                  يمكنك ربط حسابك في تطبيق معلم برو مع منصة مدرستي ونظام نور لتسهيل تصدير المستندات والتقارير.
                </Typography>
                
                <Grid container spacing={3}>
                  {/* منصة مدرستي */}
                  <Grid item xs={12} md={6}>
                    <Card
                      sx={{
                        borderRadius: 2,
                        boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                        transition: 'all 0.3s',
                        '&:hover': {
                          transform: 'translateY(-4px)',
                          boxShadow: '0 8px 16px rgba(0,0,0,0.1)',
                        },
                      }}
                      className="hover-scale"
                    >
                      <CardContent sx={{ p: 3 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <Avatar sx={{ bgcolor: 'rgba(46, 125, 50, 0.1)', color: 'success.main', mr: 2 }}>
                            <SchoolIcon />
                          </Avatar>
                          <Box>
                            <Typography variant="h6" fontWeight="bold">منصة مدرستي</Typography>
                            <Typography variant="body2" color="text.secondary">
                              {madrasatiConnected ? 'متصل' : 'غير متصل'}
                            </Typography>
                          </Box>
                        </Box>
                        <Divider sx={{ my: 2 }} />
                        {madrasatiConnected ? (
                          <>
                            <Box sx={{ mb: 2 }}>
                              <Typography variant="body2" color="text.secondary" gutterBottom>
                                اسم المستخدم
                              </Typography>
                              <Typography variant="body1">user_madrasati</Typography>
                            </Box>
                            <Box sx={{ mb: 2 }}>
                              <Typography variant="body2" color="text.secondary" gutterBottom>
                                آخر تصدير
                              </Typography>
                              <Typography variant="body1">15/04/2023</Typography>
                            </Box>
                            <Button 
                              variant="outlined" 
                              color="error" 
                              fullWidth
                              onClick={handleDisconnectMadrasati}
                              sx={{ mt: 2 }}
                            >
                              قطع الاتصال
                            </Button>
                          </>
                        ) : (
                          <>
                            <Typography variant="body2" paragraph>
                              قم بربط حسابك في منصة مدرستي لتتمكن من تصدير المستندات مباشرة إلى المنصة.
                            </Typography>
                            <Button 
                              variant="contained" 
                              color="success" 
                              fullWidth
                              onClick={handleConnectMadrasati}
                            >
                              ربط الحساب
                            </Button>
                          </>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>
                  
                  {/* نظام نور */}
                  <Grid item xs={12} md={6}>
                    <Card
                      sx={{
                        borderRadius: 2,
                        boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                        transition: 'all 0.3s',
                        '&:hover': {
                          transform: 'translateY(-4px)',
                          boxShadow: '0 8px 16px rgba(0,0,0,0.1)',
                        },
                      }}
                      className="hover-scale"
                    >
                      <CardContent sx={{ p: 3 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <Avatar sx={{ bgcolor: 'rgba(25, 118, 210, 0.1)', color: 'primary.main', mr: 2 }}>
                            <MenuBookIcon />
                          </Avatar>
                          <Box>
                            <Typography variant="h6" fontWeight="bold">نظام نور</Typography>
                            <Typography variant="body2" color="text.secondary">
                              {noorConnected ? 'متصل' : 'غير متصل'}
                            </Typography>
                          </Box>
                        </Box>
                        <Divider sx={{ my: 2 }} />
                        {noorConnected ? (
                          <>
                            <Box sx={{ mb: 2 }}>
                              <Typography variant="body2" color="text.secondary" gutterBottom>
                                اسم المستخدم
                              </Typography>
                              <Typography variant="body1">user_noor</Typography>
                            </Box>
                            <Box sx={{ mb: 2 }}>
                              <Typography variant="body2" color="text.secondary" gutterBottom>
                                آخر تصدير
                              </Typography>
                              <Typography variant="body1">20/04/2023</Typography>
                            </Box>
                            <Button 
                              variant="outlined" 
                              color="error" 
                              fullWidth
                              onClick={handleDisconnectNoor}
                              sx={{ mt: 2 }}
                            >
                              قطع الاتصال
                            </Button>
                          </>
                        ) : (
                          <>
                            <Typography variant="body2" paragraph>
                              قم بربط حسابك في نظام نور لتتمكن من تصدير المستندات مباشرة إلى النظام.
                            </Typography>
                            <Button 
                              variant="contained" 
                              color="primary" 
                              fullWidth
                              onClick={handleConnectNoor}
                            >
                              ربط الحساب
                            </Button>
                          </>
                        )}
                      </CardContent>
                    </Card>
                  </Grid>
                  
                  {/* سجل التصدير */}
                  <Grid item xs={12}>
                    <Card
                      sx={{
                        borderRadius: 2,
                        boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                        mt: 3,
                        overflow: 'hidden',
                      }}
                    >
                      <CardContent sx={{ p: 3 }}>
                        <Typography variant="h6" gutterBottom fontWeight="bold">
                          سجل التصدير
                        </Typography>
                        <Typography variant="body2" color="text.secondary" paragraph>
                          آخر المستندات التي تم تصديرها إلى الأنظمة المتكاملة
                        </Typography>
                        
                        <List>
                          <ListItem sx={{ px: 0, py: 2 }}>
                            <ListItemAvatar>
                              <Avatar sx={{ bgcolor: 'rgba(46, 125, 50, 0.1)', color: 'success.main' }}>
                                <SchoolIcon />
                              </Avatar>
                            </ListItemAvatar>
                            <ListItemText
                              primary="تقرير نشاط يوم المعلم"
                              secondary="تم التصدير إلى منصة مدرستي - 15/04/2023"
                            />
                          </ListItem>
                          <Divider />
                          <ListItem sx={{ px: 0, py: 2 }}>
                            <ListItemAvatar>
                              <Avatar sx={{ bgcolor: 'rgba(25, 118, 210, 0.1)', color: 'primary.main' }}>
                                <MenuBookIcon />
                              </Avatar>
                            </ListItemAvatar>
                            <ListItemText
                              primary="خطة درس مهارات القراءة"
                              secondary="تم التصدير إلى نظام نور - 20/04/2023"
                            />
                          </ListItem>
                          <Divider />
                          <ListItem sx={{ px: 0, py: 2 }}>
                            <ListItemAvatar>
                              <Avatar sx={{ bgcolor: 'rgba(46, 125, 50, 0.1)', color: 'success.main' }}>
                                <SchoolIcon />
                              </Avatar>
                            </ListItemAvatar>
                            <ListItemText
                              primary="تقرير إنجاز فصلي"
                              secondary="تم التصدير إلى منصة مدرستي - 10/04/2023"
                            />
                          </ListItem>
                        </List>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Box>
            </TabPanel>
            
            {/* Settings Tab */}
            <TabPanel value={tabValue} index={3}>
              <Box className="fade-in-up">
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                  <Box
                    sx={{
                      width: 40,
                      height: 40,
                      borderRadius: '50%',
                      bgcolor: 'rgba(37, 99, 235, 0.1)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 2,
                    }}
                  >
                    <SettingsIcon color="primary" />
                  </Box>
                  <Typography variant="h6" fontWeight="bold">
                    إعدادات الحساب
                  </Typography>
                </Box>

                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <Paper
                      sx={{
                        p: 3,
                        borderRadius: 2,
                        boxShadow: 'none',
                        border: '1px solid rgba(0,0,0,0.08)',
                        mb: 3,
                      }}
                    >
                      <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                        تغيير كلمة المرور
                      </Typography>
                      <Grid container spacing={3}>
                        <Grid item xs={12}>
                          <TextField
                            fullWidth
                            label="كلمة المرور الحالية"
                            type="password"
                            variant="outlined"
                            size="small"
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="كلمة المرور الجديدة"
                            type="password"
                            variant="outlined"
                            size="small"
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            fullWidth
                            label="تأكيد كلمة المرور الجديدة"
                            type="password"
                            variant="outlined"
                            size="small"
                          />
                        </Grid>
                        <Grid item xs={12}>
                          <Button
                            variant="contained"
                            sx={{
                              mt: 1,
                              boxShadow: 'none',
                              '&:hover': { boxShadow: '0 4px 8px rgba(0,0,0,0.1)' },
                            }}
                          >
                            تغيير كلمة المرور
                          </Button>
                        </Grid>
                      </Grid>
                    </Paper>
                  </Grid>

                  <Grid item xs={12}>
                    <Paper
                      sx={{
                        p: 3,
                        borderRadius: 2,
                        boxShadow: 'none',
                        border: '1px solid rgba(0,0,0,0.08)',
                        mb: 3,
                      }}
                    >
                      <Typography variant="subtitle1" fontWeight="bold" gutterBottom>
                        إعدادات الإشعارات
                      </Typography>
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          اختر أنواع الإشعارات التي ترغب في استلامها
                        </Typography>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 1, borderRadius: 1, '&:hover': { bgcolor: 'rgba(0,0,0,0.02)' } }}>
                            <Typography variant="body1">إشعارات المستندات الجديدة</Typography>
                            <Button variant="outlined" size="small" color="primary">مفعل</Button>
                          </Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 1, borderRadius: 1, '&:hover': { bgcolor: 'rgba(0,0,0,0.02)' } }}>
                            <Typography variant="body1">إشعارات التحديثات</Typography>
                            <Button variant="outlined" size="small" color="primary">مفعل</Button>
                          </Box>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 1, borderRadius: 1, '&:hover': { bgcolor: 'rgba(0,0,0,0.02)' } }}>
                            <Typography variant="body1">إشعارات الاشتراك</Typography>
                            <Button variant="outlined" size="small" color="primary">مفعل</Button>
                          </Box>
                        </Box>
                      </Box>
                    </Paper>
                  </Grid>

                  <Grid item xs={12}>
                    <Paper
                      sx={{
                        p: 3,
                        borderRadius: 2,
                        boxShadow: 'none',
                        border: '1px solid rgba(255,0,0,0.1)',
                        bgcolor: 'rgba(255,0,0,0.02)',
                      }}
                    >
                      <Typography variant="subtitle1" fontWeight="bold" gutterBottom color="error">
                        حذف الحساب
                      </Typography>
                      <Typography variant="body2" paragraph>
                        سيؤدي حذف حسابك إلى إزالة جميع بياناتك ومستنداتك بشكل نهائي. هذا الإجراء لا يمكن التراجع عنه.
                      </Typography>
                      <Button
                        variant="outlined"
                        color="error"
                        sx={{
                          borderWidth: 1.5,
                          '&:hover': {
                            borderWidth: 1.5,
                            bgcolor: 'rgba(255,0,0,0.05)',
                          },
                        }}
                      >
                        حذف الحساب
                      </Button>
                    </Paper>
                  </Grid>
                </Grid>
              </Box>
            </TabPanel>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default ProfilePage;
