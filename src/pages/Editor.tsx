import { useState, useEffect } from 'react';
import { usePara<PERSON>, Link, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import Paper from '@mui/material/Paper';
import Grid from '@mui/material/Grid';
import TextField from '@mui/material/TextField';
import Button from '@mui/material/Button';
import Divider from '@mui/material/Divider';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import IconButton from '@mui/material/IconButton';
import SaveIcon from '@mui/icons-material/Save';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import ImageIcon from '@mui/icons-material/Image';
import ShareIcon from '@mui/icons-material/Share';
import FormatBoldIcon from '@mui/icons-material/FormatBold';
import FormatItalicIcon from '@mui/icons-material/FormatItalic';
import FormatUnderlinedIcon from '@mui/icons-material/FormatUnderlined';
import FormatAlignRightIcon from '@mui/icons-material/FormatAlignRight';
import FormatAlignCenterIcon from '@mui/icons-material/FormatAlignCenter';
import FormatAlignLeftIcon from '@mui/icons-material/FormatAlignLeft';
import AddPhotoAlternateIcon from '@mui/icons-material/AddPhotoAlternate';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import SchoolIcon from '@mui/icons-material/School';
import TemplateContent from '../components/TemplateContent';
import ExportIntegration from '../components/ExportIntegration';

// استيراد مكونات القوالب الجديدة
import {
  // خطة تطوير مهني
  ProfessionalDevelopmentHeader,
  SelfAssessmentSection,
  DevelopmentGoalsSection,
  ActionPlanSection,
  ResourcesSection,
  TimelineSection,
  EvaluationSection,

  // تقرير إنجاز فصلي
  ClassAchievementHeader,
  PlannedGoalsSection,
  AchievementsSection,
  ChallengesSection,
  StatisticsSection,
  FuturePlansSection,
  AttachmentsSection,

  // تقرير زيارة إشرافية
  SupervisoryVisitHeader,
  TeacherInfoSection,
  LessonInfoSection,
  PerformanceEvaluationSection,
  StrengthsSection,
  ImprovementsSection,
  RecommendationsSection
} from '../components/templates';

// تعريف أنواع النماذج المختلفة
interface TemplateModel {
  id: number;
  name: string;
  imageCount: number;
  color: string;
  previewImage: string;
}

// تعريف القالب
interface Template {
  id: number;
  title: string;
  sections: {
    id: string;
    title: string;
    content: string;
  }[];
  model?: TemplateModel;
}

// بيانات النماذج المختلفة للقوالب
const templateModels: TemplateModel[] = [
  {
    id: 1,
    name: 'نموذج 1',
    imageCount: 1,
    color: 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)',
    previewImage: '/images/templates/template-1.svg'
  },
  {
    id: 2,
    name: 'نموذج 2',
    imageCount: 2,
    color: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
    previewImage: '/images/templates/template-2.svg'
  },
  {
    id: 3,
    name: 'نموذج 3',
    imageCount: 3,
    color: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
    previewImage: '/images/templates/template-3.svg'
  },
  {
    id: 4,
    name: 'نموذج 4',
    imageCount: 4,
    color: 'linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%)',
    previewImage: '/images/templates/template-1.svg'
  },
  {
    id: 5,
    name: 'نموذج 5',
    imageCount: 5,
    color: 'linear-gradient(135deg, #ec4899 0%, #be185d 100%)',
    previewImage: '/images/templates/template-2.svg'
  },
  {
    id: 6,
    name: 'نموذج 6',
    imageCount: 6,
    color: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',
    previewImage: '/images/templates/template-3.svg'
  },
];

// Mock template data
const templateData = [
  {
    id: 1,
    title: 'تقرير نشاط طلابي',
    sections: [
      { id: 'header', title: 'ترويسة التقرير', content: '' },
      { id: 'info', title: 'معلومات إدارية', content: '' },
      { id: 'content', title: 'محتوى التقرير', content: '' },
      { id: 'documentation', title: 'قسم التوثيق', content: '' },
    ]
  },
  {
    id: 2,
    title: 'ملف إنجاز معلم',
    sections: [
      { id: 'header', title: 'البيانات الشخصية', content: '' },
      { id: 'qualifications', title: 'المؤهلات العلمية', content: '' },
      { id: 'experience', title: 'الخبرات العملية', content: '' },
      { id: 'achievements', title: 'الإنجازات والمبادرات', content: '' },
      { id: 'training', title: 'الدورات التدريبية', content: '' },
      { id: 'documentation', title: 'الشهادات والوثائق', content: '' },
    ]
  },
  {
    id: 3,
    title: 'خطة درس',
    sections: [
      { id: 'header', title: 'معلومات الدرس الأساسية', content: '' },
      { id: 'objectives', title: 'الأهداف التعليمية', content: '' },
      { id: 'materials', title: 'المواد والموارد', content: '' },
      { id: 'procedure', title: 'إجراءات التدريس', content: '' },
      { id: 'assessment', title: 'التقييم', content: '' },
      { id: 'reflection', title: 'التأمل والملاحظات', content: '' },
    ]
  },
  {
    id: 4,
    title: 'تقرير زيارة إشرافية',
    sections: [
      { id: 'header', title: 'معلومات الزيارة', content: '' },
      { id: 'teacher_info', title: 'بيانات المعلم', content: '' },
      { id: 'lesson_info', title: 'معلومات الدرس', content: '' },
      { id: 'evaluation', title: 'تقييم الأداء', content: '' },
      { id: 'strengths', title: 'نقاط القوة', content: '' },
      { id: 'improvements', title: 'مجالات التحسين', content: '' },
      { id: 'recommendations', title: 'التوصيات', content: '' },
    ]
  },
  {
    id: 5,
    title: 'تقرير إنجاز فصلي',
    sections: [
      { id: 'header', title: 'معلومات أساسية', content: '' },
      { id: 'goals', title: 'الأهداف المخططة', content: '' },
      { id: 'achievements', title: 'الإنجازات المحققة', content: '' },
      { id: 'challenges', title: 'التحديات', content: '' },
      { id: 'statistics', title: 'إحصائيات ومؤشرات', content: '' },
      { id: 'future_plans', title: 'الخطط المستقبلية', content: '' },
      { id: 'attachments', title: 'المرفقات والوثائق', content: '' },
    ]
  },
  {
    id: 6,
    title: 'خطة تطوير مهني',
    sections: [
      { id: 'header', title: 'المعلومات الشخصية', content: '' },
      { id: 'self_assessment', title: 'التقييم الذاتي', content: '' },
      { id: 'goals', title: 'الأهداف التطويرية', content: '' },
      { id: 'action_plan', title: 'خطة العمل', content: '' },
      { id: 'resources', title: 'الموارد المطلوبة', content: '' },
      { id: 'timeline', title: 'الجدول الزمني', content: '' },
      { id: 'evaluation', title: 'آلية التقييم والمتابعة', content: '' },
    ]
  },
];

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`editor-tabpanel-${index}`}
      aria-labelledby={`editor-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const EditorPage = () => {
  const { t } = useTranslation();
  const { id } = useParams<{ id: string }>();
  const location = useLocation();

  const [template, setTemplate] = useState<Template | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [documentTitle, setDocumentTitle] = useState('');
  const [exportDialogOpen, setExportDialogOpen] = useState(false);

  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    const modelId = Number(queryParams.get('model')) || 1;

    const selectedTemplate = templateData.find(t => t.id === Number(id));
    const selectedModel = templateModels.find(m => m.id === modelId);

    if (selectedTemplate && selectedModel) {
      const templateWithModel = {
        ...selectedTemplate,
        model: selectedModel
      };
      setTemplate(templateWithModel);
      setDocumentTitle(selectedTemplate.title);
    }
  }, [id, location.search]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleTitleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setDocumentTitle(event.target.value);
  };

  if (!template) {
    return (
      <Container maxWidth="lg">
        <Typography variant="h5" align="center" sx={{ mt: 8 }}>
          جاري تحميل القالب...
        </Typography>
      </Container>
    );
  }

  const renderHeaderSection = () => {
    switch (template.id) {
      case 1:
        return <StudentActivityHeader />;
      case 2:
        return <TeacherAchievementHeader />;
      case 3:
        return <LessonPlanHeader />;
      case 4:
        return <SupervisoryVisitHeader />;
      case 5:
        return <ClassAchievementHeader />;
      case 6:
        return <ProfessionalDevelopmentHeader />;
      default:
        return null;
    }
  };

  return (
    <Container maxWidth="lg">
      {/* زر الرجوع */}
      <Box sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
        <Button
          component={Link}
          to={`/template-selector/${id}`}
          startIcon={<ArrowBackIcon />}
          sx={{
            color: 'text.secondary',
            '&:hover': {
              bgcolor: 'rgba(0,0,0,0.04)',
              color: 'primary.main',
            },
            transition: 'all 0.2s',
            fontWeight: 500,
          }}
        >
          العودة إلى اختيار النموذج
        </Button>
      </Box>

      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <TextField
            variant="standard"
            value={documentTitle}
            onChange={handleTitleChange}
            sx={{
              '& .MuiInputBase-input': {
                fontSize: '1.5rem',
                fontWeight: 'bold',
              }
            }}
          />
          {template?.model && (
            <Typography variant="subtitle1" sx={{ ml: 2, color: 'text.secondary' }}>
              {template.model.name} - {template.model.imageCount} {template.model.imageCount === 1 ? 'صورة' : 'صور'}
            </Typography>
          )}
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            sx={{
              bgcolor: 'primary.main',
              '&:hover': { bgcolor: 'primary.dark' },
              transition: 'all 0.2s',
              boxShadow: '0 4px 8px rgba(0,0,0,0.1)',
            }}
          >
            {t('editor.save')}
          </Button>
          <Button
            variant="outlined"
            startIcon={<PictureAsPdfIcon />}
            sx={{
              borderWidth: 2,
              '&:hover': { borderWidth: 2, bgcolor: 'rgba(37, 99, 235, 0.04)' },
              transition: 'all 0.2s',
            }}
          >
            {t('editor.export.pdf')}
          </Button>
          <Button
            variant="outlined"
            startIcon={<ImageIcon />}
            sx={{
              borderWidth: 2,
              '&:hover': { borderWidth: 2, bgcolor: 'rgba(37, 99, 235, 0.04)' },
              transition: 'all 0.2s',
            }}
          >
            {t('editor.export.image')}
          </Button>
          <Button
            variant="outlined"
            startIcon={<SchoolIcon />}
            sx={{
              borderWidth: 2,
              transition: 'all 0.2s',
              color: 'success.main',
              borderColor: 'success.main',
              '&:hover': { 
                borderColor: 'success.dark', 
                bgcolor: 'rgba(46, 125, 50, 0.04)' 
              },
            }}
            onClick={() => setExportDialogOpen(true)}
          >
            تصدير إلى الأنظمة
          </Button>
          <Button
            variant="outlined"
            startIcon={<ShareIcon />}
            sx={{
              borderWidth: 2,
              '&:hover': { borderWidth: 2, bgcolor: 'rgba(37, 99, 235, 0.04)' },
              transition: 'all 0.2s',
            }}
          >
            {t('editor.share')}
          </Button>
        </Box>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12} md={3}>
          <Paper
            sx={{
              p: 2,
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              position: 'sticky',
              top: 90,
              maxHeight: 'calc(100vh - 100px)',
              overflowY: 'auto',
            }}
            className="fade-in"
          >
            <Typography variant="h6" gutterBottom fontWeight="bold" sx={{ mb: 2 }}>
              أقسام المستند
            </Typography>
            <Tabs
              orientation="vertical"
              value={activeTab}
              onChange={handleTabChange}
              sx={{
                borderRight: 0,
                '& .MuiTab-root': {
                  alignItems: 'flex-start',
                  textAlign: 'right',
                  borderRadius: '8px',
                  mb: 0.5,
                  py: 1.5,
                  transition: 'all 0.2s',
                  '&.Mui-selected': {
                    bgcolor: 'rgba(37, 99, 235, 0.08)',
                    color: 'primary.main',
                    fontWeight: 'bold',
                  },
                  '&:hover': {
                    bgcolor: 'rgba(37, 99, 235, 0.04)',
                  }
                }
              }}
            >
              {template.sections.map((section) => (
                <Tab key={section.id} label={section.title} />
              ))}
            </Tabs>
          </Paper>
        </Grid>

        <Grid item xs={12} md={9}>
          <Paper
            sx={{
              p: 3,
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              mb: 4,
            }}
            className="fade-in"
          >
            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3, pb: 2 }}>
              <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                تنسيق النص
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Button variant="outlined" size="small" sx={{ minWidth: 'auto', p: 1 }}>
                  <FormatBoldIcon />
                </Button>
                <Button variant="outlined" size="small" sx={{ minWidth: 'auto', p: 1 }}>
                  <FormatItalicIcon />
                </Button>
                <Button variant="outlined" size="small" sx={{ minWidth: 'auto', p: 1 }}>
                  <FormatUnderlinedIcon />
                </Button>
                <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />
                <Button variant="outlined" size="small" sx={{ minWidth: 'auto', p: 1 }}>
                  <FormatAlignRightIcon />
                </Button>
                <Button variant="outlined" size="small" sx={{ minWidth: 'auto', p: 1 }}>
                  <FormatAlignCenterIcon />
                </Button>
                <Button variant="outlined" size="small" sx={{ minWidth: 'auto', p: 1 }}>
                  <FormatAlignLeftIcon />
                </Button>
                <Divider orientation="vertical" flexItem sx={{ mx: 1 }} />
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<AddPhotoAlternateIcon />}
                  sx={{ ml: 'auto' }}
                >
                  إضافة صورة
                </Button>
              </Box>
            </Box>

            {template.sections.map((section, index) => (
              <TabPanel key={section.id} value={activeTab} index={index}>
                <Box className="fade-in-up">
                  <Typography variant="h6" gutterBottom fontWeight="bold" color="primary.main" sx={{ mb: 3 }}>
                    {section.title}
                  </Typography>

                  {section.id === 'header' && renderHeaderSection()}

                  {/* مكونات مشتركة */}
                  {section.id === 'info' && template.id === 1 && <InfoSection1 />}
                  {section.id === 'info' && template.id === 2 && <InfoSection2 />}
                  {section.id === 'content' && template.id === 1 && <ContentSection1 />}
                  {section.id === 'content' && template.id === 2 && <ContentSection2 />}
                  {section.id === 'qualifications' && <QualificationSection />}
                  {section.id === 'experience' && <ExperienceSection />}
                  {section.id === 'achievements' && template.id === 2 && <AchievementSection />}
                  {section.id === 'training' && <TrainingSection />}
                  {section.id === 'documentation' && template.id === 1 && <DocumentationSection1 />}
                  {section.id === 'documentation' && template.id === 2 && <DocumentationSection2 />}

                  {/* مكونات تقرير زيارة إشرافية */}
                  {section.id === 'teacher_info' && template.id === 4 && <TeacherInfoSection />}
                  {section.id === 'lesson_info' && template.id === 4 && <LessonInfoSection />}
                  {section.id === 'evaluation' && template.id === 4 && <PerformanceEvaluationSection />}
                  {section.id === 'strengths' && template.id === 4 && <StrengthsSection />}
                  {section.id === 'improvements' && template.id === 4 && <ImprovementsSection />}
                  {section.id === 'recommendations' && template.id === 4 && <RecommendationsSection />}

                  {/* مكونات تقرير إنجاز فصلي */}
                  {section.id === 'goals' && template.id === 5 && <PlannedGoalsSection />}
                  {section.id === 'achievements' && template.id === 5 && <AchievementsSection />}
                  {section.id === 'challenges' && template.id === 5 && <ChallengesSection />}
                  {section.id === 'statistics' && template.id === 5 && <StatisticsSection />}
                  {section.id === 'future_plans' && template.id === 5 && <FuturePlansSection />}
                  {section.id === 'attachments' && template.id === 5 && <AttachmentsSection />}

                  {/* مكونات خطة تطوير مهني */}
                  {section.id === 'self_assessment' && template.id === 6 && <SelfAssessmentSection />}
                  {section.id === 'goals' && template.id === 6 && <DevelopmentGoalsSection />}
                  {section.id === 'action_plan' && template.id === 6 && <ActionPlanSection />}
                  {section.id === 'resources' && template.id === 6 && <ResourcesSection />}
                  {section.id === 'timeline' && template.id === 6 && <TimelineSection />}
                  {section.id === 'evaluation' && template.id === 6 && <EvaluationSection />}
                </Box>
              </TabPanel>
            ))}
          </Paper>
        </Grid>
      </Grid>

      {/* مكون التصدير إلى منصة مدرستي ونظام نور */}
      <ExportIntegration
        open={exportDialogOpen}
        onClose={() => setExportDialogOpen(false)}
        documentTitle={documentTitle}
        documentType={template?.title || ''}
        documentContent="محتوى المستند الذي سيتم تصديره" // يمكن استبداله بالمحتوى الفعلي للمستند
      />
    </Container>
  );
};

// تعريف كل قسم كمكون مستقل
export const StudentActivityHeader = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3, bgcolor: 'rgba(37, 99, 235, 0.02)' }}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          معلومات المدرسة / الجهة
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <TextField fullWidth label="اسم المدرسة / الجهة" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="المنطقة التعليمية" variant="outlined" />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="العام الدراسي" variant="outlined" />
          </Grid>
        </Grid>
      </Paper>
    </Grid>
  </Grid>
);

export const TeacherAchievementHeader = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3, bgcolor: 'rgba(37, 99, 235, 0.02)' }}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          البيانات الشخصية
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="الاسم" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="التخصص" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="المسمى الوظيفي" variant="outlined" />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="سنوات الخبرة" variant="outlined" />
          </Grid>
        </Grid>
      </Paper>
    </Grid>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3, bgcolor: 'rgba(37, 99, 235, 0.02)' }}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          معلومات المدرسة
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="اسم المدرسة" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="المنطقة التعليمية" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="تاريخ البدء"
              type="date"
              InputLabelProps={{ shrink: true }}
              variant="outlined"
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="تاريخ الانتهاء"
              type="date"
              InputLabelProps={{ shrink: true }}
              variant="outlined"
            />
          </Grid>
        </Grid>
      </Paper>
    </Grid>
  </Grid>
);

export const LessonPlanHeader = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3, bgcolor: 'rgba(37, 99, 235, 0.02)' }}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          معلومات الدرس الأساسية
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="عنوان الدرس" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="المادة" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="الصف" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="التاريخ"
              type="date"
              InputLabelProps={{ shrink: true }}
              variant="outlined"
              sx={{ mb: 2 }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="المدة الزمنية" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="اسم المعلم" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
        </Grid>
      </Paper>
    </Grid>
  </Grid>
);

// تم نقل المكونات إلى ملفات منفصلة في مجلد templates

// تعريف الأقسام الأخرى
export const InfoSection1 = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3, bgcolor: 'rgba(37, 99, 235, 0.02)' }}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          معلومات إدارية للنشاط
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="اسم النشاط" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="نوع النشاط" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="تاريخ النشاط"
              type="date"
              InputLabelProps={{ shrink: true }}
              variant="outlined"
              sx={{ mb: 2 }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="مكان النشاط" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="المسؤول عن النشاط" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="عدد المشاركين" variant="outlined" sx={{ mb: 2 }} type="number" />
          </Grid>
        </Grid>
      </Paper>
    </Grid>
  </Grid>
);

export const InfoSection2 = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3, bgcolor: 'rgba(37, 99, 235, 0.02)' }}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          معلومات إدارية للمعلم
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="الرقم الوظيفي" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="الإدارة التعليمية" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="المرحلة التعليمية" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="التخصص" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
        </Grid>
      </Paper>
    </Grid>
  </Grid>
);

export const ContentSection1 = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          محتوى تقرير النشاط
        </Typography>
        <TextField
          fullWidth
          label="أهداف النشاط"
          variant="outlined"
          multiline
          rows={3}
          sx={{ mb: 3 }}
        />
        <TextField
          fullWidth
          label="وصف النشاط"
          variant="outlined"
          multiline
          rows={4}
          sx={{ mb: 3 }}
        />
        <TextField
          fullWidth
          label="نتائج النشاط"
          variant="outlined"
          multiline
          rows={3}
          sx={{ mb: 3 }}
        />
        <TextField
          fullWidth
          label="التوصيات"
          variant="outlined"
          multiline
          rows={3}
        />
      </Paper>
    </Grid>
  </Grid>
);

export const ContentSection2 = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          محتوى ملف الإنجاز
        </Typography>
        <TextField
          fullWidth
          label="نبذة شخصية"
          variant="outlined"
          multiline
          rows={3}
          sx={{ mb: 3 }}
        />
        <TextField
          fullWidth
          label="الرؤية المهنية"
          variant="outlined"
          multiline
          rows={3}
          sx={{ mb: 3 }}
        />
        <TextField
          fullWidth
          label="الفلسفة التعليمية"
          variant="outlined"
          multiline
          rows={4}
          sx={{ mb: 3 }}
        />
      </Paper>
    </Grid>
  </Grid>
);

export const QualificationSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          المؤهلات العلمية
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="المؤهل" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="التخصص" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="الجامعة" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="سنة التخرج" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="معلومات إضافية"
              variant="outlined"
              multiline
              rows={2}
              sx={{ mb: 2 }}
            />
          </Grid>
        </Grid>
        <Button variant="outlined" sx={{ mt: 2 }}>
          إضافة مؤهل آخر
        </Button>
      </Paper>
    </Grid>
  </Grid>
);

export const ExperienceSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          الخبرات العملية
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="المسمى الوظيفي" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="جهة العمل" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="تاريخ البدء"
              type="date"
              InputLabelProps={{ shrink: true }}
              variant="outlined"
              sx={{ mb: 2 }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="تاريخ الانتهاء"
              type="date"
              InputLabelProps={{ shrink: true }}
              variant="outlined"
              sx={{ mb: 2 }}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="وصف المهام"
              variant="outlined"
              multiline
              rows={3}
              sx={{ mb: 2 }}
            />
          </Grid>
        </Grid>
        <Button variant="outlined" sx={{ mt: 2 }}>
          إضافة خبرة أخرى
        </Button>
      </Paper>
    </Grid>
  </Grid>
);

export const AchievementSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          الإنجازات والمبادرات
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="عنوان الإنجاز" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="تاريخ الإنجاز"
              type="date"
              InputLabelProps={{ shrink: true }}
              variant="outlined"
              sx={{ mb: 2 }}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="وصف الإنجاز"
              variant="outlined"
              multiline
              rows={3}
              sx={{ mb: 2 }}
            />
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="النتائج والأثر"
              variant="outlined"
              multiline
              rows={2}
              sx={{ mb: 2 }}
            />
          </Grid>
        </Grid>
        <Button variant="outlined" sx={{ mt: 2 }}>
          إضافة إنجاز آخر
        </Button>
      </Paper>
    </Grid>
  </Grid>
);

export const TrainingSection = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          الدورات التدريبية
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="اسم الدورة" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="الجهة المنظمة" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="تاريخ البدء"
              type="date"
              InputLabelProps={{ shrink: true }}
              variant="outlined"
              sx={{ mb: 2 }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="تاريخ الانتهاء"
              type="date"
              InputLabelProps={{ shrink: true }}
              variant="outlined"
              sx={{ mb: 2 }}
            />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="عدد الساعات" variant="outlined" sx={{ mb: 2 }} type="number" />
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="مكان الانعقاد" variant="outlined" sx={{ mb: 2 }} />
          </Grid>
        </Grid>
        <Button variant="outlined" sx={{ mt: 2 }}>
          إضافة دورة أخرى
        </Button>
      </Paper>
    </Grid>
  </Grid>
);

export const DocumentationSection1 = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          توثيق النشاط
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper
              elevation={0}
              sx={{
                height: 200,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                border: '2px dashed rgba(0,0,0,0.1)',
                borderRadius: 2,
                p: 2,
                cursor: 'pointer',
                transition: 'all 0.2s',
                '&:hover': {
                  borderColor: 'primary.main',
                  bgcolor: 'rgba(0,0,0,0.02)'
                },
                mb: 3
              }}
            >
              <AddPhotoAlternateIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 1 }} />
              <Typography variant="body2" color="text.secondary">
                انقر لإضافة صور توثيقية للنشاط
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="ملاحظات التوثيق"
              variant="outlined"
              multiline
              rows={3}
              sx={{ mb: 2 }}
            />
          </Grid>
        </Grid>
      </Paper>
    </Grid>
  </Grid>
);

export const DocumentationSection2 = () => (
  <Grid container spacing={3}>
    <Grid item xs={12}>
      <Paper sx={{ p: 3, borderRadius: 2, mb: 3 }}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          الشهادات والوثائق
        </Typography>
        <Grid container spacing={3}>
          <Grid item xs={12} sm={6}>
            <Paper
              elevation={0}
              sx={{
                height: 200,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                border: '2px dashed rgba(0,0,0,0.1)',
                borderRadius: 2,
                p: 2,
                cursor: 'pointer',
                transition: 'all 0.2s',
                '&:hover': {
                  borderColor: 'primary.main',
                  bgcolor: 'rgba(0,0,0,0.02)'
                },
                mb: 3
              }}
            >
              <AddPhotoAlternateIcon sx={{ fontSize: 40, color: 'text.secondary', mb: 1 }} />
              <Typography variant="body2" color="text.secondary">
                انقر لإضافة صورة الشهادة
              </Typography>
            </Paper>
          </Grid>
          <Grid item xs={12} sm={6}>
            <TextField fullWidth label="اسم الشهادة" variant="outlined" sx={{ mb: 2 }} />
            <TextField fullWidth label="الجهة المانحة" variant="outlined" sx={{ mb: 2 }} />
            <TextField
              fullWidth
              label="تاريخ الحصول عليها"
              type="date"
              InputLabelProps={{ shrink: true }}
              variant="outlined"
              sx={{ mb: 2 }}
            />
          </Grid>
        </Grid>
        <Button variant="outlined" sx={{ mt: 2 }}>
          إضافة شهادة أخرى
        </Button>
      </Paper>
    </Grid>
  </Grid>
);

export default EditorPage;