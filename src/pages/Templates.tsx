import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import CardMedia from '@mui/material/CardMedia';
import CardActions from '@mui/material/CardActions';
import Button from '@mui/material/Button';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import SearchIcon from '@mui/icons-material/Search';

// Mock template data with color backgrounds instead of images
const getTemplateColor = (category: string) => {
  switch (category) {
    case 'reports':
      return 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)';
    case 'portfolios':
      return 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)';
    case 'lessons':
      return 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
    case 'admin':
      return 'linear-gradient(135deg, #8b5cf6 0%, #6d28d9 100%)';
    default:
      return 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)';
  }
};

const getTemplateIcon = (category: string) => {
  switch (category) {
    case 'reports':
      return '📊';
    case 'portfolios':
      return '📁';
    case 'lessons':
      return '📝';
    case 'admin':
      return '📋';
    default:
      return '📄';
  }
};

const templateData = [
  {
    id: 1,
    title: 'تقرير نشاط طلابي',
    description: 'قالب احترافي لتوثيق الأنشطة الطلابية بتنسيق جذاب',
    category: 'reports',
  },
  {
    id: 2,
    title: 'ملف إنجاز معلم',
    description: 'قالب شامل لتوثيق إنجازات المعلم خلال العام الدراسي',
    category: 'portfolios',
  },
  {
    id: 3,
    title: 'خطة درس',
    description: 'قالب منظم لإعداد خطط الدروس اليومية والأسبوعية',
    category: 'lessons',
  },
  {
    id: 4,
    title: 'تقرير زيارة إشرافية',
    description: 'قالب لتوثيق الزيارات الإشرافية للمعلمين',
    category: 'admin',
  },
  {
    id: 5,
    title: 'تقرير إنجاز فصلي',
    description: 'قالب لتوثيق إنجازات الفصل الدراسي',
    category: 'reports',
  },
  {
    id: 6,
    title: 'خطة تطوير مهني',
    description: 'قالب لإعداد خطة التطوير المهني للمعلم',
    category: 'portfolios',
  },
];

const TemplatesPage = () => {
  const { t } = useTranslation();
  const [category, setCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  const handleCategoryChange = (event: React.SyntheticEvent, newValue: string) => {
    setCategory(newValue);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  const filteredTemplates = templateData.filter((template) => {
    const matchesCategory = category === 'all' || template.category === category;
    const matchesSearch = template.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                          template.description.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <Container maxWidth="lg" sx={{ pb: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
        {t('templates.title')}
      </Typography>

      <Box sx={{ mb: 4, position: 'sticky', top: 80, zIndex: 10, bgcolor: 'background.default', py: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={8}>
            <Tabs
              value={category}
              onChange={handleCategoryChange}
              variant="scrollable"
              scrollButtons="auto"
              allowScrollButtonsMobile
            >
              <Tab label={t('templates.filter.all')} value="all" />
              <Tab label={t('templates.filter.reports')} value="reports" />
              <Tab label={t('templates.filter.portfolios')} value="portfolios" />
              <Tab label={t('templates.filter.lessons')} value="lessons" />
              <Tab label={t('templates.filter.admin')} value="admin" />
            </Tabs>
          </Grid>
          <Grid item xs={12} md={4}>
            <TextField
              fullWidth
              placeholder="بحث عن قالب..."
              variant="outlined"
              value={searchQuery}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
        </Grid>
      </Box>

      <Grid container spacing={3}>
        {filteredTemplates.map((template) => (
          <Grid item xs={12} sm={6} md={4} key={template.id}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: 'transform 0.3s, box-shadow 0.3s',
                '&:hover': {
                  transform: 'translateY(-8px)',
                  boxShadow: '0 12px 20px rgba(0,0,0,0.1)',
                },
              }}
              className="hover-scale"
            >
              <Box
                sx={{
                  height: 180,
                  background: getTemplateColor(template.category),
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  position: 'relative',
                  overflow: 'hidden',
                }}
              >
                <Box
                  sx={{
                    position: 'absolute',
                    top: -20,
                    right: -20,
                    width: 100,
                    height: 100,
                    borderRadius: '50%',
                    bgcolor: 'rgba(255,255,255,0.1)',
                  }}
                />
                <Box
                  sx={{
                    position: 'absolute',
                    bottom: -30,
                    left: -30,
                    width: 120,
                    height: 120,
                    borderRadius: '50%',
                    bgcolor: 'rgba(255,255,255,0.05)',
                  }}
                />
                <Typography variant="h1" sx={{ fontSize: 64, fontWeight: 'normal' }}>
                  {getTemplateIcon(template.category)}
                </Typography>
              </Box>
              <CardContent sx={{ flexGrow: 1, pt: 3 }}>
                <Typography gutterBottom variant="h6" component="h2" fontWeight="bold">
                  {template.title}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {template.description}
                </Typography>
              </CardContent>
              <CardActions sx={{ p: 2, pt: 0 }}>
                <Button
                  component={Link}
                  to={`/template-selector/${template.id}`}
                  variant="contained"
                  fullWidth
                  sx={{
                    py: 1,
                    borderRadius: 2,
                    boxShadow: '0 4px 10px rgba(0,0,0,0.1)',
                  }}
                >
                  {t('templates.use')}
                </Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>

      {filteredTemplates.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <Typography variant="h6" color="text.secondary">
            لا توجد قوالب مطابقة لبحثك
          </Typography>
        </Box>
      )}
    </Container>
  );
};

export default TemplatesPage;
