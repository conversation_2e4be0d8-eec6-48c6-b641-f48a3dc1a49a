// تعريف الأنيميشن المستخدمة في التطبيق
import { Variants } from 'framer-motion';

// تأثير ظهور العناصر من الأسفل
export const fadeInUp: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.5,
      ease: 'easeOut'
    }
  }
};

// تأثير ظهور العناصر من اليمين (مناسب للغة العربية)
export const fadeInRight: Variants = {
  hidden: { opacity: 0, x: -20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.5,
      ease: 'easeOut'
    }
  }
};

// تأثير ظهور العناصر بشكل تدريجي
export const fadeIn: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      duration: 0.4
    }
  }
};

// تأثير ظهور العناصر بشكل متتابع
export const staggerContainer: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
};

// تأثير نبض للأزرار
export const pulseAnimation: Variants = {
  hover: {
    scale: 1.05,
    transition: {
      duration: 0.3,
      yoyo: Infinity,
      ease: 'easeInOut'
    }
  }
};

// تأثير تكبير بسيط عند التحويم
export const scaleUp: Variants = {
  hover: {
    scale: 1.05,
    transition: {
      duration: 0.2,
      ease: 'easeInOut'
    }
  }
};

// تأثير انتقال الصفحات
export const pageTransition: Variants = {
  initial: { opacity: 0 },
  animate: {
    opacity: 1,
    transition: {
      duration: 0.5
    }
  },
  exit: {
    opacity: 0,
    transition: {
      duration: 0.3
    }
  }
};

// تأثير موجة للرسائل في المحادثة
export const messageWave: Variants = {
  hidden: { opacity: 0, scale: 0.8, y: 10 },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: 'spring',
      stiffness: 400,
      damping: 20
    }
  }
};

// تأثير فقاعة المحادثة
export const chatBubble: Variants = {
  initial: { scale: 0.8, opacity: 0 },
  animate: {
    scale: 1,
    opacity: 1,
    transition: {
      type: 'spring',
      stiffness: 500,
      damping: 25,
      mass: 1
    }
  },
  hover: {
    boxShadow: '0 10px 25px rgba(0,0,0,0.1)',
    y: -2,
    transition: {
      duration: 0.2
    }
  }
};

// تأثير ظهور الاقتراحات
export const suggestionChip: Variants = {
  hidden: { opacity: 0, y: 20, scale: 0.9 },
  visible: (i: number) => ({
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      delay: i * 0.1,
      duration: 0.4,
      ease: 'easeOut'
    }
  }),
  hover: {
    y: -5,
    boxShadow: '0 8px 16px rgba(37, 99, 235, 0.2)',
    transition: {
      duration: 0.2
    }
  }
};

// تأثير ظهور العناصر من اليسار
export const fadeInLeft: Variants = {
  hidden: { opacity: 0, x: 20 },
  visible: {
    opacity: 1,
    x: 0,
    transition: {
      duration: 0.5,
      ease: 'easeOut'
    }
  }
};

// تأثير انزلاق من الأسفل
export const slideInFromBottom: Variants = {
  hidden: { opacity: 0, y: 50 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.8,
      ease: "easeOut"
    }
  }
};

// تأثير ارتداد عند الظهور
export const bounceIn: Variants = {
  hidden: { opacity: 0, scale: 0.3 },
  visible: {
    opacity: 1,
    scale: 1,
    transition: {
      duration: 0.6,
      type: "spring",
      stiffness: 300,
      damping: 20
    }
  }
};

// تأثير دوران خفيف
export const rotateIn: Variants = {
  hidden: { opacity: 0, rotate: -10, scale: 0.9 },
  visible: {
    opacity: 1,
    rotate: 0,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
};

// تأثير تموج للبطاقات
export const cardHover: Variants = {
  hover: {
    y: -10,
    boxShadow: '0 20px 40px rgba(0,0,0,0.1)',
    transition: {
      duration: 0.3,
      ease: 'easeOut'
    }
  }
};

// تأثير نبض مستمر
export const continuousPulse: Variants = {
  animate: {
    scale: [1, 1.05, 1],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: 'easeInOut'
    }
  }
};

// تأثير تحريك الخلفية
export const floatingBackground: Variants = {
  animate: {
    y: [-10, 10, -10],
    transition: {
      duration: 6,
      repeat: Infinity,
      ease: 'easeInOut'
    }
  }
};