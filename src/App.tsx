import { Routes, Route } from 'react-router-dom'
import { ThemeProvider } from '@mui/material/styles'
import CssBaseline from '@mui/material/CssBaseline'
import { theme } from './styles/theme'
import Layout from './components/Layout/Layout'
import HomePage from './pages/Home'
import ChatPage from './pages/Chat'
import TemplatesPage from './pages/Templates'
import TemplateSelectorPage from './pages/TemplateSelector'
import EditorPage from './pages/Editor'
import ProfilePage from './pages/Profile'
import LoginPage from './pages/Auth/Login'
import RegisterPage from './pages/Auth/Register'
import NotFoundPage from './pages/NotFound'
import ScrollToTop from './components/ScrollToTop'
import { AnimatePresence } from 'framer-motion'

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AnimatePresence mode="wait">
        <Routes>
          <Route path="/" element={<Layout />}>
            <Route index element={<HomePage />} />
            <Route path="chat" element={<ChatPage />} />
            <Route path="templates" element={<TemplatesPage />} />
            <Route path="template-selector/:id" element={<TemplateSelectorPage />} />
            <Route path="editor/:id" element={<EditorPage />} />
            <Route path="profile" element={<ProfilePage />} />
          </Route>
          <Route path="/login" element={<LoginPage />} />
          <Route path="/register" element={<RegisterPage />} />
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
      </AnimatePresence>
      <ScrollToTop />
    </ThemeProvider>
  )
}

export default App
